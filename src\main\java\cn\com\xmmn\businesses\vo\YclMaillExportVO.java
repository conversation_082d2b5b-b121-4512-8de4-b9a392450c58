package cn.com.xmmn.businesses.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 应处理邮件数据导出
 */
@Data
@HeadRowHeight(value = 30) //头部行高
@ContentRowHeight(value = 25) //内容行高
@ColumnWidth(value = 20) //列宽
@HeadFontStyle(fontName = "宋体",fontHeightInPoints = 11)
public class YclMaillExportVO implements Serializable {
    private static final long serialVersionUID = 1L;


    @ExcelProperty("客户名称")
    private String custName;

    @ExcelProperty("总包条码")
    private String barCode;

    @ExcelProperty("邮件号码")
    private String itemId;
    @ExcelProperty("原寄局")
    private String originOrgName;


    @ExcelProperty("寄达局")
    private String destOrgName;
    @ExcelProperty("业务种类")
    private String product;
    @ExcelProperty("接收交換站名称")
    private String recvOrgName;
    @ExcelProperty("交换站接收时间")
    private Date recvOrgTime;
    @ExcelProperty("交换站发运时间")
    private Date recvOrgSendTime;
    @ExcelProperty("接收互换站名称")
    private String chOrgName;
    @ExcelProperty("机构代码")
    private String chOrgCode;
    @ExcelProperty("互换局接收时间")
    private Date chOrgRecvTime;


    //接收互换局是否与寄达局一致:0=否1=是
    @ExcelProperty(value = "接收互换局是否与寄达局一致",converter = StatusConverter.class)
    private String chIsEqualDest;

    @ExcelProperty(value = "是否已接收")
    private String isRecv;

    @ExcelProperty("交换站发运-互换局接收时长(h)")
    private Double sendRecvtimeDiff;

    public String getIsRecv() {
        Date chOrgRecvTime = this.getChOrgRecvTime();
        if(null != chOrgRecvTime){
            return "是";
        }
        return "否";
    }
}
