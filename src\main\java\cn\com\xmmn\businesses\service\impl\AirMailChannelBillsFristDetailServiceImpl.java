package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.AirMailChannelBillsFristDetailDao;
import cn.com.xmmn.businesses.domain.AirMailChannelBillsFristDetailDO;
import cn.com.xmmn.businesses.service.AirMailChannelBillsFristDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Service
public class AirMailChannelBillsFristDetailServiceImpl implements AirMailChannelBillsFristDetailService {
	@Autowired
	private AirMailChannelBillsFristDetailDao airMailChannelBillsFristDetailDao;
	
	@Override
	public AirMailChannelBillsFristDetailDO get(Integer id){
		return airMailChannelBillsFristDetailDao.get(id);
	}
	
	@Override
	public List<AirMailChannelBillsFristDetailDO> list(Map<String, Object> map){
		return airMailChannelBillsFristDetailDao.list(map);
	}
	
	@Override
	public int count(Map<String, Object> map){
		return airMailChannelBillsFristDetailDao.count(map);
	}
	
	@Override
	public int save(AirMailChannelBillsFristDetailDO airMailChannelBillsFristDetail){
		return airMailChannelBillsFristDetailDao.save(airMailChannelBillsFristDetail);
	}
	
	@Override
	public int update(AirMailChannelBillsFristDetailDO airMailChannelBillsFristDetail){
		return airMailChannelBillsFristDetailDao.update(airMailChannelBillsFristDetail);
	}
	
	@Override
	public int remove(Integer id){
		return airMailChannelBillsFristDetailDao.remove(id);
	}
	
	@Override
	public int batchRemove(Integer[] ids){
		return airMailChannelBillsFristDetailDao.batchRemove(ids);
	}

	@Override
	public List detail(AirMailChannelBillsFristDetailDO airMailChannelBillsFristDetail){
		return airMailChannelBillsFristDetailDao.detail(airMailChannelBillsFristDetail);
	}

	@Override
	public List detailOfBill(AirMailChannelBillsFristDetailDO airMailChannelBillsFristDetail){
		return airMailChannelBillsFristDetailDao.detailOfBill(airMailChannelBillsFristDetail);
	}
	
}
