package cn.com.xmmn.businesses.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 承运商邮袋启运列表
 * <AUTHOR>
 * @Date 2024/6/4 16:47
 */
@Data
@HeadRowHeight(value = 30) // 头部行高
@ContentRowHeight(value = 25) // 内容行高
@ColumnWidth(value = 20) // 列宽
@HeadFontStyle(fontName = "宋体", fontHeightInPoints = 11) // 头部字体样式
public class CarrierDealerD2ADetailListVo implements Serializable {
    @ExcelProperty(value = "交换站机构名称", index = 0)
    private String orgName;

    @ExcelProperty(value = "承运商", index = 1)
    private String carrierDealer;


    @ExcelProperty(value = "业务种类", index = 2)
    private String product;

    @ExcelProperty(value = "邮袋条码", index = 3)
    private String oeBagBarcode;

    @ExcelProperty(value = "计划航班", index = 4)
    private String flightNumber;

    @ExcelProperty(value = "实际航班", index = 5)
    private String newFlightNo;

    @ExcelProperty(value = "运输方式", index = 6)
    private String transType;

    @ExcelProperty(value = "寄达国家名称", index = 7)
    private String receiverCountryName;

    @ExcelProperty(value = "寄达互换局代码", index = 8)
    private String oeDest;


    @ExcelProperty(value = "寄达互换局名称", index = 9)
    private String oeDestName;


    @ExcelProperty(value = "出口邮件直封封发emc（最新）", index = 10,format = "yyyy-MM-dd HH:mm:ss")
    private Date oeBillStartTime;

    @ExcelProperty(value = "交换站解车时间（最新）", index = 11,format = "yyyy-MM-dd HH:mm:ss")
    private Date opTime352;

    @ExcelProperty(value = "交航时间", index = 12,format = "yyyy-MM-dd HH:mm:ss")
    private Date opTime404;


    @ExcelProperty(value = "承运商接收时间", index = 13,format = "yyyy-MM-dd HH:mm:ss")
    private Date opTime538;

    @ExcelProperty(value = "启运时间", index = 14,format = "yyyy-MM-dd HH:mm:ss")
    private Date opTime457;

    @ExcelProperty(value = "抵达目的地", index = 15,format = "yyyy-MM-dd HH:mm:ss")
    private Date opTime505;

    @ExcelProperty(value = "送交境外邮政", index = 16,format = "yyyy-MM-dd HH:mm:ss")
    private Date opTime507;

    @ExcelProperty(value = "总包到达寄达地resdes", index = 17,format = "yyyy-MM-dd HH:mm:ss")
    private Date opTime516;

    @ExcelProperty(value = "到达境外进口互换局emd", index = 18,format = "yyyy-MM-dd HH:mm:ss")
    private Date opTime459;

    @ExcelProperty(value = "境外进口海关留存待验eme", index = 19,format = "yyyy-MM-dd HH:mm:ss")
    private Date opTime486;

    @ExcelProperty(value = "离开境外进口互换局emf", index = 20,format = "yyyy-MM-dd HH:mm:ss")
    private Date opTime460;

    @ExcelProperty(value = "到达境外投递局emg", index = 21,format = "yyyy-MM-dd HH:mm:ss")
    private Date opTime461;

    @ExcelProperty(value = "境外试投emh", index = 22,format = "yyyy-MM-dd HH:mm:ss")
    private Date opTime462;

    @ExcelProperty(value = "境外妥投emi", index = 23,format = "yyyy-MM-dd HH:mm:ss")
    private Date opTime463;


    @ExcelProperty(value = "启运至运抵时限（天）", index = 24)
    private String proTime;

    @ExcelProperty(value = "总包当前状态", index = 25)
    private String proStatus;

}
