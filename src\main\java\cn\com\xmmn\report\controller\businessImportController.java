package cn.com.xmmn.report.controller;

import cn.com.xmmn.businesses.service.OrdersImportService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * TMS订单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-30 20:15:07
 */
@Slf4j
@Controller
@RequestMapping("/report/businessReport")
public class businessImportController {

    @Autowired
    private OrdersImportService ordersImportService;

    @GetMapping()
    @RequiresPermissions("report:businessReport")
    String init(Model model) {
        //设置初始时间  初始时间为三个月前的第一天，比如今天3月22日，则开始时间设为2023-01，结束时间为2023-03
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DATE, 1);// 设为当前月的1号
        calendar.add(Calendar.MONTH, -2);// 0表示当前月，-2就是当前月-2
        Date d = calendar.getTime();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String startDate = sdf.format(d);
        String endDate = sdf.format(new Date());
        String nowDate = sdf.format(new Date());
        model.addAttribute("startDate", startDate);
        model.addAttribute("endDate", endDate);
        model.addAttribute("nowDate", nowDate);
        model.addAttribute("startDate1", startDate);
        model.addAttribute("endDate1", endDate);
        model.addAttribute("nowDate1", nowDate);
        return "report/businessImport";
    }

    @ResponseBody
    @PostMapping("/allShipway")
    @RequiresPermissions("report:businessReport")
    public Map<String, Object> allShipway(Model model, String startDate, String endDate) {
        Map<String, Object> dataMap = new HashMap<>();
        String startDatestr = startDate + "-01";
        String endDateStr = endDate + "-01";
        log.info(startDatestr + "====" + endDateStr);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("endDateStr", endDateStr);
        map.put("startDateStr", startDatestr);
        List<Map<String, Object>> data = ordersImportService.countShipway(map);
        String result = JSON.toJSONString(data).toString();
        log.info("商业渠道左侧柱折图数据：" + result);
        dataMap.put("result", result);

        //商业渠道右侧环比
        List<String> huanbi = getHuanbiMonth(endDate);//若 end = '2022-11',则返回[2022-10, 2022-11],比较环比升降用的
        map.put("huanbi", huanbi);
        List<Map<String, Object>> huanbiList = ordersImportService.huanbiShipway(map);
        String hbStr = JSON.toJSONString(huanbiList).toString();
        log.info("商业渠道右侧环比数据===" + hbStr);
        dataMap.put("huanbi", hbStr);
        return dataMap;
    }

    @ResponseBody
    @PostMapping("/oneShipWay")
    @RequiresPermissions("report:businessReport")
    public Map<String, Object> oneShipWay(Model model, String nowDate) {
        Map<String, Object> dataMap = new HashMap<>();

        log.info("商业渠道饼图页面时间==" + nowDate);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("nowDateStr", nowDate);
        List<Map<String, Object>> shipWayList = ordersImportService.oneShipway(map);
        dataMap.put("result", shipWayList);
        return dataMap;
    }

    @ResponseBody
    @PostMapping("/allCustomer")
    @RequiresPermissions("report:businessReport")
    public Map<String, Object> allCustomer(Model model, String startDate, String endDate) {
        Map<String, Object> dataMap = new HashMap<>();
        String startDatestr = startDate + "-01";
        String endDateStr = endDate + "-01";
        log.info(startDatestr + "====" + endDateStr);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("endDateStr", endDateStr);
        map.put("startDateStr", startDatestr);
        List<Map<String, Object>> data = ordersImportService.countCustomer(map);
        String result = JSON.toJSONString(data).toString();
        log.info(result);
        dataMap.put("result", result);

        //客户右侧环比
        List<String> huanbi = getHuanbiMonth(endDate);//若 end = '2022-11',则返回[2022-10, 2022-11],比较环比升降用的
        map.put("huanbi", huanbi);
        List<Map<String, Object>> huanbiList = ordersImportService.huanbiCustomer(map);
        String hbStr = JSON.toJSONString(huanbiList).toString();
        log.info("客户环比查询结果===" + hbStr);
        dataMap.put("huanbi", hbStr);
        return dataMap;
    }

    @ResponseBody
    @PostMapping("/oneCustomer")
    @RequiresPermissions("report:businessReport")
    public Map<String, Object> oneCustomer(Model model, String nowDate) {
        Map<String, Object> dataMap = new HashMap<>();

        log.info("客户饼图页面时间" + nowDate);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("nowDateStr", nowDate);
        List<Map<String, Object>> shipWayList = ordersImportService.oneCustomer(map);
        dataMap.put("result", shipWayList);
        return dataMap;
    }

    // 使用参数月份,得到上一个月及参数月，计算环比用
    public List<String> getHuanbiMonth(String end) {
        //若 end = '2022-11',则返回[2022-10, 2022-11]
        List<String> list = new ArrayList<String>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        try {
            date = sdf.parse(end + "-" + "01");
        } catch (ParseException e) {
            e.printStackTrace();
        }

        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MONTH, -1);
        String lastDate = "";
        if (c.get(Calendar.MONTH) < 9) {//做这判断是因为，如果上一个月是10/11/12 则会变成2023-010/011/012
            lastDate = c.get(Calendar.YEAR) + "-0"
                    + (c.get(Calendar.MONTH) + 1);
        } else {
            lastDate = c.get(Calendar.YEAR) + "-"
                    + (c.get(Calendar.MONTH) + 1);
        }

        list.add(lastDate);
        list.add(end);
        return list;
    }

    //使用Calendar的set和add方法，从下个月的第一天计算得到当前月的最后一天,并得到最后一天是28/29/30/31
    public String getLastDayOfMonth(String dateStr) {//参数2023-03，返回结果31

        String year = dateStr.substring(0, 4);
        String month = dateStr.substring(5, 7);
        Calendar cal = Calendar.getInstance();
        //年
        cal.set(Calendar.YEAR, Integer.parseInt(year));
        //月，因为Calendar里的月是从0开始，所以要-1
        cal.set(Calendar.MONTH, Integer.parseInt(month) - 1);
        //日，设为一号
        cal.set(Calendar.DATE, 1);
        //月份加一，得到下个月的一号
        cal.add(Calendar.MONTH, 1);
        //下一个月减一为本月最后一天
        cal.add(Calendar.DATE, -1);
        String monthEnd = String.valueOf(cal.get(Calendar.DAY_OF_MONTH));//获得月末是几号
//        System.out.println(year+month+",获得本月月末:" + monthEnd);
        return monthEnd;
    }

    //获取sql需要分组的参数
    public List<String> getTitle(String begin, String end) {
        //若 begin = '2022-11',end = '2023-03',则返回[2022-11, 2022-12, 2023-01, 2023-02, 2023-03]
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        List<String> list = new ArrayList<String>();
        /*list.add("product"); //Echart显示表头的时候需要用到*/
        Date d1;
        Date d2;
        try {
            d1 = new SimpleDateFormat("yyyy-MM").parse(begin);
            d2 = new SimpleDateFormat("yyy-MM").parse(end);//定义结束日期可以去当前月也可以手动写日期。
            Calendar dd = Calendar.getInstance();//定义日期实例
            dd.setTime(d1);//设置日期起始时间
            while (dd.getTime().before(d2)) {//判断是否到结束日期
                SimpleDateFormat sdf = new SimpleDateFormat("yyy-MM");
                String str = sdf.format(dd.getTime());
                dd.add(Calendar.MONTH, 1);//进行当前日期月份加1
                list.add(str);
            }
            list.add(end);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return list;
    }


}
