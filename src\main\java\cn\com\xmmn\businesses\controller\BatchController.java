package cn.com.xmmn.businesses.controller;

import java.io.UnsupportedEncodingException;
import java.util.*;


import cn.com.xmmn.businesses.domain.CommercialDO;
import cn.com.xmmn.businesses.domain.NewDO;
import cn.com.xmmn.businesses.service.impl.BatchServiceImpl;
import cn.com.xmmn.common.controller.BaseController;
import cn.com.xmmn.common.utils.*;
import cn.com.xmmn.system.domain.UserDO;
import io.swagger.models.auth.In;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import cn.com.xmmn.businesses.domain.BatchDO;
import cn.com.xmmn.businesses.service.BatchService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 导入批次表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-06 13:07:47
 */
 
@Controller
@RequestMapping("/businesses/batch")
public class BatchController extends BaseController {
	@Autowired
	private BatchService batchService;
	@Autowired
	private BatchServiceImpl BatchServiceImpl;
	@GetMapping()
	@RequiresPermissions("businesses:batch:batch")
	String Batch(){
	    return "businesses/batch/batch";
	}
	
	@ResponseBody
	@GetMapping("/list")
	@RequiresPermissions("businesses:batch:batch")
	public PageUtils list(@RequestParam Map<String, Object> params){
		//查询列表数据
        Query query = new Query(params);
		List<BatchDO> batchList = batchService.list(query);
		int total = batchService.count(query);
		PageUtils pageUtils = new PageUtils(batchList, total);
		return pageUtils;
	}
	@ResponseBody
	@GetMapping("/newlist")
	@RequiresPermissions("businesses:batch:batch")
	public PageUtils newlist(@RequestParam Map<String, Object> params){
		//查询列表数据
		Query query = new Query(params);
		System.out.println(query);
		List<BatchDO> batchList = batchService.list(query);
		int total = batchService.count(query);
		PageUtils pageUtils = new PageUtils(batchList, total);
		return pageUtils;
	}
	
	@GetMapping("/add")
	@RequiresPermissions("businesses:batch:add")
	String add(){
	    return "businesses/batch/add";
	}

	/*@GetMapping("/edit/{id}")
	@RequiresPermissions("businesses:batch:edit")
	String edit(@PathVariable("id") Integer id,Model model,String modelType){
		System.out.println(modelType);
		BatchDO batch = batchService.get(id);
		model.addAttribute("batch", batch);
	    return "businesses/batch/edit";
	}*/


@GetMapping("/edit/{id}/{moduleType}")

	String edit(@PathVariable("id") Integer id, Model model,@PathVariable("moduleType") String moduleType){

	 /*if ("1".equals(moduleType)){
			CommercialDO put=new CommercialDO();
		    put.setBatchId(id);
		    List batchid=batchService.detailsCommercial(put);
			model.addAttribute("bacthid", batchid);
			return "businesses/batch/details";
		} else if ("2".equals(moduleType)){
			NewDO batchid=batchService.newCommercial(id);
			model.addAttribute("bacthid", batchid);
			return "businesses/batch/details";
	}*/
	   NewDO put=new NewDO();
	   put.setBatchId(id);
	  List bacthid=batchService.newCommercial(put);
	   System.out.println(bacthid);
	   model.addAttribute("bacthid", bacthid);

	return "businesses/batch/details";

	}
	//导入
	/**
	 *   接受文件   解析  上传资料。
	 *   /admin/client/upload_excel
	 */
	@PostMapping("/uploadExcel")
	public String uploadExcel(MultipartFile file,String modelType,Model model) {
         //判断前台传值modeltype来确认导入的表
		/*if ("1".equals(modelType)){
		//导入商业出口手工表
			int batch=BatchServiceImpl.CommercialExcel(file,modelType);
			model.addAttribute("id",batch);
		}else if ("2".equals(modelType)){
			int batch=BatchServiceImpl.NewExcel(file,modelType);
			model.addAttribute("id",batch);
		}*/

		int batch= 0;
		try {
			batch = BatchServiceImpl.NewExcel(file,modelType);
		} catch (Exception e) {
			e.printStackTrace();
			String msg = "导入失败，请检查导入模板是否有误！"; // 错误原因
			model.addAttribute("message", msg); // 错误信息
			return "businesses/batch/batch";
		}
		model.addAttribute("id",batch);

		return "businesses/batch/batch";

	}


	/**
	 * 下载excel模板
	 */
	@GetMapping("/downloadExcel")
	@ResponseBody
	@RequiresPermissions("businesses:batch:downloadExcel")
	public void downloadExcel(HttpServletResponse response) throws UnsupportedEncodingException {
		BatchServiceImpl.downloadExcel(response);
	}

	/**
	 * 保存
	 */
	@ResponseBody
	@PostMapping("/save")
	@RequiresPermissions("businesses:batch:add")
	public R save( BatchDO batch){
		if(batchService.save(batch)>0){
			return R.ok();
		}
		return R.error();
	}
	/**
	 * 修改
	 */
	@ResponseBody
	@RequestMapping("/update")
	@RequiresPermissions("businesses:batch:edit")
	public R update( BatchDO batch){
		batchService.update(batch);
		return R.ok();
	}
	
	/**
	 * 删除
	 */
	@PostMapping( "/remove")
	@ResponseBody
	@RequiresPermissions("businesses:batch:remove")
	public R remove( Integer id){
		if(batchService.remove(id)>0){
		return R.ok();
		}
		return R.error();
	}
	
	/**
	 * 删除
	 */
	@PostMapping( "/batchRemove")
	@ResponseBody
	@RequiresPermissions("businesses:batch:batchRemove")
	public R remove(@RequestParam("ids[]") Integer[] ids){
		batchService.batchRemove(ids);
		return R.ok();
	}

}
