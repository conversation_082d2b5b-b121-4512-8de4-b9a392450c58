package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.CarrierDealerD2ADao;
import cn.com.xmmn.businesses.dao.CarrierDealerDepartDao;
import cn.com.xmmn.businesses.dao.CarrierDealerRecivDao;
import cn.com.xmmn.businesses.domain.CarrierDealerRecivDO;
import cn.com.xmmn.businesses.dt.CarrierDealerRecivDetailDT;
import cn.com.xmmn.businesses.service.CarrierDealerD2AService;
import cn.com.xmmn.businesses.service.CarrierDealerDepartService;
import cn.com.xmmn.businesses.vo.CarrierDealerD2AListVo;
import cn.com.xmmn.businesses.vo.CarrierDealerDepartListVo;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Service
public class CarrierDealerD2AServiceImpl extends ServiceImpl<CarrierDealerD2ADao,CarrierDealerRecivDO> implements CarrierDealerD2AService {

    @Autowired
    private CarrierDealerD2ADao carrierDealerD2ADao;

    @Override
    public List<CarrierDealerD2AListVo> group(Map<String,Object> query) {


        return carrierDealerD2ADao.group(query);
    }

    @Override
    public List<CarrierDealerD2AListVo>groupCount(Map<String,Object> query) {
        return carrierDealerD2ADao.groupCount(query);
    }


    @Override
    public List<CarrierDealerRecivDO> detail(CarrierDealerRecivDetailDT dealerRecivDetailDT) {
        return carrierDealerD2ADao.detail(dealerRecivDetailDT);
    }

    @Override
    public Integer detailCount(CarrierDealerRecivDetailDT dealerRecivDetailDT) {
        return carrierDealerD2ADao.detailCount(dealerRecivDetailDT);
    }
}
