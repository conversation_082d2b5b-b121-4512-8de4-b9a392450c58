package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.XgOutMailDao;
import cn.com.xmmn.businesses.domain.XgOutMailDO;
import cn.com.xmmn.businesses.service.XgOutMailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;



@Service
public class XgOutMailServiceImpl extends ServiceImpl<XgOutMailDao, XgOutMailDO>  implements XgOutMailService {
	@Autowired
	private XgOutMailDao xgOutMailDao;
	

	
	@Override
	public void save(List<XgOutMailDO> xgOutMailDOList){
		saveBatch(xgOutMailDOList);
	}
	

	
}
