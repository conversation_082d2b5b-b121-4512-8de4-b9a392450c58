package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.PdfBillDao;
import cn.com.xmmn.businesses.domain.ManualPriceDO;
import cn.com.xmmn.businesses.service.PdfBillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class PdfBillServiceImpl implements PdfBillService {

    @Autowired
    private PdfBillDao pdfBillDao;

    @Override
    public List<ManualPriceDO> getManualPrice(List<String> portIdList,List<String> flightNoList) {
        return pdfBillDao.getManualPrice(portIdList,flightNoList);
    }

}
