package cn.com.xmmn.businesses.service;

import cn.com.xmmn.businesses.domain.HkNoticeAwordDO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 香港成交通知单服务测试类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-31
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class HkNoticeAwordServiceTest {

    @Autowired
    private HkNoticeAwordService hkNoticeAwordService;

    /**
     * 测试保存功能
     */
    @Test
    public void testSave() {
        HkNoticeAwordDO entity = new HkNoticeAwordDO();
        entity.setCapacityCode("TEST001");
        entity.setEmsPrice(new BigDecimal("10.50"));
        entity.setEpacketPrice(new BigDecimal("7.70"));
        entity.setAirLettersPrice(new BigDecimal("7.70"));
        entity.setAirParcelsPrice(new BigDecimal("7.70"));
        entity.setSalPrice(new BigDecimal("5.50"));
        entity.setEffectiveDate(new Date());
        entity.setExpiryDate(new Date());
        entity.setCurrency("HKD");

        int result = hkNoticeAwordService.save(entity);
        log.info("保存结果：{}, ID：{}", result, entity.getId());
        assert result > 0;
    }

    /**
     * 测试查询功能
     */
    @Test
    public void testList() {
        Map<String, Object> params = new HashMap<>();
        params.put("capacityCode", "TEST");
        
        List<HkNoticeAwordDO> list = hkNoticeAwordService.list(params);
        int count = hkNoticeAwordService.count(params);
        
        log.info("查询结果数量：{}", count);
        for (HkNoticeAwordDO item : list) {
            log.info("查询结果：{}", item);
        }
    }

    /**
     * 测试手动调用接口（需要配置真实的第三方接口地址）
     */
    @Test
    public void testManualRequestBill() {
        try {
            String result = hkNoticeAwordService.manualRequestBill("2025-07");
            log.info("手动调用结果：{}", result);
        } catch (Exception e) {
            log.error("手动调用测试失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 测试定时任务调用（需要配置真实的第三方接口地址）
     */
    @Test
    public void testScheduledRequestBill() {
        try {
            String result = hkNoticeAwordService.scheduledRequestBill();
            log.info("定时任务调用结果：{}", result);
        } catch (Exception e) {
            log.error("定时任务调用测试失败：{}", e.getMessage(), e);
        }
    }
}
