package cn.com.xmmn.businesses.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import cn.com.xmmn.businesses.dao.OrdersImportDao;
import cn.com.xmmn.businesses.domain.OrdersImportDO;
import cn.com.xmmn.businesses.service.OrdersImportService;



@Service
public class OrdersImportServiceImpl implements OrdersImportService {
	@Autowired
	private OrdersImportDao ordersImportDao;
	
	@Override
	public OrdersImportDO get(Integer id){
		return ordersImportDao.get(id);
	}
	
	@Override
	public List<OrdersImportDO> list(Map<String, Object> map){
		return ordersImportDao.list(map);
	}
	@Override
	public Map<String,Object> countList(Map<String, Object> map){
		return ordersImportDao.countList(map);
	}

	@Override
	public int count(Map<String, Object> map){
		return ordersImportDao.count(map);
	}
	
	@Override
	public int save(OrdersImportDO ordersImport){
		return ordersImportDao.save(ordersImport);
	}
	
	@Override
	public int update(OrdersImportDO ordersImport){
		return ordersImportDao.update(ordersImport);
	}
	
	@Override
	public int remove(Integer id){
		return ordersImportDao.remove(id);
	}
	
	@Override
	public int batchRemove(Integer[] ids){
		return ordersImportDao.batchRemove(ids);
	}

	@Override
	public List<Map<String, Object>> countShipway(Map<String, Object> map) {
		return ordersImportDao.countShipway(map);
	}

	@Override
	public List<Map<String, Object>> huanbiShipway(Map<String, Object> map) {
		return ordersImportDao.huanbiShipway(map);
	}

	@Override
	public List<Map<String, Object>> oneShipway(Map<String, Object> map) {
		return ordersImportDao.oneShipway(map);
	}

	@Override
	public List<Map<String, Object>> countCustomer(Map<String, Object> map) {
		return ordersImportDao.countCustomer(map);
	}

	@Override
	public List<Map<String, Object>> huanbiCustomer(Map<String, Object> map) {
		return ordersImportDao.huanbiCustomer(map);
	}

	@Override
	public List<Map<String, Object>> oneCustomer(Map<String, Object> map) {
		return ordersImportDao.oneCustomer(map);
	}


}
