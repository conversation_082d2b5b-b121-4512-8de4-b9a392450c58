package cn.com.xmmn.common.utils;

import lombok.Data;

/**
 * @Description 响应信息
 * <AUTHOR>
 * @Date 2024/5/27 15:38
 */
@Data
public class ApiResponseData<T> {

    //响应状态码
    private int code;
    //响应消息
    private String msg;

    //实际的数据对象
    private T data;


    public ApiResponseData(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }
    public ApiResponseData(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }
    public static <T> ApiResponseData<T> ok(T data){

        return new ApiResponseData<>(0, "Success", data);
    }

    public static <T> ApiResponseData<T> ok(){

        return new ApiResponseData<>(0, "Success");
    }

    public static <T> ApiResponseData<T> error(int code, String msg){
        return new ApiResponseData<>(code, msg);
    }
    // 默认的构造函数设置默认值
    public ApiResponseData() {
        this.code = 0; // 默认状态码为0
        this.msg = "Success"; // 默认消息为"Success"
        this.data = null; // 默认数据为空
    }
}
