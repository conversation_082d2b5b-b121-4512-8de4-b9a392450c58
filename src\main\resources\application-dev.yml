config:
  uploadPath: c:/var/uploaded_files/
  username: admin
  password: 111111
  tmsKey: Zs56MbMHa3NGKgp
logging:
  level:
    root: info
    cn.com.xmmn: debug
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.jdbc.Driver
#    url: **************************************************************************************************************
#    username: emsjyfx
#    password: emsjyfx_2022
    url: ****************************************************************************************************************************************************************************************************************************************
    username: root
    password: hw123456
    initialSize: 1
    minIdle: 3
    maxActive: 20
    # 配置获取连接等待超时的时间
    maxWait: 60000
    # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
    timeBetweenEvictionRunsMillis: 60000
    # 配置一个连接在池中最小生存的时间，单位是毫秒
    minEvictableIdleTimeMillis: 30000
    validationQuery: select 'x'
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    # 打开PSCache，并且指定每个连接上PSCache的大小
    poolPreparedStatements: true
    maxPoolPreparedStatementPerConnectionSize: 20
    # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
    filters: stat,wall,slf4j
    # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
    connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    # 合并多个DruidDataSource的监控数据
    #useGlobalDataSourceStat: true
  redis:
    host: localhost
    port: 6379
    password:
    # 连接超时时间（毫秒）
    timeout: 10000
    jedis:
      pool:
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池中的最小空闲连接
        min-idle: 10
        # 连接池最大连接数（使用负值表示没有限制）
        max-active: 100
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1
interface:
  tmsPath: http://*************
  deliveryPath: http://of.elinkwms.com:6008
  importPath: http://of.elinkwms.com:9188

decrypt:
  tmsKey: Zs56MbMHa3NGKgp
  deliveryKey: jkl;POIU1234++==
push:
    track: #推送到新一代轨迹信息
      url: http://***************/querypush-rcvr/qps/rcvr/getMailsTrack #url
      identifier: ZYHK #标识
      key: 71C0F40EC68DD0C6 #秘钥