package cn.com.xmmn.businesses.dao;

import cn.com.xmmn.businesses.domain.CarrierDealerRecivDO;
import cn.com.xmmn.businesses.domain.OutMailDO;

import java.util.List;
import java.util.Map;

import cn.com.xmmn.businesses.dt.CarrierDealerRecivDetailDT;
import cn.com.xmmn.businesses.dt.OutMailDT;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 出口邮件表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-13 14:08:34
 */
@Mapper
public interface OutMailDao extends BaseMapper<OutMailDO> {

	OutMailDO get(Integer id);
	
	List<OutMailDO> list(Map<String,Object> map);

	List<OutMailDO> hjList(Map<String,Object> map);
	
	Integer save(OutMailDO outMail);

	int update(OutMailDO outMail);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);

	/**
	 * 获取封发时间邮袋详情
	 * @param dealerRecivDetailDT
	 * @return
	 */
	List<OutMailDO> detail(OutMailDT dealerRecivDetailDT);

	/**
	 * 获取封发时间邮袋详情的总数
	 * @param dealerRecivDetailDT
	 * @return
	 */
	Integer detailCount(OutMailDT dealerRecivDetailDT);
}
