package cn.com.xmmn.common.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author: zxh
 * @since: 2022-11-27 19:46
 * @description:
 */
public class UnicodeUtils {

    /**
     * unicode编码 将所有字符转换成Unicode字符
     *
     * @param string String
     * @return String
     * <AUTHOR>
     * @since 2022-11-27 19:46
     */
    public static String unicodeEncodeALL(String string) {
        char[] utfBytes = string.toCharArray();
        String unicodeBytes = "";
        for (int i = 0; i < utfBytes.length; i++) {
            String hexB = Integer.toHexString(utfBytes[i]);
            if (hexB.length() <= 2) {
                hexB = "00" + hexB;
            }
            unicodeBytes = unicodeBytes + "\\u" + hexB;
        }
        return unicodeBytes;
    }

    /**
     * unicode解码 将Unicode的编码转换为中文
     *
     * @param string String
     * @return String
     * <AUTHOR>
     * @since 2022-11-27 19:47
     */
    public static String unicodeDecode(String string) {
        Pattern pattern = Pattern.compile("(\\\\u(\\p{XDigit}{4}))");
        Matcher matcher = pattern.matcher(string);
        char ch;
        while (matcher.find()) {
            ch = (char) Integer.parseInt(matcher.group(2), 16);
            string = string.replace(matcher.group(1), ch + "");
        }
        return string;
    }


    /**
     * 只把中文转成Unicode码
     *
     * @param str String
     * @return String
     * <AUTHOR>
     * @since 2022-11-27 20:12
     */
    public static String unicodeEncode(String str) {
        String result = "";
        for (int i = 0; i < str.length(); i++) {
            int chr1 = str.charAt(i);
            if (chr1 >= 19968 && chr1 <= 171941) {//汉字范围 \u4e00-\u9fa5 (中文)
                result += "\\u" + Integer.toHexString(chr1);
            } else {
                result += str.charAt(i);
            }
        }
        return result;
    }
}
