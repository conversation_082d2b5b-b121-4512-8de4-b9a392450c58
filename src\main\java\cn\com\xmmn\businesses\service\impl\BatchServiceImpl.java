package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.BatchDao;
import cn.com.xmmn.businesses.domain.BatchDO;
import cn.com.xmmn.businesses.domain.CommercialDO;
import cn.com.xmmn.businesses.domain.NewDO;
import cn.com.xmmn.businesses.service.BatchService;
import cn.com.xmmn.common.controller.BaseController;
import cn.com.xmmn.common.utils.ShiroUtils;
import cn.com.xmmn.system.domain.UserDO;
import org.apache.commons.io.IOUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;


@Service
public class BatchServiceImpl implements BatchService {
	private SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
	java.util.Date date = new java.util.Date();//在时间前加上java.util就可以了
	@Autowired
	private BatchDao batchDao;
	@Autowired
	private BatchService batchService;
	@Autowired
	private BatchDao commercialDao;
	@Autowired
	private BatchDao newDao;
	@Autowired
	private BatchDao detailsCommercialDao;
	@Autowired
	private BatchDao newCommercialDao;
//导入t_batch批次表
	//   上传用户数据
	public void uploadExcel(MultipartFile file) throws Exception {
//        有内容的workbook工作薄
		Workbook workbook = new XSSFWorkbook(file.getInputStream());
//        获取到第一个工作表
		Sheet sheet = workbook.getSheetAt(0);
		int lastRowIndex = sheet.getLastRowNum();//当前sheet的最后一行的索引值
//        读取工作表中的内容
		Row row = null;

		for (int i = 2; i <= lastRowIndex; i++) {
			row = sheet.getRow(i);

			Integer id = ((Double) row.getCell(0).getNumericCellValue()).intValue(); //批次号
			/*String phone = null; //手机号
			try {
				phone = row.getCell(1).getStringCellValue();
			} catch (Exception e) {
				phone = row.getCell(1).getNumericCellValue() + "";
			}
			String province = row.getCell(2).getStringCellValue(); //省份
			String city = row.getCell(3).getStringCellValue(); //城市
			Integer salary = ((Double) row.getCell(4).getNumericCellValue()).intValue(); //工资
			Date hireDate = simpleDateFormat.parse(row.getCell(5).getStringCellValue()); //入职日期
			Date birthDay = simpleDateFormat.parse(row.getCell(6).getStringCellValue()); //出生日期
			String address = row.getCell(7).getStringCellValue(); //现住地址*/

			System.out.println(id);

			BatchDO batchDO = new BatchDO();

			batchDO.setId(id);
			/*batchDO.setPhone(phone);
			batchDO.setProvince(province);
			batchDO.setCity(city);
			batchDO.setSalary(salary);
			batchDO.setHireDate(hireDate);
			batchDO.setBirthday(birthDay);
			batchDO.setAddress(address);*/
//            执行插入user方法
			batchService.save(batchDO);
		}
	}
	//导入商业出口手工表
	//   上传用户数据
	public int CommercialExcel(MultipartFile file,String modelType) throws Exception {
//        有内容的workbook工作薄
		Workbook workbook = new XSSFWorkbook(file.getInputStream());
//        获取到第一个工作表
		Sheet sheet = workbook.getSheetAt(0);
		int lastRowIndex = sheet.getLastRowNum();//当前sheet的最后一行的索引值
//        读取工作表中的内容
		Row row = null;
		//登录信息查批次表
		BaseController userDO=new BaseController();
		BatchDO batchDO=new BatchDO();
		long userId=userDO.getUserId();
		String UserName= userDO.getUsername();
		long deptId=userDO.getDeptId();
		String deptName=userDO.getDeptName();
		batchDO.setCreateUserId((int)userId);
		batchDO.setCreateUserName(UserName);
		batchDO.setCreateDeptId((int)deptId);
		batchDO.setUpdateDeptName(deptName);
		batchDO.setNum(lastRowIndex);
		batchDO.setModuleType(modelType);
		batchService.save(batchDO);
		for (int i = 1; i <= lastRowIndex; i++) {
			row = sheet.getRow(i);
      //获取批次表的id,创建人id和姓名，创建部门id和名称
			int s=  batchDO.getId();
			int createUserId=batchDO.getCreateUserId();
			String createUserName=batchDO.getCreateUserName();
			int createDeptId=batchDO.getCreateDeptId();
			String createDeptName=batchDO.getCreateDeptName();

			String businessProduct = row.getCell(0).getStringCellValue(); //业务产品
			String customerCode = row.getCell(1).getStringCellValue(); //大客户号
			String customerName = row.getCell(2).getStringCellValue(); //大宗客户
			String itemNo = row.getCell(3).getStringCellValue(); //邮件号码
			String receiveCountry = row.getCell(4).getStringCellValue(); //寄达国
			String sender = row.getCell(5).getStringCellValue(); //寄件人
			Double weight= row.getCell(6).getNumericCellValue();//重量(g)
			Double amount=row.getCell(7).getNumericCellValue();//金额(元）
			Date postingDatetime= simpleDateFormat.parse(row.getCell(8).getStringCellValue());//收寄时间
			//Integer BachId = ((Double) row.getCell(0).getNumericCellValue()).intValue(); //批次号
			System.out.println(postingDatetime);
			/*String phone = null; //手机号
			try {
				phone = row.getCell(1).getStringCellValue();
			} catch (Exception e) {
				phone = row.getCell(1).getNumericCellValue() + "";
			}
			String province = row.getCell(2).getStringCellValue(); //省份
			String city = row.getCell(3).getStringCellValue(); //城市
			Integer salary = ((Double) row.getCell(4).getNumericCellValue()).intValue(); //工资
			Date hireDate = simpleDateFormat.parse(row.getCell(5).getStringCellValue()); //入职日期
			Date birthDay = simpleDateFormat.parse(row.getCell(6).getStringCellValue()); //出生日期
			String address = row.getCell(7).getStringCellValue(); //现住地址*/

			//System.out.println(BachId);

			CommercialDO commercialDO = new CommercialDO();

			commercialDO.setBusinessProduct(businessProduct);
			commercialDO.setCustomerCode(customerCode);
			commercialDO.setCustomerName(customerName);
			commercialDO.setItemNo(itemNo);
			commercialDO.setReceiveCountry(receiveCountry);
			commercialDO.setSender(sender);
			commercialDO.setWeight(weight);
			commercialDO.setAmount(amount);
			commercialDO.setPostingDatetime(postingDatetime);
			commercialDO.setBatchId(s);
			commercialDO.setCreateUserId(createUserId);
			commercialDO.setCreateUserName(createUserName);
			commercialDO.setCreateDeptId(createDeptId);
			commercialDO.setCreateDeptName(createDeptName);

			// 执行插入user方法
			batchService.exportCommercial(commercialDO);
		}
		int c=batchDO.getId();
		  return c;

	}
	//导入新一代表
	//   上传用户数据
	@Transactional
	public int NewExcel(MultipartFile file,String modelType) throws Exception {
//        有内容的workbook工作薄
		Workbook workbook = new XSSFWorkbook(file.getInputStream());
//        获取到第一个工作表
		Sheet sheet = workbook.getSheetAt(0);
		int lastRowIndex = sheet.getLastRowNum();//当前sheet的最后一行的索引值
		int h=0;
//        读取工作表中的内容
		Row row = null;
		//登录信息插批次表
		UserDO userDO = ShiroUtils.getUser();
		BatchDO batchDO=new BatchDO();
		long userId=userDO.getUserId();
		String UserName= userDO.getUsername();
		long deptId=userDO.getDeptId();
		String deptName=userDO.getDeptName();
		batchDO.setCreateUserId((int)userId);
		batchDO.setCreateUserName(UserName);
		batchDO.setCreateDeptId((int)deptId);
		batchDO.setCreateDeptName(deptName);
		batchDO.setModuleType(modelType);
		for (int i = 3; i <= lastRowIndex; i++) {
			row = sheet.getRow(i);
			if (row == null || row.getCell(0) == null) {
				break;
			}
			h++;
		}
		batchDO.setNum(h);
		batchService.save(batchDO);
       SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
		SimpleDateFormat sfm=new SimpleDateFormat("HH:mm:ss");
		SimpleDateFormat sj=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		System.out.println(lastRowIndex);
		for (int i = 3; i <= h+2; i++) {
			//System.out.println(i);
			row = sheet.getRow(i);
			//判断为空的垃圾行
			if (row==null || row.getCell(0)==null){
				break;
			}
			//获取批次表的id,创建人id和姓名，创建部门id和名称
			int s=batchDO.getId();
			int createUserId=batchDO.getCreateUserId();
			String createUserName=batchDO.getCreateUserName();
			int createDeptId=batchDO.getCreateDeptId();
			String createDeptName=batchDO.getCreateDeptName();
			//System.out.println(row.getCell(0).getStringCellValue());
			/*System.out.println(row.getCell(1).getStringCellValue());
			System.out.println(row.getCell(2).getStringCellValue());

			Date postingDate= row.getCell(3).getDateCellValue();
			String a=sdf.format(postingDate);
			Date time= row.getCell(4).getDateCellValue();
			String b=sfm.format(time);
			String c=a+" "+b;

			Date postingDatetime=simpleDateFormat.parse(c);
			System.out.println(postingDatetime);
			System.out.println(row.getCell(5).getStringCellValue());
			System.out.println(row.getCell(6).getStringCellValue());
			System.out.println(row.getCell(7).getStringCellValue());
			System.out.println(row.getCell(8).getNumericCellValue());
			System.out.println(row.getCell(9).getStringCellValue());
			System.out.println(row.getCell(10).getStringCellValue());
			System.out.println(row.getCell(11).getStringCellValue());
			System.out.println(row.getCell(12).getStringCellValue());
			System.out.println(row.getCell(13).getStringCellValue());
			System.out.println(row.getCell(14).getStringCellValue());
			System.out.println(row.getCell(15).getStringCellValue());
			System.out.println(row.getCell(16).getNumericCellValue());
			System.out.println(new BigDecimal(row.getCell(16).getNumericCellValue()));
			System.out.println(new BigDecimal(row.getCell(17).getNumericCellValue()));
			System.out.println(new BigDecimal(row.getCell(18).getNumericCellValue()));
			System.out.println(new BigDecimal(row.getCell(19).getNumericCellValue()));
			System.out.println(new BigDecimal(row.getCell(20).getNumericCellValue()));
			System.out.println(row.getCell(21).getStringCellValue());
			Date inputDate=row.getCell(22).getDateCellValue();
			String d=sdf.format(inputDate);
			Date times=row.getCell(23).getDateCellValue();
			String e=sfm.format(times);
			String f=d+" "+e;
			Date inputDatetimes=simpleDateFormat.parse(f);
			System.out.println(inputDatetimes);
			System.out.println(row.getCell(24).getStringCellValue());*/

			String mainNo = row.getCell(0)==null?"":row.getCell(0).getStringCellValue();

			String sub = row.getCell(1)==null?"":row.getCell(1).getStringCellValue();
			String type = row.getCell(2)==null?"":row.getCell(2).getStringCellValue();
			Date postingDate= row.getCell(3).getDateCellValue();
			String a=sdf.format(postingDate);
			Date time= row.getCell(4).getDateCellValue();
			String b=sfm.format(time);
			String c=a+" "+b;
			//System.out.println(c);
			Date postingDatetime=sj.parse(c);
			//System.out.println(postingDatetime);
			String itemNo = row.getCell(5)==null?"":row.getCell(5).getStringCellValue(); //邮件号
			String dest = row.getCell(6)==null?"":row.getCell(6).getStringCellValue(); //寄达国代码
			String destinationName= row.getCell(7)==null?"":row.getCell(7).getStringCellValue();//寄达国名称
			Double weight=row.getCell(8).getNumericCellValue();//重量
			String walkInPickup = row.getCell(9)==null?"":row.getCell(9).getStringCellValue();
			String arPod = row.getCell(10)==null?"":row.getCell(10).getStringCellValue();
			String kpAba = row.getCell(11)==null?"":row.getCell(11).getStringCellValue();
			//System.out.println("getCell12"+row.getCell(12));
			String csgnNo = row.getCell(12)==null?"":row.getCell(12).getStringCellValue();
			String pp = row.getCell(13)==null?"":row.getCell(13).getStringCellValue();
			String ps = row.getCell(14)==null?"":row.getCell(14).getStringCellValue();
			String mailSubClass = row.getCell(15)==null?"":row.getCell(15).getStringCellValue();
			BigDecimal postage = new BigDecimal(row.getCell(16).getNumericCellValue());
			BigDecimal sumInsured = new BigDecimal(row.getCell(17).getNumericCellValue());
			BigDecimal insurPremium = new BigDecimal(row.getCell(18).getNumericCellValue());
			BigDecimal deliveryPremium = new BigDecimal(row.getCell(19).getNumericCellValue());
			BigDecimal netAmount = new BigDecimal(row.getCell(20).getNumericCellValue());
			String deliveryType = row.getCell(21)==null?"":row.getCell(21).getStringCellValue();
			Date inputDate=row.getCell(22).getDateCellValue();
			String d=sdf.format(inputDate);
			Date times=row.getCell(23).getDateCellValue();
			String e=sfm.format(times);
			String f=d+" "+e;
			Date inputDatetime=sj.parse(f);
			String acceptanceOffice = row.getCell(24)==null?"":row.getCell(24).getStringCellValue();



			NewDO newDO = new NewDO();
			newDO.setMainNo(mainNo);
			newDO.setSub(sub);
			newDO.setType(type);
			newDO.setPostingDatetime(postingDatetime);
			newDO.setItemNo(itemNo);
			newDO.setDest(dest);
			newDO.setDestinationName(destinationName);
			newDO.setWeight(weight);
			newDO.setWalkInPickup(walkInPickup);
			newDO.setArPod(arPod);
			newDO.setKpAba(kpAba);
			newDO.setCsgnNo(csgnNo);
			newDO.setPp(pp);
			newDO.setPs(ps);
			newDO.setMailSubClass(mailSubClass);
			newDO.setPostage(postage);
			newDO.setSumInsured(sumInsured);
			newDO.setInsurPremium(insurPremium);
			newDO.setDeliveryPremium(deliveryPremium);
			newDO.setNetAmount(netAmount);
			newDO.setDeliveryType(deliveryType);
			newDO.setInputDatetime(inputDatetime);
			newDO.setAcceptanceOffice(acceptanceOffice);
			newDO.setBatchId(s);
			newDO.setCreateUserId(createUserId);
			newDO.setCreateUserName(createUserName);
			newDO.setCreateDeptId(createDeptId);
			newDO.setCreateDeptName(createDeptName);

//            执行插入新一代表方法
			batchService.exportNew(newDO);
		//	System.out.println("test");
		}

		int c=batchDO.getId();
		return c;

	}
	//下载导入模板
	public void downloadExcel(HttpServletResponse response) throws UnsupportedEncodingException {
//特别注意：上面用file流读取文件只能在本地（windows）适用，项目打成jar包后再linux执行，会提示读取不到路径，linux想要读取jar包里的文件，只能通过以下流的形式来读
		String path = "file/xianggangyouzheng.xlsx";
		try {
			InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(path);
			OutputStream outputStream = response.getOutputStream();
			response.setContentType("application/x-download");
			response.addHeader("Content-Disposition", "attachment;filename=" + "xianggangyouzheng.xlsx");
			IOUtils.copy(inputStream, outputStream);
			outputStream.flush();
			inputStream.close();
			outputStream.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	
	@Override
	public List<BatchDO> list(Map<String, Object> map){
		return batchDao.list(map);
	}
	
	@Override
	public int count(Map<String, Object> map){
		return batchDao.count(map);
	}
	
	@Override
	public int save(BatchDO batch){
		return batchDao.save(batch);
	}

	/*@Override
	public BatchDO get(Integer id){
		return batchDao.get(id);
	}
*/

	@Override
	public int update(BatchDO batch){
		return batchDao.update(batch);
	}
	
	@Override
	public int remove(Integer id){
		return batchDao.remove(id);
	}
	
	@Override
	public int batchRemove(Integer[] ids){
		return batchDao.batchRemove(ids);
	}

	@Override
	public int exportCommercial(CommercialDO bacth) {
		return commercialDao.exportCommercial(bacth);
	}

	@Override
	public int exportNew(NewDO bacth) {
		return newDao.exportNew(bacth);
	}

	@Override
	public List detailsCommercial(CommercialDO bacth) {
		return detailsCommercialDao.detailsCommercial(bacth);
	}


	@Override
	public List newCommercial(NewDO batch) {
		return newCommercialDao.newCommercial(batch);
	}

	@Override
	//	<!--查询商业导入每月件数报表-->
	public List<Map<String,Object>> commercialList(Map<String,Object> map){
		return batchDao.commercialList(map);
	}
}
