package cn.com.xmmn.common.exception;


import cn.com.xmmn.common.utils.ApiResponseData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.error.ErrorAttributes;
import org.springframework.boot.web.servlet.error.ErrorController;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.ModelAndView;

import cn.com.xmmn.common.utils.R;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.security.Principal;
import java.util.Iterator;
import java.util.Locale;
import java.util.Map;
/**
 *
 * 用来处理异常时是跳转错误页面还是返回json格式
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024年7月25日09:44:35
 */
@RestController
public class MainsiteErrorController implements ErrorController {
    private Logger logger = LoggerFactory.getLogger(getClass());
    private static final String ERROR_PATH = "/error";

    @Autowired
    ErrorAttributes errorAttributes;

    @RequestMapping(
            value = {ERROR_PATH},
            produces = {"text/html"}
    )
    public ModelAndView errorHtml(HttpServletRequest request, HttpServletResponse response) {
        int code = response.getStatus();
        if (404 == code) {
            return new ModelAndView("error/404");
        } else if (403 == code) {
            return new ModelAndView("error/403");
        } else if (401 == code) {
            return new ModelAndView("login");
        } else {
            return new ModelAndView("error/500");
        }

    }

    @RequestMapping(value = ERROR_PATH)
    public R handleError(HttpServletRequest request, HttpServletResponse response) {
        int code = response.getStatus();
        if (404 == code) {
            return R.error(404, "未找到资源");
        } else if (403 == code) {
            return R.error(403, "没有访问权限");
        } else if (401 == code) {
            return R.error(403, "登录过期");
        } else {
            return R.error(500, "服务器错误");
        }
    }

    @Override
    public String getErrorPath() {
        // TODO Auto-generated method stub
        return ERROR_PATH;
    }

    /**
     * 返回json时不要跳转页面
     * @param request
     * @return
     */
    @RequestMapping(value = ERROR_PATH, produces = {"application/json"})
    public ResponseEntity<ApiResponseData> errorJson(HttpServletRequest request) {
        WebRequest webRequest = (WebRequest) request;
        Map<String, Object> errorAttributesMap = errorAttributes.getErrorAttributes(webRequest, true);

        HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;
        Integer statusCode = (Integer) errorAttributesMap.get("status");
        if (statusCode != null) {
            status = HttpStatus.valueOf(statusCode);
        }

        String message = (String) errorAttributesMap.get("error");
        if (message == null) {
            message = "服务器内部错误";
        }
        ApiResponseData responseData = new ApiResponseData(status.value(), (String) errorAttributesMap.get("message"));
        return new ResponseEntity<>(responseData, status);
    }
}