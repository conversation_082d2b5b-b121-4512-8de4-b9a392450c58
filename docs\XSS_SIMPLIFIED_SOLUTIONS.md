# XSS防护简化方案

## 问题
如果每个控制器都手动写XSS清洗代码，会导致大量重复代码，维护困难。

## 🎯 **解决方案对比**

### **方案1：使用通用清洗方法（推荐）**

#### 新增的工具方法：
```java
// 在 XssUtil 中新增
public static <T> T cleanObject(T obj)              // 清洗单个对象
public static <T> List<T> cleanObjectList(List<T> list)  // 清洗对象列表
```

#### 使用示例：
```java
@GetMapping("/list")
public PageUtils list(@RequestParam Map<String, Object> params) {
    // 清洗查询参数
    Map<String, Object> safeParams = XssUtil.cleanMap(params);
    
    // 查询数据
    Query query = new Query(safeParams);
    List<DictDO> dictList = dictService.list(query);
    int total = dictService.count(query);
    
    // 一行代码清洗所有返回数据
    XssUtil.cleanObjectList(dictList);
    
    return new PageUtils(dictList, total);
}
```

**优点：**
- 只需要一行代码 `XssUtil.cleanObjectList(dictList)`
- 使用反射自动清洗所有字符串字段
- 不需要修改现有继承关系

---

### **方案2：继承XSS防护基类**

#### 新增基类：
```java
public abstract class XssProtectedBaseController extends BaseController {
    protected <T> PageUtils createSafePageUtils(List<T> list, int total)
    protected <T> List<T> createSafeList(List<T> list)
    protected <T> T createSafeObject(T obj)
    protected Map<String, Object> cleanQueryParams(Map<String, Object> params)
    // ... 更多便捷方法
}
```

#### 使用示例：
```java
// 继承XSS防护基类
public class DictController extends XssProtectedBaseController {
    
    @GetMapping("/list")
    public PageUtils list(@RequestParam Map<String, Object> params) {
        // 清洗查询参数
        Map<String, Object> safeParams = cleanQueryParams(params);
        
        // 查询数据
        Query query = new Query(safeParams);
        List<DictDO> dictList = dictService.list(query);
        int total = dictService.count(query);
        
        // 一行代码创建安全的PageUtils
        return createSafePaageUtils(dictList, total);
    }
}
```

**优点：**
- 方法名更语义化
- 提供更多便捷方法
- 统一的XSS防护规范

**缺点：**
- 需要修改继承关系

---

### **方案3：使用注解（现有方案）**

#### 使用示例：
```java
@GetMapping("/list")
@XssClean  // 自动清洗
public PageUtils list(@RequestParam Map<String, Object> params) {
    // 查询数据
    Query query = new Query(params);
    List<DictDO> dictList = dictService.list(query);
    int total = dictService.count(query);
    
    return new PageUtils(dictList, total);
}
```

**优点：**
- 代码最简洁
- 自动处理

**缺点：**
- 安全扫描工具可能无法识别

---

## 🚀 **推荐的最佳实践**

### **组合使用方案1 + 方案3**

```java
@Controller
public class DictController extends BaseController {
    
    // 对于简单的查询，使用注解
    @GetMapping("/simple")
    @XssClean
    public List<DictDO> simpleList(@RequestParam Map<String, Object> params) {
        return dictService.list(new Query(params));
    }
    
    // 对于复杂的业务，使用显式清洗（便于扫描工具识别）
    @GetMapping("/list")
    public PageUtils list(@RequestParam Map<String, Object> params) {
        // 清洗查询参数
        Map<String, Object> safeParams = XssUtil.cleanMap(params);
        
        // 查询数据
        Query query = new Query(safeParams);
        List<DictDO> dictList = dictService.list(query);
        int total = dictService.count(query);
        
        // 清洗返回数据
        XssUtil.cleanObjectList(dictList);
        
        return new PageUtils(dictList, total);
    }
    
    // 对于保存操作，使用对象清洗
    @PostMapping("/save")
    public R save(DictDO dict) {
        try {
            // 清洗输入对象
            XssUtil.cleanObject(dict);
            
            // 验证安全性
            if (!XssUtil.isSafe(dict.getName())) {
                return R.error("输入不安全");
            }
            
            return dictService.save(dict) > 0 ? R.ok() : R.error();
        } catch (Exception e) {
            return R.error(XssUtil.getSafeErrorMessage(e.getMessage()));
        }
    }
}
```

## 📊 **代码量对比**

### **原来的写法（每个字段手动清洗）：**
```java
// 需要 10+ 行代码
for (DictDO dict : dictList) {
    dict.setName(dict.getName() != null ? XssUtil.clean(dict.getName()) : null);
    dict.setValue(dict.getValue() != null ? XssUtil.clean(dict.getValue()) : null);
    dict.setType(dict.getType() != null ? XssUtil.clean(dict.getType()) : null);
    dict.setDescription(dict.getDescription() != null ? XssUtil.clean(dict.getDescription()) : null);
    dict.setRemarks(dict.getRemarks() != null ? XssUtil.clean(dict.getRemarks()) : null);
}
```

### **新的写法（通用方法）：**
```java
// 只需要 1 行代码
XssUtil.cleanObjectList(dictList);
```

## ✅ **总结**

1. **最简单**：使用 `XssUtil.cleanObjectList()` 一行代码解决
2. **最规范**：继承 `XssProtectedBaseController` 获得更多便捷方法
3. **最灵活**：组合使用注解和显式清洗

**建议：**
- 新项目：使用方案2（继承基类）
- 现有项目：使用方案1（通用方法）
- 关键接口：使用显式清洗确保扫描工具能识别
