package cn.com.xmmn.common.enums;
/**
 *
 *  新一代轨迹事件类型枚举
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024年10月9日10:04:19
 */
public enum PostalTraceOpCodeEnum {

    AIRLINE_RECEIVED("500", "航空公司接收"),
    AIRLINE_DEPARTED("457", "航空公司启运"),
    AIRCRAFT_ARRIVED("505", "飞机到达进港(运抵)"),
    DELIVERY_TO_FOREIGN("507", "送交境外航站（交邮处理）");


    private final String value;
    private final String description;

    PostalTraceOpCodeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
}
