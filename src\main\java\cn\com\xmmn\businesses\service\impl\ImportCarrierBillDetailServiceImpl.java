package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.FeedbackRecordsDao;
import cn.com.xmmn.businesses.dao.ImportCarrierBillDetailDao;
import cn.com.xmmn.businesses.domain.CarrierBillLogDO;
import cn.com.xmmn.businesses.domain.FeedbackRecordsDO;
import cn.com.xmmn.businesses.domain.ImportCarrierBillDetailDO;
import cn.com.xmmn.businesses.service.CarrierBillLogService;
import cn.com.xmmn.businesses.service.ImportCarrierBillDetailService;
import cn.com.xmmn.common.dao.DictDao;
import cn.com.xmmn.common.domain.DictDO;
import cn.com.xmmn.common.enums.PushStatusEnum;
import cn.com.xmmn.common.service.DictService;
import cn.com.xmmn.common.utils.DateUtils;
import cn.com.xmmn.common.utils.ShiroUtils;
import cn.com.xmmn.system.domain.UserDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.IOUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;


@Service
public class ImportCarrierBillDetailServiceImpl implements ImportCarrierBillDetailService {
	@Autowired
	private   DictDao dictDao;
	@Autowired
	private ImportCarrierBillDetailDao importCarrierBillDetailDao;
	@Autowired
	private ImportCarrierBillDetailService importCarrierBillDetailService;

	@Autowired
	private CarrierBillLogService carrierBillLogService;
	@Autowired
	private DictService dictService;

	public ImportCarrierBillDetailServiceImpl() {
		dictDao = null;
	}

	@Override
	public ImportCarrierBillDetailDO get(Integer id){
		return importCarrierBillDetailDao.get(id);
	}
	
	@Override
	public List<ImportCarrierBillDetailDO> list(Map<String, Object> map){
		return importCarrierBillDetailDao.list(map);
	}
	
	@Override
	public int count(Map<String, Object> map){
		return importCarrierBillDetailDao.count(map);
	}

	@Override
	public int countRow(String barcodes){
		return importCarrierBillDetailDao.countRow(barcodes);
	}
	@Override
	public int countHis(String barcodes){
		return importCarrierBillDetailDao.countHis(barcodes);
	}

	@Override
	public int save(ImportCarrierBillDetailDO importCarrierBillDetailDO){
		return importCarrierBillDetailDao.save(importCarrierBillDetailDO);
	}
	
	@Override
	public int update(ImportCarrierBillDetailDO importCarrierBillDetailDO){
		return importCarrierBillDetailDao.update(importCarrierBillDetailDO);
	}
	
	@Override
	public int remove(Integer id){
		return importCarrierBillDetailDao.remove(id);
	}
	
	@Override
	public int batchRemove(Map<String, Object> map){

		List<ImportCarrierBillDetailDO> list=importCarrierBillDetailDao.listByIds(map);

		UserDO user = ShiroUtils.getUser();
		List<CarrierBillLogDO> carrierBillLogDOS=new ArrayList<>();
		list.forEach(importCarrierBillDetailDO -> {

			boolean exists =carrierBillLogDOS.stream()
					.anyMatch(log ->log.getCarrierName().equals(importCarrierBillDetailDO.getCarrierName()) && log.getAccountPeriod().equals(importCarrierBillDetailDO.getAccountPeriod()));
			if(!exists){
			//添加日志
			CarrierBillLogDO carrierBillLogDO=new CarrierBillLogDO();
			carrierBillLogDO.setCarrierName(importCarrierBillDetailDO.getCarrierName());
			carrierBillLogDO.setAccountPeriod(importCarrierBillDetailDO.getAccountPeriod());
			carrierBillLogDO.setOperType("delete");
			carrierBillLogDO.setCreatedBy(user.getUsername());
			carrierBillLogDO.setCreatedTime(new Date());
			carrierBillLogDOS.add(carrierBillLogDO);
			}
		});

		//添加日志
		carrierBillLogService.saveBatch(carrierBillLogDOS);

		return importCarrierBillDetailDao.batchRemove(map);
	}

	@Override
	public int exportNew(ImportCarrierBillDetailDO importCarrierBillDetailDO) {
		return importCarrierBillDetailDao.save(importCarrierBillDetailDO);
	}

	@Transactional
	public List NewExcel(MultipartFile file, String modelType) throws Exception {


		List<CarrierBillLogDO> carrierBillLogDOS=new ArrayList<>();
		UserDO user = ShiroUtils.getUser();
		//有内容的workbook工作薄
		Workbook workbook = new XSSFWorkbook(file.getInputStream());
//        获取到第一个工作表
		Sheet sheet = workbook.getSheetAt(0);
		//当前sheet的最后一行的索引值
		int lastRowIndex = sheet.getLastRowNum();
		ArrayList<Object> list = new ArrayList<>();

		HashMap<String, String> map = new HashMap<>();
		int h=0;
//        读取工作表中的内容
		Row row = null;
//		for (int i = 1; i <= lastRowIndex; i++) {
//			row = sheet.getRow(i);
//			if (row == null || row.getCell(2).getStringCellValue() == "") {
//				String barcodes = row.getCell(2)==null?"":row.getCell(2).getStringCellValue();
//				map.put("错误结构","第i行总包号"+barcodes+"为空");
//				break;
//			}
//			h++;
//		}
		for (int i = 1; i <= lastRowIndex; i++) {
			//System.out.println(i);
			StringBuilder msgError = new StringBuilder();

			row = sheet.getRow(i);


//			//判断为空行
//			if (row == null) {
//				String barcodes = row.getCell(2)==null?"":row.getCell(2).getStringCellValue();
//				map.put("错误结构","该行记录为空行");
//				mapError.put("错误结构","该行记录为空行");
//				list.add(map);
////				continue;
//
//			}
			String barcodes = row.getCell(2)==null?"":row.getCell(2).getStringCellValue();


			//判断是否存在结算记录
			if (importCarrierBillDetailService.countRow(barcodes)>0){
				map.put("错误结构","总包号"+barcodes+"重复结算记录");
//				list.add(map);
				msgError.append("该总包号重复结算记录|");
			}


			//判断是否存在历史结算
			if (importCarrierBillDetailService.countHis(barcodes)>0){
				map.put("错误结构","总包号"+barcodes+"已存在结算");
//				list.add(map);
				msgError.append("该总包号已存在结算|");
			}


			//判断邮件类型为空
			if ( row.getCell(0).getStringCellValue() == ""
					) {

				map.put("错误结构","总包号"+barcodes+"的邮件类型为空");
				msgError.append("邮件类型为空|");
//				list.add(map);
			}
			//判断承运人名称为空
			if ( row.getCell(1).getStringCellValue() == ""
			) {
				map.put("错误结构","总包号"+barcodes+"的承运人名称为空");
//				list.add(map);
				msgError.append("承运人名称为空|");
			}
			//判断barcode为空
			if ( row.getCell(2).getStringCellValue() == ""
			) {
				map.put("错误结构","总包号"+barcodes+"的barcode为空");
//				list.add(map);
				msgError.append("barcode为空|");
			}
			//判断CN38时间为空
			if ( row.getCell(3).getDateCellValue()   ==null
			) {
				map.put("错误结构","总包号"+barcodes+"的CN38时间为空");
//				list.add(map);
				msgError.append("CN38时间为空|");
			}
			//判断接收扫描时间为空
			if ( row.getCell(4).getDateCellValue()   ==null
			) {
				map.put("错误结构","总包号"+barcodes+"的接收扫描时间为空");
//				list.add(map);
				msgError.append("接收扫描时间为空|");
			}
			//判断启运地点为空
			if (row.getCell(5).getStringCellValue()   ==""
			) {
				map.put("错误结构","总包号"+barcodes+"的启运地点为空");
//				list.add(map);
				msgError.append("启运地点为空|");
			}
			//判断启运时间为空
			if ( row.getCell(6).getDateCellValue()   ==null
			) {
				map.put("错误结构","总包号"+barcodes+"的启运时间为空");
//				list.add(map);
				msgError.append("启运时间为空|");
			}
			//判断到达地点为空
			if ( row.getCell(10).getStringCellValue()   ==""
			) {
				map.put("错误结构","总包号"+barcodes+"的到达地点为空");
//				list.add(map);
				msgError.append("到达地点为空|");
			}
			//判断目的地到达时间为空
			if ( row.getCell(11).getDateCellValue()   ==null
			) {
				map.put("错误结构","总包号"+barcodes+"的目的地到达时间为空");
//				list.add(map);
				msgError.append("目的地到达时间为空|");
			}
			//判断目的地交邮时间为空
			if ( row.getCell(12).getDateCellValue()   ==null
			) {
				map.put("错误结构","总包号"+barcodes+"的目的地交邮时间为空");
//				list.add(map);
				msgError.append("目的地交邮时间为空|");
			}
			//判断收费路由为空
			if ( row.getCell(13).getStringCellValue()   ==""
			) {
				map.put("错误结构","总包号"+barcodes+"的收费路由为空");
//				list.add(map);
				msgError.append("收费路由为空|");
			}
			//判断航班为空
			if ( row.getCell(14).getStringCellValue()   ==""
			) {
				map.put("错误结构","总包号"+barcodes+"的航班为空");
//				list.add(map);
				msgError.append("航班为空|");
			}
			//判断重量为空
			if ( row.getCell(15).getNumericCellValue()  ==0
			) {
				map.put("错误结构","总包号"+barcodes+"的重量为空");
//				list.add(map);
				msgError.append("重量为空|");
			}
			//判断费率为空
			if ( row.getCell(16).getNumericCellValue()  ==0
			) {
				map.put("错误结构","总包号"+barcodes+"的费率为空");
//				list.add(map);
				msgError.append("费率为空|");
			}
			//判断金额为空
			if ( row.getCell(17).getNumericCellValue()  ==0
			) {
				map.put("错误结构","总包号"+barcodes+"的金额为空");
//				list.add(map);
				msgError.append("金额为空|");
			}
			//判断币种为空
			if ( row.getCell(18).getStringCellValue()   ==""
			) {
				map.put("错误结构","总包号"+barcodes+"的币种为空");
//				list.add(map);
				msgError.append("币种为空|");
			}
			//判断账务时期为空
			if ( row.getCell(19).getNumericCellValue()  ==0
			) {
				map.put("错误结构","总包号"+barcodes+"的账务时期为空");
//				list.add(map);
				msgError.append("账务时期为空|");
			}
			//判断运能编码为空
			if ( row.getCell(22).getNumericCellValue()  ==0
			) {
				map.put("错误结构","总包号"+barcodes+"的运能编码为空");
//				list.add(map);
				msgError.append("运能编码为空|");
			}
			//判断箱板类型为空
			if ( row.getCell(23).getStringCellValue()   ==""
			) {
				map.put("错误结构","总包号"+barcodes+"的箱板类型为空");
//				list.add(map);
				msgError.append("箱板类型为空|");
			}
			//接收扫描时间不可早于CN38时间
			if (row.getCell(3).getDateCellValue()!=null && row.getCell(4).getDateCellValue()!=null && row.getCell(3).getDateCellValue().after(row.getCell(4).getDateCellValue())){
				map.put("错误结构","总包号"+barcodes+"的接收扫描时间不可早于CN38时间");
//				list.add(map);
				msgError.append("接收扫描时间不可早于CN38时间|");


			}
			//启运时间不可早于接受扫描时间
			if (row.getCell(4).getDateCellValue()!=null && row.getCell(6).getDateCellValue()!=null && row.getCell(4).getDateCellValue().after(row.getCell(6).getDateCellValue())){
				map.put("错误结构","总包号"+barcodes+"的启运时间不可早于接受扫描时间");
//				list.add(map);
				msgError.append("启运时间不可早于接受扫描时间|");

			}
			//目的地到达时间不可早于启运时间
			if (row.getCell(6).getDateCellValue()!=null && row.getCell(11).getDateCellValue()!=null && row.getCell(6).getDateCellValue().after(row.getCell(11).getDateCellValue())){
				map.put("错误结构","总包号"+barcodes+"的目的地到达时间不可早于启运时间");
//				list.add(map);
				msgError.append("目的地到达时间不可早于启运时间|");

			}
			//目的地交邮时间不可早于目的地到达时间
			if (row.getCell(11).getDateCellValue()!=null && row.getCell(12).getDateCellValue()!=null && row.getCell(11).getDateCellValue().after(row.getCell(12).getDateCellValue())){
				map.put("错误结构","总包号"+barcodes+"的目的地交邮时间不可早于目的地到达时间");
//				list.add(map);
				msgError.append("目的地交邮时间不可早于目的地到达时间|");
			}



			String mail_type = row.getCell(0)==null?"":row.getCell(0).getStringCellValue();
			String carrier_name = row.getCell(1)==null?"":row.getCell(1).getStringCellValue();
			String barcode = row.getCell(2)==null?"":row.getCell(2).getStringCellValue();
			Date cn38time = row.getCell(3)==null?null:row.getCell(3).getDateCellValue();

			Date scan_time = row.getCell(4)==null?null:row.getCell(4).getDateCellValue();
			String start_place = row.getCell(5)==null?"":row.getCell(5).getStringCellValue();
			Date start_time = row.getCell(6)==null?null:row.getCell(6).getDateCellValue();
			String trans_arrive_place = row.getCell(7)==null?"":row.getCell(7).getStringCellValue();
			Date trans_arrive_time = row.getCell(8)==null?null:row.getCell(8).getDateCellValue();
			Date trans_start_time = row.getCell(9)==null?null:row.getCell(9).getDateCellValue();
			String arrive_place = row.getCell(10)==null?"":row.getCell(10).getStringCellValue();
			Date arrive_time =row.getCell(11)==null?null:row.getCell(11).getDateCellValue();
			Date hold_time = row.getCell(12)==null?null:row.getCell(12).getDateCellValue();
			String toll_route = row.getCell(13)==null?"":row.getCell(13).getStringCellValue();
			String flight_num = row.getCell(14)==null?"":row.getCell(14).getStringCellValue();
			BigDecimal weight = row.getCell(15)==null?null:new BigDecimal(row.getCell(15).getNumericCellValue());
			BigDecimal fee_rate = row.getCell(16)==null?null:new BigDecimal(row.getCell(16).getNumericCellValue());
			BigDecimal amount = row.getCell(17)==null?null:new BigDecimal(row.getCell(17).getNumericCellValue());
			String currency = row.getCell(18)==null?"":row.getCell(18).getStringCellValue();
			String account_period = row.getCell(19)==null?"":String.valueOf((row.getCell(19).getNumericCellValue())==0?"":(int)row.getCell(19).getNumericCellValue());
			String bill_no = row.getCell(20)==null?"":row.getCell(20).getStringCellValue();
			String remark = row.getCell(21)==null?"":row.getCell(21).getStringCellValue();
			String capacity_code = row.getCell(22)==null?"":String.valueOf((row.getCell(22).getNumericCellValue())==0?"":(int)row.getCell(22).getNumericCellValue());
			String case_type = row.getCell(23)==null?"":row.getCell(23).getStringCellValue();
			String pack_num = row.getCell(24)==null?"":row.getCell(24).getStringCellValue();

			ImportCarrierBillDetailDO importCarrierBillDetailDO = new ImportCarrierBillDetailDO();
			importCarrierBillDetailDO.setMailType(mail_type);
			importCarrierBillDetailDO.setCarrierName(carrier_name);
			importCarrierBillDetailDO.setBarcode(barcode);
			importCarrierBillDetailDO.setCn38time(cn38time);
			importCarrierBillDetailDO.setScanTime(scan_time);
			importCarrierBillDetailDO.setStartPlace(start_place);
			importCarrierBillDetailDO.setStartTime(start_time);
			importCarrierBillDetailDO.setTransArrivePlace(trans_arrive_place);
			importCarrierBillDetailDO.setTransArriveTime(trans_arrive_time);
			importCarrierBillDetailDO.setTransStartTime(trans_start_time);
			importCarrierBillDetailDO.setArrivePlace(arrive_place);
			importCarrierBillDetailDO.setArriveTime(arrive_time);
			importCarrierBillDetailDO.setHoldTime(hold_time);
			importCarrierBillDetailDO.setTollRoute(toll_route);
			importCarrierBillDetailDO.setFlightNum(flight_num);
			importCarrierBillDetailDO.setWeight(weight);
			importCarrierBillDetailDO.setFeeRate(fee_rate);
			importCarrierBillDetailDO.setAmount(amount);
			importCarrierBillDetailDO.setCurrency(currency);
			importCarrierBillDetailDO.setAccountPeriod(account_period);
			importCarrierBillDetailDO.setBillNo(bill_no);
			importCarrierBillDetailDO.setRemark(remark);
			importCarrierBillDetailDO.setCapacityCode(capacity_code);
			importCarrierBillDetailDO.setCaseType(case_type);
			importCarrierBillDetailDO.setPackNum(pack_num);
			if(msgError.length()>0){ //导入失败
				importCarrierBillDetailDO.setImportState("2");
				importCarrierBillDetailDO.setImportContent(msgError.toString());
			}else{
				importCarrierBillDetailDO.setImportState("1");
			}
			importCarrierBillDetailService.exportNew(importCarrierBillDetailDO);


			//查询是否重复了承运商和财务日期
			boolean exists =carrierBillLogDOS.stream()
					.anyMatch(log ->log.getCarrierName().equals(carrier_name) && log.getAccountPeriod().equals(account_period));
            if(!exists){
              //添加日志
				CarrierBillLogDO carrierBillLogDO=new CarrierBillLogDO();
				carrierBillLogDO.setCarrierName(carrier_name);
				carrierBillLogDO.setAccountPeriod(account_period);
				carrierBillLogDO.setOperType("import");
				carrierBillLogDO.setCreatedBy(user.getUsername());
				carrierBillLogDO.setCreatedTime(new Date());
				carrierBillLogDOS.add(carrierBillLogDO);


			}
		}
        //添加日志
		carrierBillLogService.saveBatch(carrierBillLogDOS);


		return list;
	}



	@Transactional
	public List importCsv(MultipartFile file, String modelType) throws Exception {


		List<CarrierBillLogDO> carrierBillLogDOS=new ArrayList<>();
		UserDO user = ShiroUtils.getUser();
		ArrayList<Object> list = new ArrayList<>();

		HashMap<String, String> map = new HashMap<>();
		int h=0;

		// 使用InputStreamReader包装InputStream，并指定字符集（这里使用UTF-8）
		// 也可以根据需要替换为其他字符集
		InputStreamReader inputStreamReader = new InputStreamReader(file.getInputStream(), "GBK");

		// 创建CSVParser实例，这里使用CSVFormat.DEFAULT作为格式，也可以根据需要自定义格式
		CSVParser csvParser = new CSVParser(inputStreamReader, CSVFormat.DEFAULT.withFirstRecordAsHeader());
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd"); // 假设CSV中的日期格式是"yyyyMMdd"

		// 遍历CSV中的每一行记录
		Iterator<CSVRecord> iterator = csvParser.iterator();
		while (iterator.hasNext()) {
			CSVRecord csvRecord = iterator.next();
			StringBuilder msgError = new StringBuilder();
			// 根据需要处理每一行记录


			String mail_type = csvRecord.get("邮件种类").trim()==null?"":csvRecord.get("邮件种类").trim();
			String carrier_name = csvRecord.get("承运人名称").trim()==null?"":csvRecord.get("承运人名称").trim();
			String barcodes = csvRecord.get("Barcode").trim()==null?"":csvRecord.get("Barcode").trim();
			String cn38timeStr=csvRecord.get("CN38时间").trim();
			Date cn38time =null;
			if(!cn38timeStr.isEmpty()){
				cn38time=formatter.parse(cn38timeStr);
			}
			String scan_timeStr=csvRecord.get("接收扫描时间").trim();
			Date scan_time =null;
			if(!scan_timeStr.isEmpty()){
				scan_time=formatter.parse(scan_timeStr);
			}
			String start_place = csvRecord.get("启运地点").trim()==null?"":csvRecord.get("启运地点").trim();
			String start_timeStr=csvRecord.get("启运时间").trim();
			Date start_time =null;
			if(!start_timeStr.isEmpty()){
				start_time=formatter.parse(start_timeStr);
			}
			String trans_arrive_place = csvRecord.get("中转到达地点").trim()==null?"":csvRecord.get("中转到达地点").trim();
			String trans_arrive_timeStr=csvRecord.get("中转到达时间").trim();
			Date trans_arrive_time =null;
			if(!trans_arrive_timeStr.isEmpty()){
				trans_arrive_time=formatter.parse(trans_arrive_timeStr);
			}
			String trans_start_timeStr=csvRecord.get("中转启运时间").trim();
			Date trans_start_time =null;
			if(!trans_start_timeStr.isEmpty()){
				trans_start_time=formatter.parse(trans_start_timeStr);
			}
			String arrive_place = csvRecord.get("到达地点").trim()==null?"":csvRecord.get("到达地点").trim();
			String arrive_timeStr=csvRecord.get("目的地到达时间").trim();
			Date arrive_time =null;
			if(!arrive_timeStr.isEmpty()){
				arrive_time=formatter.parse(arrive_timeStr);
			}
			String hold_timeStr=csvRecord.get("目的地交邮时间").trim();
			Date hold_time =null;
			if(!hold_timeStr.isEmpty()){
				hold_time=formatter.parse(hold_timeStr);
			}
			String toll_route = csvRecord.get("收费路由").trim()==null?"":csvRecord.get("收费路由").trim();
			String flight_num = csvRecord.get("航班").trim()==null?"":csvRecord.get("航班").trim();
			String weightStr = csvRecord.get("重量").trim()==null?"":csvRecord.get("重量").trim();
			BigDecimal weight = new BigDecimal(weightStr);
			String fee_rateStr = csvRecord.get("费率").trim()==null?"":csvRecord.get("费率").trim();
			BigDecimal fee_rate = new BigDecimal(fee_rateStr);
			String amountStr = csvRecord.get("金额").trim()==null?"":csvRecord.get("金额").trim();
			BigDecimal amount = new BigDecimal(amountStr);
			String currency = csvRecord.get("币种").trim()==null?"":csvRecord.get("币种").trim();
			String account_period = csvRecord.get("账务时期").trim()==null?"":csvRecord.get("账务时期").trim();
			String bill_no = csvRecord.get("账单编号").trim()==null?"":csvRecord.get("账单编号").trim();
			String remark = csvRecord.get("备注").trim()==null?"":csvRecord.get("备注").trim();
			String capacity_code =csvRecord.get("运能编码").trim()==null?"":csvRecord.get("运能编码").trim();
			String case_type = csvRecord.get("箱板类型").trim()==null?"":csvRecord.get("箱板类型").trim();
			String pack_num = csvRecord.get("集装器号（板号）").trim()==null?"":csvRecord.get("集装器号（板号）").trim();

			//判断是否存在结算记录
			if (importCarrierBillDetailService.countRow(barcodes)>0){
				map.put("错误结构","总包号"+barcodes+"重复结算记录");
//				list.add(map);
				msgError.append("该总包号重复结算记录|");
			}

			//判断是否存在历史结算
			if (importCarrierBillDetailService.countHis(barcodes)>0){
				map.put("错误结构","总包号"+barcodes+"已存在结算");
//				list.add(map);
				msgError.append("该总包号已存在结算|");
			}


			//判断邮件类型为空
			if ( mail_type == "" || mail_type == null
			) {

				map.put("错误结构","总包号"+barcodes+"的邮件类型为空");
				msgError.append("邮件类型为空|");
//				list.add(map);
			}
			//判断承运人名称为空
			if ( carrier_name == "" || carrier_name == null
			) {
				map.put("错误结构","总包号"+barcodes+"的承运人名称为空");
//				list.add(map);
				msgError.append("承运人名称为空|");
			}
			//判断barcode为空
			if ( barcodes == "" || barcodes == null
			) {
				map.put("错误结构","总包号"+barcodes+"的barcode为空");
//				list.add(map);
				msgError.append("barcode为空|");
			}
			//判断CN38时间为空
			if ( cn38time  ==null
			) {
				map.put("错误结构","总包号"+barcodes+"的CN38时间为空");
//				list.add(map);
				msgError.append("CN38时间为空|");
			}
			//判断接收扫描时间为空
			if ( scan_time   ==null
			) {
				map.put("错误结构","总包号"+barcodes+"的接收扫描时间为空");
//				list.add(map);
				msgError.append("接收扫描时间为空|");
			}

			//接收扫描时间不可早于CN38时间
			if (cn38time!=null && scan_time!=null && cn38time.after(scan_time)){
				map.put("错误结构","总包号"+barcodes+"的接收扫描时间不可早于CN38时间");
//				list.add(map);
				msgError.append("接收扫描时间不可早于CN38时间|");

			}

			//判断启运地点为空
			if (start_place  ==""  || start_place == null
			) {
				map.put("错误结构","总包号"+barcodes+"的启运地点为空");
//				list.add(map);
				msgError.append("启运地点为空|");
			}
			//判断启运时间为空
			if ( start_time   ==null
			) {
				map.put("错误结构","总包号"+barcodes+"的启运时间为空");
//				list.add(map);
				msgError.append("启运时间为空|");
			}
			//启运时间不可早于接受扫描时间
			if (scan_time!=null && start_time!=null && scan_time.after(start_time)){
				map.put("错误结构","总包号"+barcodes+"的启运时间不可早于接收扫描时间");
//				list.add(map);
				msgError.append("启运时间不可早于接收扫描时间|");

			}

			//判断到达地点为空
			if ( arrive_place  =="" || arrive_place ==null
			) {
				map.put("错误结构","总包号"+barcodes+"的到达地点为空");
//				list.add(map);
				msgError.append("到达地点为空|");
			}
			//判断目的地到达时间为空
			if ( arrive_time   ==null
			) {
				map.put("错误结构","总包号"+barcodes+"的目的地到达时间为空");
//				list.add(map);
				msgError.append("目的地到达时间为空|");
			}
			//目的地到达时间不可早于启运时间
			if (start_time!=null && arrive_time!=null && start_time.after(arrive_time)){
				map.put("错误结构","总包号"+barcodes+"的目的地到达时间不可早于启运时间");
//				list.add(map);
				msgError.append("目的地到达时间不可早于启运时间|");

			}
			//判断目的地交邮时间为空
			if ( hold_time   ==null
			) {
				map.put("错误结构","总包号"+barcodes+"的目的地交邮时间为空");
//				list.add(map);
				msgError.append("目的地交邮时间为空|");
			}
			//目的地交邮时间不可早于目的地到达时间
			if (arrive_time!=null && hold_time!=null && arrive_time.after(hold_time)){
				map.put("错误结构","总包号"+barcodes+"的目的地交邮时间不可早于目的地到达时间");
//				list.add(map);
				msgError.append("目的地交邮时间不可早于目的地到达时间|");
			}

			//判断收费路由为空
			if ( toll_route    =="" || toll_route == null
			) {
				map.put("错误结构","总包号"+barcodes+"的收费路由为空");
//				list.add(map);
				msgError.append("收费路由为空|");
			}
			//判断航班为空
			if ( flight_num   =="" || flight_num ==null
			) {
				map.put("错误结构","总包号"+barcodes+"的航班为空");
//				list.add(map);
				msgError.append("航班为空|");
			}
			//判断重量为空
			if (  weight  == null || BigDecimal.valueOf(0).compareTo(weight) ==0
			) {
				map.put("错误结构","总包号"+barcodes+"的重量为空");
//				list.add(map);
				msgError.append("重量为空|");
			}
			//判断费率为空
			if (  fee_rate  == null || BigDecimal.valueOf(0).compareTo(fee_rate) ==0
			) {
				map.put("错误结构","总包号"+barcodes+"的费率为空");
//				list.add(map);
				msgError.append("费率为空|");
			}
			//判断金额为空
			if ( amount  == null || BigDecimal.valueOf(0).compareTo(amount) ==0
			) {
				map.put("错误结构","总包号"+barcodes+"的金额为空");
//				list.add(map);
				msgError.append("金额为空|");
			}
			//判断币种为空
			if ( currency  =="" || currency ==null
			) {
				map.put("错误结构","总包号"+barcodes+"的币种为空");
//				list.add(map);
				msgError.append("币种为空|");
			}
			//判断账务时期为空
			if ( account_period  =="" || account_period ==null
			) {
				map.put("错误结构","总包号"+barcodes+"的账务时期为空");
//				list.add(map);
				msgError.append("账务时期为空|");
			}
			//判断运能编码为空
			if ( capacity_code  =="" || capacity_code== null
			) {
				map.put("错误结构","总包号"+barcodes+"的运能编码为空");
//				list.add(map);
				msgError.append("运能编码为空|");
			}
			//判断箱板类型为空
			if ( case_type   =="" || case_type == null
			) {
				map.put("错误结构","总包号"+barcodes+"的箱板类型为空");
//				list.add(map);
				msgError.append("箱板类型为空|");
			}


			ImportCarrierBillDetailDO importCarrierBillDetailDO = new ImportCarrierBillDetailDO();
			importCarrierBillDetailDO.setMailType(mail_type);
			importCarrierBillDetailDO.setCarrierName(carrier_name);
			importCarrierBillDetailDO.setBarcode(barcodes);
			importCarrierBillDetailDO.setCn38time(cn38time);
			importCarrierBillDetailDO.setScanTime(scan_time);
			importCarrierBillDetailDO.setStartPlace(start_place);
			importCarrierBillDetailDO.setStartTime(start_time);
			importCarrierBillDetailDO.setTransArrivePlace(trans_arrive_place);
			importCarrierBillDetailDO.setTransArriveTime(trans_arrive_time);
			importCarrierBillDetailDO.setTransStartTime(trans_start_time);
			importCarrierBillDetailDO.setArrivePlace(arrive_place);
			importCarrierBillDetailDO.setArriveTime(arrive_time);
			importCarrierBillDetailDO.setHoldTime(hold_time);
			importCarrierBillDetailDO.setTollRoute(toll_route);
			importCarrierBillDetailDO.setFlightNum(flight_num);
			importCarrierBillDetailDO.setWeight(weight);
			importCarrierBillDetailDO.setFeeRate(fee_rate);
			importCarrierBillDetailDO.setAmount(amount);
			importCarrierBillDetailDO.setCurrency(currency);
			importCarrierBillDetailDO.setAccountPeriod(account_period);
			importCarrierBillDetailDO.setBillNo(bill_no);
			importCarrierBillDetailDO.setRemark(remark);
			importCarrierBillDetailDO.setCapacityCode(capacity_code);
			importCarrierBillDetailDO.setCaseType(case_type);
			importCarrierBillDetailDO.setPackNum(pack_num);
			if(msgError.length()>0){ //导入失败
				importCarrierBillDetailDO.setImportState("2");
				importCarrierBillDetailDO.setImportContent(msgError.toString());
			}else{
				importCarrierBillDetailDO.setImportState("1");
			}
			importCarrierBillDetailService.exportNew(importCarrierBillDetailDO);


			//查询是否重复了承运商和财务日期
			boolean exists =carrierBillLogDOS.stream()
					.anyMatch(log ->log.getCarrierName().equals(carrier_name) && log.getAccountPeriod().equals(account_period));
			if(!exists){
				//添加日志
				CarrierBillLogDO carrierBillLogDO=new CarrierBillLogDO();
				carrierBillLogDO.setCarrierName(carrier_name);
				carrierBillLogDO.setAccountPeriod(account_period);
				carrierBillLogDO.setOperType("import");
				carrierBillLogDO.setCreatedBy(user.getUsername());
				carrierBillLogDO.setCreatedTime(new Date());
				carrierBillLogDOS.add(carrierBillLogDO);

				//增加承运商基础信息
				LambdaQueryWrapper<DictDO> queryWrapper = new LambdaQueryWrapper();
				queryWrapper.eq(DictDO::getValue,carrier_name);
				queryWrapper.eq(DictDO::getRemarks,account_period);
				queryWrapper.eq(DictDO::getType,"billcarrier");
				List<DictDO> dictDOS = dictDao.selectList(queryWrapper);
				if(dictDOS.size()==0){
					DictDO dict=new DictDO();
					dict.setValue(carrier_name);
					dict.setName(carrier_name);
					dict.setType("billcarrier");
					dict.setDescription("billcarrier");
					dict.setRemarks(account_period);
					dictService.save(dict);
				}
			}

		}

		//添加日志
		carrierBillLogService.saveBatch(carrierBillLogDOS);

		return list;
	}

	public void downloadExcel(HttpServletResponse response) {
		//特别注意：上面用file流读取文件只能在本地（windows）适用，项目打成jar包后再linux执行，会提示读取不到路径，linux想要读取jar包里的文件，只能通过以下流的形式来读
		String path = "file/2023年12月ZK012账单明细汇总 - CN66-0319.xlsx";
		String fileName="2023年12月ZK012账单明细汇总 - CN66-0319.xlsx";
		try {
			 fileName = URLEncoder.encode(fileName, "UTF-8");
			InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(path);
			OutputStream outputStream = response.getOutputStream();
			response.setContentType("application/x-download");
			response.addHeader("Content-Disposition", "attachment;filename=" + fileName);
			IOUtils.copy(inputStream, outputStream);
			outputStream.flush();
			inputStream.close();
			outputStream.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void downloadCsv(HttpServletResponse response) {
		//特别注意：上面用file流读取文件只能在本地（windows）适用，项目打成jar包后再linux执行，会提示读取不到路径，linux想要读取jar包里的文件，只能通过以下流的形式来读

		try {


			// 假设CSV文件位于服务器的某个固定位置
			String filePath = "file/账单明细导入模板.csv"; // 注意：这是服务器上的路径
			String fileName="账单明细导入模板.csv";
			// 设置响应内容类型
			response.setContentType("text/csv");
			fileName = URLEncoder.encode(fileName, "UTF-8");

			InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(filePath);
			OutputStream outputStream = response.getOutputStream();
			response.setContentType("Content-Disposition") ;
			response.addHeader("Content-Disposition", "attachment;filename=" + fileName);
			IOUtils.copy(inputStream, outputStream);
			outputStream.flush();
			inputStream.close();
			outputStream.close();


		} catch (Exception e) {
			e.printStackTrace();
		}


	}
}
