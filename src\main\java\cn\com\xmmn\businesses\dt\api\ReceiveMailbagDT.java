package cn.com.xmmn.businesses.dt.api;

import lombok.Builder;
import lombok.Data;

/**
 * 接收邮袋格式
 */
@Data
@Builder
public class ReceiveMailbagDT {

    /**
     *邮袋号
     */
    private String bagBarcode;

    /**
     *CN38号
     */
    private String deliveryBillNo;

    /**
     *起飞时间(yyyyMMddhhmm)
     */
    private String departureDatetime;

    /**
     * 容器号
     */
    private String containerNo;

    /**
     * 承运商
     */
    private String carrieName;

    /**
     * 邮袋接收时间起飞时间(yyyyMMddhhmm)
     */
    private String receiveDatetime;

}
