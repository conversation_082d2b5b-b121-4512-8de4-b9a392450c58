package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.XgImportMailReceiptDao;
import cn.com.xmmn.businesses.domain.XgImportMailReceiptDO;
import cn.com.xmmn.businesses.service.XgImportMailReceiptService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class XgImportMailReceiptServiceImpl extends ServiceImpl<XgImportMailReceiptDao, XgImportMailReceiptDO> implements XgImportMailReceiptService {


	@Override
	public void save(List<XgImportMailReceiptDO> xgImportMailReceiptDOList) {
		saveBatch(xgImportMailReceiptDOList);
	}
}
