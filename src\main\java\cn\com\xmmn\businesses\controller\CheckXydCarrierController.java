package cn.com.xmmn.businesses.controller;

import cn.com.xmmn.businesses.domain.BillCheckedDetailDO;
import cn.com.xmmn.businesses.domain.CarrierBillDetailDO;
import cn.com.xmmn.businesses.domain.CheckXydCarrierDO;
import cn.com.xmmn.businesses.domain.XydBillDetailDO;
import cn.com.xmmn.businesses.service.BillCheckedDetailService;
import cn.com.xmmn.businesses.service.CarrierBillDetailService;
import cn.com.xmmn.businesses.service.XydBillDetailService;
import cn.com.xmmn.common.utils.PageUtils;
import cn.com.xmmn.common.utils.Query;
import cn.com.xmmn.common.utils.R;
import cn.com.xmmn.common.utils.ShiroUtils;
import cn.com.xmmn.report.domain.LandMailBillsDo;
import cn.com.xmmn.system.domain.UserDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 新一代收入账单明细核对
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-20 13:12:29
 */
@Slf4j
@Controller
@RequestMapping("/check/checkXydCarrier")
public class CheckXydCarrierController {
	@Autowired
	private XydBillDetailService xydBillDetailService;
	@Autowired
	private CarrierBillDetailService carrierBillDetailService;
	@Autowired
	private BillCheckedDetailService billCheckedDetailService;
	@GetMapping()
	@RequiresPermissions("check:checkXydCarrier:checkXydCarrier")
	String XydBillDetail(Model model){
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.DATE, 1);// 设为当前月的1号
		calendar.add(Calendar.MONTH, 0);// 0表示当前月，-2就是当前月-2
		Date d = calendar.getTime();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
		String startDate = sdf.format(d);
		model.addAttribute("dateStart", startDate);
		return "check/checkXydCarrier/checkXydCarrier";
	}

	@ResponseBody
	@GetMapping("/list")
	@RequiresPermissions("check:checkXydCarrier:checkXydCarrier")
	public PageUtils list(@RequestParam Map<String, Object> params){
		//查询列表数据
        Query query = new Query(params);
		List<CheckXydCarrierDO> xydBillDetailList = xydBillDetailService.checkList(query);
		int total = xydBillDetailService.checkCount(query);
		PageUtils pageUtils = new PageUtils(xydBillDetailList,total);
		return pageUtils;
	}
	/**
	 * 保存核对结果
	 */
	@ResponseBody
	@PostMapping("/save")
	public R save(@RequestBody BillCheckedDetailDO billCheckedDetailDOS) {
		List<CheckXydCarrierDO> checkXydCarrierDOS = billCheckedDetailDOS.getRows();

		try {
			String checkState="1";
			CarrierBillDetailDO carrierBillDetailDO = new CarrierBillDetailDO();
			XydBillDetailDO xydBillDetailDO = new XydBillDetailDO();
			for (CheckXydCarrierDO checkXydCarrierDO : checkXydCarrierDOS) {
				//登录信息
				UserDO userDO = ShiroUtils.getUser();
				long userId = userDO.getUserId();
				String UserName = userDO.getUsername();
//				checkXydCarrierDO.setCheckTime(new Date());
//				checkXydCarrierDO.setCheckPersonId(String.valueOf(userId));
//				checkXydCarrierDO.setCheckPersonName(UserName);
//				billCheckedDetailService.save(checkXydCarrierDO);
				String barcode = checkXydCarrierDO.getBarcode();
				carrierBillDetailDO.setBarcode(barcode);
				carrierBillDetailDO.setCheckState(checkState);
                carrierBillDetailDO.setCheckTm(new Date());
                carrierBillDetailDO.setCheckBy(UserName);
				xydBillDetailDO.setBarcode(barcode);
				xydBillDetailDO.setCheckState(checkState);
				xydBillDetailDO.setCheckBy(UserName);
				xydBillDetailDO.setCheckTm(new Date());
				carrierBillDetailService.update(carrierBillDetailDO);
				xydBillDetailService.update(xydBillDetailDO);

			}
			return R.ok("保存成功");

		}catch (Exception e){
			e.printStackTrace();
			return R.error("保存失败");
		}
	}
	/**
	 * 确认结算
	 */
	@ResponseBody
	@PostMapping("/checkSettle")
	public R checkSettle(@RequestBody BillCheckedDetailDO billCheckedDetailDOS) {
		List<CheckXydCarrierDO> checkXydCarrierDOS = billCheckedDetailDOS.getRows();

		try {
			String checkState="1";
			CarrierBillDetailDO carrierBillDetailDO = new CarrierBillDetailDO();
			XydBillDetailDO xydBillDetailDO = new XydBillDetailDO();
			for (CheckXydCarrierDO checkXydCarrierDO : checkXydCarrierDOS) {
				//登录信息
				UserDO userDO = ShiroUtils.getUser();
				long userId = userDO.getUserId();
				String UserName = userDO.getUsername();
				checkXydCarrierDO.setCheckTime(new Date());
				checkXydCarrierDO.setCheckPersonId(String.valueOf(userId));
				checkXydCarrierDO.setCheckPersonName(UserName);
				billCheckedDetailService.save(checkXydCarrierDO);
				String barcode = checkXydCarrierDO.getBarcode();
				carrierBillDetailDO.setBarcode(barcode);
				carrierBillDetailDO.setCheckState(checkState);
				carrierBillDetailDO.setCheckTm(new Date());
				carrierBillDetailDO.setCheckBy(UserName);
				xydBillDetailDO.setBarcode(barcode);
				xydBillDetailDO.setCheckState(checkState);
				xydBillDetailDO.setCheckBy(UserName);
				xydBillDetailDO.setCheckTm(new Date());
				carrierBillDetailService.settleUpdate(carrierBillDetailDO);
				xydBillDetailService.update(xydBillDetailDO);

			}
			return R.ok("确认结算成功");

		}catch (Exception e){
			e.printStackTrace();
			return R.error("确认结算失败");
		}
	}

	/**
	 * 一键结算
	 */
	@ResponseBody
	@PostMapping("/allSettle")
	public R allSettle(@RequestParam Map<String, Object> params) {
		Query query = new Query(params);
		List<CheckXydCarrierDO> checkXydCarrierDOS = xydBillDetailService.checkList(query);

		try {
			String checkState="1";
			CarrierBillDetailDO carrierBillDetailDO = new CarrierBillDetailDO();
			XydBillDetailDO xydBillDetailDO = new XydBillDetailDO();
			for (CheckXydCarrierDO checkXydCarrierDO : checkXydCarrierDOS) {
				//登录信息
				UserDO userDO = ShiroUtils.getUser();
				long userId = userDO.getUserId();
				String UserName = userDO.getUsername();
				checkXydCarrierDO.setCheckTime(new Date());
				checkXydCarrierDO.setCheckPersonId(String.valueOf(userId));
				checkXydCarrierDO.setCheckPersonName(UserName);
				billCheckedDetailService.save(checkXydCarrierDO);
				String barcode = checkXydCarrierDO.getBarcode();
				carrierBillDetailDO.setBarcode(barcode);
				carrierBillDetailDO.setCheckState(checkState);
				carrierBillDetailDO.setCheckTm(new Date());
				carrierBillDetailDO.setCheckBy(UserName);
				xydBillDetailDO.setBarcode(barcode);
				xydBillDetailDO.setCheckState(checkState);
				xydBillDetailDO.setCheckBy(UserName);
				xydBillDetailDO.setCheckTm(new Date());
				carrierBillDetailService.settleUpdate(carrierBillDetailDO);
				xydBillDetailService.update(xydBillDetailDO);

			}
			return R.ok("一键结算成功");

		}catch (Exception e){
			e.printStackTrace();
			return R.error("一键结算失败");
		}
	}
	@RequestMapping(value = "/exportExcel",method = RequestMethod.GET)
	public void exportExcel(@RequestParam Map<String, Object> map, HttpServletResponse response) throws IOException {
		long startTime1 = System.currentTimeMillis();
		long startTime = System.currentTimeMillis();

		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").withZone(ZoneId.systemDefault());
		Instant instant = Instant.ofEpochMilli(startTime);
		String formattedDateTime = formatter.format(instant);
		log.info("导出请求开始时间: {}", formattedDateTime);

		List<CheckXydCarrierDO> portbatchList = xydBillDetailService.checkList(map);//账单
		//创建HSSFWorkbook对象,  excel的文档对象
		HSSFWorkbook wb = new HSSFWorkbook();
		// 创建sheet
		Sheet sheet = wb.createSheet("中国快递服务有限公司账单核对表");
		//表头字体
		Font headerFont = wb.createFont();
		headerFont.setFontName("宋体");
		headerFont.setFontHeightInPoints((short) 18);
		headerFont.setBold(true);
		headerFont.setColor(Font.COLOR_NORMAL);
		//正文字体
		Font contextFont = wb.createFont();
		contextFont.setFontName("宋体");
		contextFont.setFontHeightInPoints((short) 12);
		headerFont.setBold(true);
		//表头样式，左右上下居中
		CellStyle headerStyle = wb.createCellStyle();
		headerStyle.setFont(headerFont);
		// 左右居中
		headerStyle.setAlignment(HorizontalAlignment.CENTER);
		// 上下居中
		headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		headerStyle.setLocked(true);
		// 自动换行
		headerStyle.setWrapText(false);
		//单元格样式，右对齐
		CellStyle alignRightStyle = wb.createCellStyle();
		alignRightStyle.setFont(contextFont);
		alignRightStyle.setLocked(true);
		// 自动换行
		alignRightStyle.setWrapText(false);
		alignRightStyle.setAlignment(HorizontalAlignment.RIGHT);
		//单元格样式，左右上下居中 边框
		CellStyle commonStyle = wb.createCellStyle();
		commonStyle.setFont(contextFont);
		// 左右居中
		commonStyle.setAlignment(HorizontalAlignment.CENTER);
		// 上下居中
		commonStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		commonStyle.setLocked(true);
		// 自动换行
		commonStyle.setWrapText(false);
		// 行号
		int rowNum = 0;
		//设置列宽
		for (int i = 0; i < 43; i++) {
			sheet.setColumnWidth(i, 6000);
		}
		//第一行中国快递服务有限公司核账单
		Row r0 = sheet.createRow(rowNum++);
		r0.setHeight((short) 1000);
		Cell c0 = r0.createCell(0);
		c0.setCellValue("中国快递服务有限公司账单核对表");
		c0.setCellStyle(headerStyle);
		//合并单元格
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 43));
		//第二行 结算周期
		Row r1 = sheet.createRow(rowNum++);
		r1.setHeight((short) 500);
		Cell c1 = r1.createCell(0);
		c1.setCellValue("财务时期：" + map.get("dateStart"));

		//合并单元格
		sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 43));
		Row r2 = sheet.createRow(rowNum++);
		r2.setHeight((short) 500);
		String[] rowSecond = {"Barcode","匹配情况","检查运抵情况","邮件种类", "","","承运人名称","","", "CN38时间","", "接收扫描时间","","到达地点","","","目的地到达时间","","目的地交邮时间","","收费路由","","","航班","","","重量","","","费率","","","金额","","","币种","","","账务时期","","","运能编码"};

		sheet.addMergedRegion(new CellRangeAddress(2, 2, 3, 5));
		sheet.addMergedRegion(new CellRangeAddress(2, 2, 6, 8));
		sheet.addMergedRegion(new CellRangeAddress(2, 2, 9, 10));
		sheet.addMergedRegion(new CellRangeAddress(2, 2, 11, 12));
		sheet.addMergedRegion(new CellRangeAddress(2, 2, 13, 15));
		sheet.addMergedRegion(new CellRangeAddress(2, 2, 16, 17));
		sheet.addMergedRegion(new CellRangeAddress(2, 2, 18, 19));
		sheet.addMergedRegion(new CellRangeAddress(2, 2, 20, 22));
		sheet.addMergedRegion(new CellRangeAddress(2, 2, 23, 25));
		sheet.addMergedRegion(new CellRangeAddress(2, 2, 26, 28));
		sheet.addMergedRegion(new CellRangeAddress(2, 2, 29, 31));
		sheet.addMergedRegion(new CellRangeAddress(2, 2, 32, 34));
		sheet.addMergedRegion(new CellRangeAddress(2, 2, 35, 37));
		sheet.addMergedRegion(new CellRangeAddress(2, 2, 38, 40));
		sheet.addMergedRegion(new CellRangeAddress(2, 2, 41, 43));

		for (int i = 0; i < rowSecond.length; i++) {
			Cell tempCell = r2.createCell(i);
			tempCell.setCellValue(rowSecond[i]);
			tempCell.setCellStyle(commonStyle);
			tempCell.setCellStyle(commonStyle);
		}
		Row r3 = sheet.createRow(rowNum++);
		r3.setHeight((short) 500);
		String[] rowSecond2 = {"Barcode","匹配情况","检查运抵情况","最终匹配结果","导入","新一代邮件种类","最终匹配结果","导入", "新一代承运人名称","导入CN38时间","新一代CN38时间", "导入接收扫描时间","新一代接收扫描时间","最终匹配结果","导入","新一代到达地点","导入目的地到达时间", "新一代目的地到达时间","导入目的地交邮时间","新一代目的地交邮时间","最终匹配结果","导入","新一代收费路由","最终匹配结果","导入","新一代航班","最终匹配结果","导入","新一代重量","最终匹配结果","导入","新一代费率","最终匹配结果","导入","新一代金额","最终匹配结果","导入","新一代币种","最终匹配结果","导入","新一代账务时期","最终匹配结果","导入","新一代运能编码"};
		for (int i = 0; i < rowSecond2.length; i++) {
			Cell tempCell = r3.createCell(i);
			tempCell.setCellValue(rowSecond2[i]);
			tempCell.setCellStyle(commonStyle);
			tempCell.setCellStyle(commonStyle);
		}

		ArrayList<Object> list = new ArrayList<>();
		int s=0;

		for (int i = 0; i < portbatchList.size(); i++) {
			HashMap<String, Object> map1 = new HashMap<>();
			CheckXydCarrierDO vo= portbatchList.get(i);
			Row tempRow = sheet.createRow(rowNum++);
			tempRow.setRowStyle(commonStyle);
			//barcode
			String barcode = vo.getBarcode();
			tempRow.createCell(0).setCellValue(barcode!= null ? barcode : "");
			//匹配情况
			tempRow.createCell(1).setCellValue(vo.getMatchState()!= null ? vo.getMatchState() : "");
			//检查运抵情况
			tempRow.createCell(2).setCellValue(vo.getArriveState()!= null ? vo.getArriveState() : "");
			//导入邮件种类
			String mailType = vo.getMailType();
			//新一代邮件种类
			String xydMailType = vo.getXydMailType();
			if (mailType.equals(xydMailType)){
				tempRow.createCell(3).setCellValue("匹配成功");
			}else {
				list.add("邮件号"+barcode+"的邮件种类和新一代不一致");
				map1.put("第"+i+"列"+"匹配失败日志:",list);
				tempRow.createCell(3).setCellValue("匹配失败");
			}
			//导入邮件种类
			tempRow.createCell(4).setCellValue(mailType!= null ? mailType : "");
			//新一代邮件种类
			tempRow.createCell(5).setCellValue(xydMailType!= null ? xydMailType : "");

			//承运人名称
			String carrierName = vo.getCarrierName();
			//新一代承运人名称
			String xydCarrierName = vo.getXydCarrierName();

			if (carrierName.equals(xydCarrierName)){
				tempRow.createCell(6).setCellValue("匹配成功");
			}else {

				list.add("邮件号"+barcode+"的承运人名称不符");
				map1.put("第"+i+"列"+"匹配失败日志:",list);
				tempRow.createCell(6).setCellValue("匹配失败");
			}

			tempRow.createCell(7).setCellValue(carrierName);
			tempRow.createCell(8).setCellValue(xydCarrierName);
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
			//CN38时间
			Date cn38time = vo.getCn38time();
			//新一代CN38时间
			Date xydCn38time = vo.getXydCn38time();
			/*
			if (cn38time.equals(xydCn38time)){
				tempRow.createCell(7).setCellValue("匹配成功");
			}else {
				list.add("邮件号"+barcode+"的CN38时间和新一代不一致");
				map1.put("第"+i+"列"+"匹配失败日志:",list);
				tempRow.createCell(7).setCellValue("匹配失败");
			}*/

			tempRow.createCell(9).setCellValue(cn38time!= null ? sdf.format(cn38time) : "");
			tempRow.createCell(10).setCellValue(xydCn38time!= null ? sdf.format(xydCn38time) : "");

			//接收扫描时间
			Date scanTime = vo.getScanTime();
			//新一代接收扫描时间
			Date xydScanTime = vo.getXydScanTime();
            /*
			if (scanTime.equals(xydScanTime)){
				tempRow.createCell(10).setCellValue("匹配成功");
			}else {
				list.add("邮件号"+barcode+"的接收扫描时间和新一代不一致");
				map1.put("第"+i+"列"+"匹配失败日志:",list);

				tempRow.createCell(10).setCellValue("匹配失败");
			}
			*/

			tempRow.createCell(11).setCellValue(scanTime!= null ? sdf.format(scanTime) : "");
			tempRow.createCell(12).setCellValue(xydScanTime!= null ? sdf.format(xydScanTime) : "");

			//到达地点
			String arrivePlace = vo.getArrivePlace();
			//新一代到达地点
			String xydArrivePlace = vo.getXydArrivePlace();
			if (arrivePlace.equals(xydArrivePlace)){
				tempRow.createCell(13).setCellValue("匹配成功");
			}else {
				list.add("邮件号"+barcode+"的到达地点和新一代不一致");
				map1.put("第"+i+"列"+"匹配失败日志:",list);
				tempRow.createCell(13).setCellValue("匹配失败");
			}

			tempRow.createCell(14).setCellValue(arrivePlace);
			tempRow.createCell(15).setCellValue(xydArrivePlace);

			//目的地到达时间
			Date arriveTime = vo.getArriveTime();
			//新一代目的地到达时间
			Date xydArriveTime = vo.getXydArriveTime();
			/*
			if (arriveTime.equals(xydArriveTime)){
				tempRow.createCell(16).setCellValue("匹配成功");
			}else {
				list.add("邮件号"+barcode+"的目的地到达时间和新一代不一致");
				map1.put("第"+i+"列"+"匹配失败日志:",list);
				tempRow.createCell(16).setCellValue("匹配失败");
			}
			 */

			tempRow.createCell(16).setCellValue(arriveTime!= null ? sdf.format(arriveTime) : "");
			tempRow.createCell(17).setCellValue(xydArriveTime!= null ? sdf.format(xydArriveTime) : "");

			//目的地交邮时间
			Date holdTime = vo.getHoldTime();
			//新一代目的地交邮时间
			Date xydHoldTime = vo.getXydHoldTime();
			/*
			if (holdTime.equals(xydHoldTime)){
				tempRow.createCell(19).setCellValue("匹配成功");
			}else {
				map1.put("第"+i+"列"+"匹配失败日志:","邮件号"+barcode+"的目的地交邮时间和新一代不一致");
				tempRow.createCell(19).setCellValue("匹配失败");
			}

			 */

			tempRow.createCell(18).setCellValue(holdTime!= null ? sdf.format(holdTime) : "");
			tempRow.createCell(19).setCellValue(xydHoldTime!= null ? sdf.format(xydHoldTime) : "");

			//收费路由
			String tollRoute = vo.getTollRoute();
			//新一代收费路由
			String xydTollRoute = vo.getXydTollRoute();
			if (tollRoute.equals(xydTollRoute)){
				tempRow.createCell(20).setCellValue("匹配成功");
			}else {
				list.add("邮件号"+barcode+"的收费路由不符和新一代不一致");
				map1.put("第"+i+"列"+"匹配失败日志:",list);
				tempRow.createCell(20).setCellValue("匹配失败");
			}

			tempRow.createCell(21).setCellValue(tollRoute);
			tempRow.createCell(22).setCellValue(xydTollRoute);

			//航班
			String flightNum = vo.getFlightNum();
			//新一代航班
			String xydFlightNum = vo.getXydFlightNum();
			if (flightNum.equals(xydFlightNum)){
				tempRow.createCell(23).setCellValue("匹配成功");
			}else {
				list.add("邮件号"+barcode+"的航班不符和新一代不一致");
				map1.put("第"+i+"列"+"匹配失败日志:",list);
				tempRow.createCell(23).setCellValue("匹配失败");
			}

			tempRow.createCell(24).setCellValue(flightNum);
			tempRow.createCell(25).setCellValue(xydFlightNum);

			//重量
			BigDecimal weight = vo.getWeight();
			//新一代重量
			BigDecimal xydWeight = vo.getXydWeight();

			if (weight.equals(xydWeight)){
				tempRow.createCell(26).setCellValue("匹配成功");
			}else {
				list.add("邮件号"+barcode+"的重量和新一代不一致");
				map1.put("第"+i+"列"+"匹配失败日志:",list);
				tempRow.createCell(26).setCellValue("匹配失败");
			}

			tempRow.createCell(27).setCellValue(String.valueOf(weight)!= null ? String.valueOf(weight) : "");
			tempRow.createCell(28).setCellValue(String.valueOf(xydWeight)!= null ? String.valueOf(xydWeight) : "");

			//费率
			BigDecimal feeRate = vo.getFeeRate();
			//新一代费率
			BigDecimal xydFeeRate = vo.getXydFeeRate();

			if (feeRate.equals(xydFeeRate)){
				tempRow.createCell(29).setCellValue("匹配成功");
			}else {
				list.add("邮件号"+barcode+"的费率不符和新一代不一致");
				map1.put("第"+i+"列"+"匹配失败日志:",list);
				tempRow.createCell(29).setCellValue("匹配失败");
			}

			tempRow.createCell(30).setCellValue(String.valueOf(feeRate)!= null ? String.valueOf(feeRate) : "");
			tempRow.createCell(31).setCellValue(String.valueOf(xydFeeRate)!= null ? String.valueOf(xydFeeRate) : "");

			//金额
			BigDecimal amount = vo.getAmount();
			//新一代金额
			BigDecimal xydAmount = vo.getXydAmount();


			if (amount.equals(xydAmount)){
				tempRow.createCell(32).setCellValue("匹配成功");
			}else {
				list.add("邮件号"+barcode+"的金额和新一代不一致");
				map1.put("第"+i+"列"+"匹配失败日志:",list);
				tempRow.createCell(32).setCellValue("匹配失败");
			}

			tempRow.createCell(33).setCellValue(String.valueOf(amount)!= null ? String.valueOf(amount) : "");
			tempRow.createCell(34).setCellValue(String.valueOf(xydAmount)!= null ? String.valueOf(xydAmount) : "");

			//币种
			String currency = vo.getCurrency();
			//新一代币种
			String xydCurrency = vo.getXydCurrency();

			if (currency.equals(xydCurrency)){
				tempRow.createCell(35).setCellValue("匹配成功");
			}else {
				list.add("邮件号"+barcode+"的币种和新一代不一致");
				map1.put("第"+i+"列"+"匹配失败日志:",list);
				tempRow.createCell(35).setCellValue("匹配失败");
			}

			tempRow.createCell(36).setCellValue(currency!= null ? currency : "");
			tempRow.createCell(37).setCellValue(xydCurrency!= null ? xydCurrency : "");

			//账务时期
			String accountPeriod = vo.getAccountPeriod();
			//新一代账务时期
			String xydAccountPeriod = vo.getXydAccountPeriod();
			if (accountPeriod.equals(xydAccountPeriod)){
				tempRow.createCell(38).setCellValue("匹配成功");
			}else {
				list.add("邮件号"+barcode+"的账务时期和新一代不一致");
				map1.put("第"+i+"列"+"匹配失败日志:",list);
				tempRow.createCell(38).setCellValue("匹配失败");
			}

			tempRow.createCell(39).setCellValue(accountPeriod!= null ? accountPeriod : "");
			tempRow.createCell(40).setCellValue(xydAccountPeriod!= null ? xydAccountPeriod : "");

			//运能编码
			String capacityCode = vo.getCapacityCode();
			//新一代运能编码
			String xydCapacityCode = vo.getXydCapacityCode();
			if (capacityCode.equals(xydCapacityCode)){
				tempRow.createCell(41).setCellValue("匹配成功");
			}else {
				list.add("邮件号"+barcode+"的运能编码不符和新一代不一致");
				map1.put("第"+i+"列"+"匹配失败日志:",list);
				tempRow.createCell(41).setCellValue("匹配失败");
			}

			tempRow.createCell(42).setCellValue(capacityCode!= null ? capacityCode : "");
			tempRow.createCell(43).setCellValue(xydCapacityCode!= null ? xydCapacityCode : "");

             if(map1.size()>0){
                 s++;
             }

		}
		//最后一行 结算失败原因
		instant = Instant.ofEpochMilli(startTime);
		formattedDateTime = formatter.format(instant);
		log.info("导出记录循环结束时间: {}", formattedDateTime);

		Row r4 = sheet.createRow(rowNum++);
		r4.setHeight((short) 500);
		Cell c4 = r4.createCell(0);
		c4.setCellValue("本次核对"+portbatchList.size()+"条数据，其中有"+s+"未匹配成功！" );


		//导出
		response.setContentType("application/vnd.ms-excel");
		response.setCharacterEncoding("UTF-8");
		// 文件名
		String fileName =  map.get("dateStart")  + "账单核对表" + ".xlsx";
		try {
			fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
		OutputStream stream = null;
		try {
			stream = response.getOutputStream();
		} catch (IOException e) {
			e.printStackTrace();
		}

		try {
			instant = Instant.ofEpochMilli(startTime);
			formattedDateTime = formatter.format(instant);
			log.info("导出写入文件开始时间: {}", formattedDateTime);

			if (null != wb && null != stream) {
				wb.write(stream);
			}

			instant = Instant.ofEpochMilli(startTime);
			formattedDateTime = formatter.format(instant);
			log.info("导出写入文件结束时间: {}", formattedDateTime);
		} catch (Exception e) {
			log.info("excel文档导出错误-异常信息：" + e);
		} finally {
			long endTime = System.currentTimeMillis();
			instant = Instant.ofEpochMilli(endTime);
			formattedDateTime = formatter.format(instant);
			log.info("请求结束时间: {}", formattedDateTime);

			long duration = endTime - startTime1;
			log.info("请求处理时间: {} 毫秒", duration);
			if (stream != null) {
				try {
					stream.flush();  // 刷新输出流
					stream.close();  // 关闭输出流
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}

    /**
     * 删除
     */
    @PostMapping( "/barcodesRemove")
    @ResponseBody
    @RequiresPermissions("check:checkXydCarrier:checkXydCarrier")
    public R remove(@RequestParam("barcodes[]") String[] barcodes){
        xydBillDetailService.barcodesRemove(barcodes);
        return R.ok();
    }
	/*@GetMapping("/add")
	@RequiresPermissions("system:xydBillDetail:add")
	String add(){
	    return "system/xydBillDetail/add";
	}

	@GetMapping("/edit/{id}")
	@RequiresPermissions("system:xydBillDetail:edit")
	String edit(@PathVariable("id") Integer id,Model model){
		XydBillDetailDO xydBillDetail = xydBillDetailService.get(id);
		model.addAttribute("xydBillDetail", xydBillDetail);
	    return "system/xydBillDetail/edit";
	}
	
	*/
	/**
	 * 修改
	 *//*
	@ResponseBody
	@RequestMapping("/update")
	@RequiresPermissions("system:xydBillDetail:edit")
	public R update( XydBillDetailDO xydBillDetail){
		xydBillDetailService.update(xydBillDetail);
		return R.ok();
	}
	
	*//**
	 * 删除
	 *//*
	@PostMapping( "/remove")
	@ResponseBody
	@RequiresPermissions("system:xydBillDetail:remove")
	public R remove( Integer id){
		if(xydBillDetailService.remove(id)>0){
		return R.ok();
		}
		return R.error();
	}*/

	
}
