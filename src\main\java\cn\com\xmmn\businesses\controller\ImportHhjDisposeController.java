package cn.com.xmmn.businesses.controller;

import cn.com.xmmn.businesses.domain.ImportHhjDisposeDO;
import cn.com.xmmn.businesses.dt.ImportHhjDisposeExcelDT;
import cn.com.xmmn.businesses.service.ImportHhjDisposeService;
import cn.com.xmmn.businesses.service.ImportMailService;
import cn.com.xmmn.businesses.vo.ImportMailShouldListVO;
import cn.com.xmmn.common.strategy.ImportHhjDisposeExcelStrategy;
import cn.com.xmmn.common.utils.PageUtils;
import cn.com.xmmn.common.utils.Query;
import cn.hutool.core.convert.Convert;
import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 进口互换局时限统计表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-20 15:02:00
 */
@Controller
@RequestMapping("/import/importHhjDispose")
@Slf4j
public class ImportHhjDisposeController {


    @Autowired
    private ImportHhjDisposeService importHhjDisposeService;
    @Autowired
    private ImportMailService importMailService;

    @GetMapping()
    @RequiresPermissions("importHhjDispose:importHhjDispose")
    String importHhjDispose() {
        return "importHhjDispose/importHhjDispose";
    }

    @ResponseBody
    @GetMapping("/list")
    @RequiresPermissions("importHhjDispose:importHhjDispose")
    public PageUtils list(@RequestParam Map<String, Object> params) {
        //查询列表数据
        Query query = new Query(params);
        List<ImportHhjDisposeDO> importHhjDisposeList = importHhjDisposeService.list(query);
        int total = importHhjDisposeService.count(query);
        PageUtils pageUtils = new PageUtils(importHhjDisposeList, total);
        return pageUtils;
    }


    /**
     * 跳转到明细页面
     *
     * @return
     */
    @GetMapping("/pageDetail")
    @RequiresPermissions("importHhjDispose:importHhjDispose")
    String pageDetail(HttpServletRequest request, Model model) {

        //跳转页面: shouldHandled=应处理邮件明细，oaoHandled=开拆量及逾期开拆邮件明细,cpeHandled=待通关量邮件明细,OutMail Volume封发量邮件明细
        String pageHandled = request.getParameter("pageHandled");
        //机构代码
        String orgNo = request.getParameter("orgNo");
        //检索互换局接收开始时间
        String ptTimeStart = request.getParameter("ptTimeStart").substring(0,10);
        //检索互换局接收接收时间
        String ptTimeEnd = request.getParameter("ptTimeEnd").substring(0,10);

        model.addAttribute("orgNo", orgNo);
        model.addAttribute("ptTimeStart", ptTimeStart);
        model.addAttribute("ptTimeEnd", ptTimeEnd);
        model.addAttribute("pageHandled", pageHandled);
        return "importHhjDispose/" + pageHandled;
    }


    @ResponseBody
    @GetMapping("/detailTable")
    @RequiresPermissions("importHhjDispose:importHhjDispose")
    public PageUtils detailTable(@RequestParam Map<String, Object> params) {
        log.info("进口互换局时限统计表详情table类型:{}", params.get("pageHandled"));
        //查询列表数据
        Query query = new Query(params);
        //应处理量邮件明细
        List<ImportMailShouldListVO> list = importMailService.getShouldList(query);
        int total = importMailService.getShouldCount(query);
        PageUtils pageUtils = new PageUtils(list, total);
        return pageUtils;
    }

    /**
     * 导出数据
     *
     * @param map
     * @param response 入参
     * @return void
     **/
    @RequestMapping(value = "/exportExcel", method = RequestMethod.GET)
    public void exportExcel(@RequestParam Map<String, Object> map, HttpServletResponse response) throws IOException {

        List<ImportHhjDisposeDO> importHhjDisposeList = importHhjDisposeService.list(map);

        List<ImportHhjDisposeExcelDT>  disposeExcelDTS=new ArrayList<>();
        for (ImportHhjDisposeDO importHhjDisposeDO:importHhjDisposeList){
            ImportHhjDisposeExcelDT importHhjDisposeExcelDT=new ImportHhjDisposeExcelDT();

            BeanUtils.copyProperties(importHhjDisposeDO,importHhjDisposeExcelDT);
            disposeExcelDTS.add(importHhjDisposeExcelDT);
        }
        //导出
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");
        // 文件名
        String fileName = map.get("dateStart") + "进口互换局处理时限汇总表" + ".xlsx";
        try {
            fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
        try {
         EasyExcel.write(response.getOutputStream(), ImportHhjDisposeExcelDT.class)
                    .sheet("进口互换局处理时限汇总表")
                    .registerWriteHandler(new ImportHhjDisposeExcelStrategy(disposeExcelDTS))
                    .doWrite(disposeExcelDTS);
        } catch (Exception e) {
            log.info("excel文档导出错误-异常信息：" + e);
            e.printStackTrace();
        }
    }

    /**
     * 导出详情数据
     *
     * @param map
     * @return void
     **/
    @RequestMapping(value = "/detailTable/exportExcel", method = RequestMethod.GET)
    public void exportDetailsExcel(@RequestParam Map<String, Object> map,HttpServletResponse response) {

        importHhjDisposeService.handleDetailsExport(map,response);

    }
}
