package cn.com.xmmn.businesses.service;

import cn.com.xmmn.businesses.domain.FeedbackRecordsDO;
import cn.com.xmmn.businesses.domain.FeedbackRecordsPushLogDO;
import org.springframework.scheduling.annotation.Async;

import java.util.List;
import java.util.Map;

/**
 * 信息反馈
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-08 15:45:24
 */
public interface FeedbackRecordsService {
	
	FeedbackRecordsDO get(Integer id);
	
	List<FeedbackRecordsDO> list(Map<String, Object> map);
	
	int count(Map<String, Object> map);
	
	int save(FeedbackRecordsDO feedbackRecords);
	
	int update(FeedbackRecordsDO feedbackRecords);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);

	/**
	 *  批量推送
	 * @param ids
	 * @param pushType 推送类型:1接收2启运3运抵4交邮
	 */

    void batchPush(Integer[] ids, Integer pushType);

	/**
	 * 信息反馈推送日志分页列表
	 * @param map
	 * @return
	 */
    List<FeedbackRecordsPushLogDO> pushLogList(Map<String, Object> map);

	/**
	 * 信息反馈推送日志总数量
	 * @param map
	 * @return
	 */
	int pushLogCount(Map<String, Object> map);

	/**
	 *  推送接收状态未推送的记录
	 */
	@Async("asyncTaskPool")
	void pushReceiveStatus();


	/**
	 *  推送启运状态未推送的记录
	 */
	@Async("asyncTaskPool")
	void pushDepartureStatus();


	/**
	 *  推送运抵状态未推送的记录
	 */
	@Async("asyncTaskPool")
	void pushDeliveryStatus();

	/**
	 * 推送交邮状态未推送的记录
	 */
	@Async("asyncTaskPool")
	void pushPostStatus();
}
