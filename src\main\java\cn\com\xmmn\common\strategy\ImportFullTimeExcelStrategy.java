package cn.com.xmmn.common.strategy;

import cn.com.xmmn.businesses.vo.ImportFullTimeListVO;
import cn.com.xmmn.common.utils.Convert;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 进口全程时限监控统计合并单元格策略
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-02 15:02:00
 */
public class ImportFullTimeExcelStrategy {
    // 创建Excel sheet并设置样式
    public static void createHeader(Sheet sheet) {
        // 创建行
        Row row0 = sheet.createRow(0);  // 第0行 - 标题行
        Row row1 = sheet.createRow(1);  // 第1行 - 列名行

        // 创建单元格样式（居中对齐）
        CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());  // 设置背景颜色
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);


        // 创建第二行的样式（列名行）
        CellStyle colNameStyle = sheet.getWorkbook().createCellStyle();
        colNameStyle.setAlignment(HorizontalAlignment.CENTER);
        colNameStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        colNameStyle.setBorderTop(BorderStyle.THIN);
        colNameStyle.setBorderBottom(BorderStyle.THIN);
        colNameStyle.setBorderLeft(BorderStyle.THIN);
        colNameStyle.setBorderRight(BorderStyle.THIN);

        // 合并单元格并填充内容
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 0)); // 客户名称
        createCell(row0, 0, "客户名称", headerStyle);

        sheet.addMergedRegion(new CellRangeAddress(0, 0, 1, 3)); // 收寄环节
        createCell(row0, 1, "收寄环节", headerStyle);
        createCell(row1, 1, "邮件号码", colNameStyle);
        createCell(row1, 2, "香港已形成总包时间", colNameStyle);
        createCell(row1, 3, "香港已形成总包条码", colNameStyle);

        sheet.addMergedRegion(new CellRangeAddress(0, 0, 4, 5)); // 互换局接收环节
        createCell(row0, 4, "互换局接收环节", headerStyle);
        createCell(row1, 4, "互换局进口接收扫描时间", colNameStyle);
        createCell(row1, 5, "互换局开拆时间", colNameStyle);

        sheet.addMergedRegion(new CellRangeAddress(0, 0, 6, 9)); // 清关环节
        createCell(row0, 6, "清关环节", headerStyle);
        createCell(row1, 6, "交海关待验", colNameStyle);
        createCell(row1, 7, "海关待验开拆", colNameStyle);
        createCell(row1, 8, "封报关行时间", colNameStyle);
        createCell(row1, 9, "报关行出库时间", colNameStyle);

        sheet.addMergedRegion(new CellRangeAddress(0, 0, 10, 11)); // 互换局发运环节
        createCell(row0, 10, "互换局发运环节", headerStyle);
        createCell(row1, 10, "互换局封发时间", colNameStyle);
        createCell(row1, 11, "互换局发运时间", colNameStyle);

        sheet.addMergedRegion(new CellRangeAddress(0, 0, 12, 13)); // 国内环节
        createCell(row0, 12, "国内环节", headerStyle);
        createCell(row1, 12, "国内处理中心接收时间", colNameStyle);
        createCell(row1, 13, "投递时间", colNameStyle);

        sheet.addMergedRegion(new CellRangeAddress(0, 1, 14, 14)); // 发运-接收（天数）
        createCell(row0, 14, "发运-接收（天数）", headerStyle);

        sheet.addMergedRegion(new CellRangeAddress(0, 1, 15, 15)); // 开拆-封发（天数）
        createCell(row0, 15, "开拆-封发（天数）", headerStyle);

        sheet.addMergedRegion(new CellRangeAddress(0, 1, 16, 16)); // 封发-发运（天数）
        createCell(row0, 16, "封发-发运（天数）", headerStyle);

        sheet.addMergedRegion(new CellRangeAddress(0, 1, 17, 17)); // 发运-投递（天数）
        createCell(row0, 17, "发运-投递（天数）", headerStyle);

        sheet.addMergedRegion(new CellRangeAddress(0, 1, 18, 18)); // 全程时限（出仓-投递）天数
        createCell(row0, 18, "全程时限（接收-投递）天数", headerStyle);

        sheet.addMergedRegion(new CellRangeAddress(0, 1, 19, 19)); // 超时预警
        createCell(row0, 19, "超时预警", headerStyle);


        // 设置列宽
        sheet.setColumnWidth(0, 40 * 256);  // 客户名称
        sheet.setColumnWidth(1, 20 * 256);  // 收寄环节
        sheet.setColumnWidth(2, 20 * 256);  // 收寄环节
        sheet.setColumnWidth(3, 20 * 256);  // 收寄环节
        sheet.setColumnWidth(4, 22 * 256);  // 收寄环节
        sheet.setColumnWidth(5, 22 * 256);  // 互换局接收环节
        sheet.setColumnWidth(6, 22 * 256);  // 互换局接收环节
        sheet.setColumnWidth(7, 22 * 256);  // 互换局接收环节
        sheet.setColumnWidth(8, 22 * 256);  // 清关环节
        sheet.setColumnWidth(9, 22 * 256);  // 清关环节
        sheet.setColumnWidth(10, 22 * 256);  // 清关环节
        sheet.setColumnWidth(11, 22 * 256);  // 清关环节
        sheet.setColumnWidth(12, 22 * 256);  // 清关环节
        sheet.setColumnWidth(13, 20 * 256); // 互换局发运环节
        sheet.setColumnWidth(14, 20 * 256); // 互换局发运环节
        sheet.setColumnWidth(15, 20 * 256); // 互换局发运环节
        sheet.setColumnWidth(16, 20 * 256); // 国内环节
        sheet.setColumnWidth(17, 20 * 256); // 国内环节
        sheet.setColumnWidth(18, 20 * 256); // 国内环节
        sheet.setColumnWidth(19, 20 * 256); // 发运-接收（天数）
        sheet.setColumnWidth(20, 20 * 256); // 开拆-封发（天数）
        sheet.setColumnWidth(21, 20 * 256); // 封发-发运（天数）

    }

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    // 格式化日期字段
    private static String formatDate(Date date) {
        return date != null ? DATE_FORMAT.format(date) : ""; // 如果为 null 返回空字符串
    }
    // 填充数据行
    public static void fillDataRows(Sheet sheet, List<ImportFullTimeListVO> dataList) {
        int rowIndex = 2; // 数据从第三行开始填充

        for (ImportFullTimeListVO data : dataList) {
            Row row = sheet.createRow(rowIndex++);

            // 填充数据
            createCell(row, 0, data.getCustName(), null); // 客户名称

            // 收寄环节
            createCell(row, 1, data.getItemId(), null);   // 邮件号码
            createCell(row, 2, formatDate(data.getContractTime()), null); // 香港已形成总包时间
            createCell(row, 3, data.getBarCode(), null); // 香港已形成总包条码

            // 互换局接收环节
            createCell(row, 4, formatDate(data.getChOrgRecvTime()), null); // 互换局进口接收扫描时间
            createCell(row, 5, formatDate(data.getChOrgOpenTime()), null); // 互换局开拆时间

            // 清关环节
            createCell(row, 6, formatDate(data.getWaitTestTime()), null);  // 交海关待验
            createCell(row, 7, formatDate(data.getWaitOpenTime()), null);  // 海关待验开拆
            createCell(row, 8, formatDate(data.getDisBrokerTime()), null); // 封报关行时间
            createCell(row, 9, formatDate(data.getBrokerOutTime()), null); // 报关行出库时间

            // 互换局发运环节
            createCell(row, 10, formatDate(data.getChOrgDisTime()), null);  // 互换局封发时间
            createCell(row, 11, formatDate(data.getChOrgSendTime()), null); // 互换局发运时间

            // 国内环节
            createCell(row, 12, formatDate(data.getProcessRecvTime()), null); // 国内处理中心接收时间
            createCell(row, 13, formatDate(data.getDeliverTime()), null); // 投递时间

            // 时间字段：发运-接收（天数），开拆-封发（天数），封发-发运（天数），发运-投递（天数），全程时限（出仓-投递）天数
            createCell(row, 14, Convert.toStr(data.getDayShipmentToReceipt()), null); // 发运-接收（天数）
            createCell(row, 15, Convert.toStr(data.getDayOpenToSeal()), null); // 开拆-封发（天数）
            createCell(row, 16, Convert.toStr(data.getDaySealToShipment()), null); // 封发-发运（天数）
            createCell(row, 17, Convert.toStr(data.getDayShipmentToDelivery()), null); // 发运-投递（天数）
            createCell(row, 18, Convert.toStr(data.getTotalDays()), null); // 全程时限（出仓-投递）天数
            // 超时预警字段
            createCell(row, 19, Convert.toStr(data.getStatus()), null); // 超时预警
        }
    }

    private static void createCell(Row row, int column, String value, CellStyle style) {
        Cell cell = row.createCell(column);
        cell.setCellValue(value);
        if (style != null) {
            cell.setCellStyle(style); // 设置样式（居中对齐）
        }
    }
}
