package cn.com.xmmn.businesses.service;

import cn.com.xmmn.businesses.domain.CarrierBillDetailDO;


import java.util.List;
import java.util.Map;

/**
 * 承运商账单明细
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-20 13:12:29
 */
public interface CarrierBillDetailService {
	
	CarrierBillDetailDO get(Integer id);
	
	List<CarrierBillDetailDO> list(Map<String, Object> map);
	
	int count(Map<String, Object> map);
	
	int save(CarrierBillDetailDO carrierBillDetail);
	
	int update(CarrierBillDetailDO carrierBillDetail);
	int settleUpdate(CarrierBillDetailDO carrierBillDetail);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);
}
