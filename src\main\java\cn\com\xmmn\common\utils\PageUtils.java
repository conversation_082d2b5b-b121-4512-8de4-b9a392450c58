package cn.com.xmmn.common.utils;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
@Data
public class  PageUtils implements Serializable {
	private static final long serialVersionUID = 1L;
	private Integer total;
	private List<?> rows;

	public PageUtils(List<?> list, Integer total) {
		this.rows = list;
		this.total = (total == null || total <0)?0:total;
	}
	//封发至交航合计数
	//已封发
	private Integer yff_hj;
	//已交航
	private Integer yjh_hj;
	//封发-交航平均时长
	private String sc_hj;

    //邮袋接收合计数
	//已交航袋数
	private Integer devNum;
	//交航总重量(kg)
	private BigDecimal totalWeight;
	//承运商已接收袋数
	private Integer receiveNum;
	//承运商接收总重量（kg）
	private BigDecimal receiverTotalWeight;
	//平均接收处理时长（天)
	private BigDecimal avgReceiverTime;
	//承运商未接收袋数
	private Integer notReceiverNum;
	//承运商未接收总重量（kg）
	private BigDecimal notReceiverTotalWeight;
	//异常袋数
	private Integer exceptionBagNum;

	//启运环节
	//承运商已启运袋数
	private Integer departNum;

	//正常启运邮袋（2小时内，含12小时）
	private Integer normalDepartNum;

	//超时启运邮袋（12至24小时）
	private Integer overtime12to24DepartNum;

	//超时启运邮袋（24小时以上）
	private Integer overtime24PlusDepartNum;

	//承运商未启运袋数
	private Integer notDepartNum;

	//启运-运抵环节
	//在途邮袋数
	private Integer inTransitMailbagNum;
	//运抵邮袋数
	private Integer arrivedMailbagNum;

	//交邮环节

	// 承运商运抵袋数
	private Integer arriveNum;

	// 交邮袋数
	private Integer postNum;

	// 正常交邮邮袋（12小时内，含12小时）
	private Integer normalPostNum;

	// 超时交邮邮袋（12至24小时）
	private Integer overPostNum;

	// 承运商未启运袋数
	private Integer notPostNum;


	private BigDecimal avgReceiveToPostTime;
	private Integer onTimeNum;
	private Integer overTimeNum;
	private BigDecimal timeLimitRate;



}
