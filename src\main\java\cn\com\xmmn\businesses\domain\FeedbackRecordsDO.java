package cn.com.xmmn.businesses.domain;

import cn.com.xmmn.businesses.vo.StatusIntConverter;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;



/**
 * 信息反馈
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-08 15:45:24
 */
@TableName(value = "t_feedback_records")
@HeadRowHeight(value = 30) // 头部行高
@ContentRowHeight(value = 25) // 内容行高
@ColumnWidth(value = 20) // 列宽
@HeadFontStyle(fontName = "宋体", fontHeightInPoints = 11) // 头部字体样式
@Data
public class FeedbackRecordsDO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//ID;主键
	@ExcelIgnore
	private Integer id;
	//邮袋条码
	@ExcelProperty("邮袋条码")
	private String bagBarCode;
	//航班号
	@ExcelProperty("航班号")
	private String vehicleCode;
	//CN38号
	@ExcelIgnore
	private String deliverBillNo;
	//承运商
	@ExcelIgnore
	private String carrierName;
	//容器号
	@ExcelProperty("容器号")
	private String containerNumbers;
	//邮袋接收时间
	@ExcelProperty("接收时间")
	private Date receiceDatetime;
	//起飞时间
	@ExcelIgnore
	private Date departureDatetime;
	//创建时间
	@ExcelIgnore
	private Date createTime;
	//启运时间（航空公司启运）
	@ExcelProperty("启运时间")
	private Date departureTime;
	//运抵时间（飞机到达进港）
	@ExcelProperty("运抵时间")
	private Date deliveryTime;
	//交邮时间（送交境外航站（交邮处理））
	@ExcelProperty("交邮时间")
	private Date postTime;
	//航空公司ID（代码前2位）
	@ExcelIgnore
	private String opOrgCode;
	//航空公司名称
	@ExcelIgnore
	private String opOrgName;
	//邮件类型(默认 3-国际邮件)
	@ExcelIgnore
	private String traceType;
	//操作员ID
	@ExcelIgnore
	private String operatorNo;
	//操作员名称
	@ExcelIgnore
	private String operatorName;
	//扫描标志(P总包 C集装箱默认P总包)
	@ExcelIgnore
	private String containerType;
	//承运商类型(F航空,S海运,T火车陆运,C汽车陆运,默认F)
	@ExcelIgnore
	private String transClass;
	//操作口岸三位操作码
	@ExcelIgnore
	private String opPortCode;
	//启运站位操作码
	@ExcelIgnore
	private String departPort;
	//卸运站
	@ExcelIgnore
	private String offloadPort;
	//封志条码
	@ExcelIgnore
	private String dispBarCode;
	//1包机 2 包箱 3散仓 4 高板 5 低板 6 平高板 7 预留版型1   8预留版型2
	@ExcelIgnore
	private String executionMode;
	//是否混板1 是 2 否
	@ExcelIgnore
	private Integer isHybrid;
	//航司运单号
	@ExcelIgnore
	private String airlinebillNo;


	//承运商
	@ExcelIgnore
	private String deviceType;

	//接收推送：0未推送1推送成功2推送失败
	@ExcelProperty(value = "接收推送状态",converter= StatusIntConverter.class)
	private Integer isPushReceice;
	//启运推送：0未推送1推送成功2推送失败
	@ExcelProperty(value = "启运推送状态",converter= StatusIntConverter.class)
	private Integer isPushDeparture;
	//运抵推送：0未推送1推送成功2推送失败
	@ExcelProperty(value = "运抵推送状态",converter= StatusIntConverter.class)
	private Integer isPushDelivery;
	//交邮推送：0未推送1推送成功2推送失败
	@ExcelProperty(value = "交邮推送状态",converter= StatusIntConverter.class)
	private Integer isPushPost;


}
