package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.config.InterfaceConfig;
import cn.com.xmmn.businesses.dao.HkNoticeAwordDao;
import cn.com.xmmn.businesses.domain.HkNoticeAwordDO;
import cn.com.xmmn.businesses.dt.api.HkNoticeAwordResponseDT;
import cn.com.xmmn.businesses.service.HkNoticeAwordService;
import cn.com.xmmn.businesses.utils.HttpClientUtil;
import cn.com.xmmn.common.exception.BusinessException;
import cn.com.xmmn.common.utils.DateUtils;
import cn.com.xmmn.common.utils.EncryptUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 香港成交通知单接收表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-31
 */
@Slf4j
@Service("hkNoticeAwordService")
public class HkNoticeAwordServiceImpl implements HkNoticeAwordService {
    
    @Autowired
    private HkNoticeAwordDao hkNoticeAwordDao;
    
    @Autowired
    private InterfaceConfig interfaceConfig;

    @Override
    public HkNoticeAwordDO get(Integer id) {
        return hkNoticeAwordDao.get(id);
    }

    @Override
    public List<HkNoticeAwordDO> list(Map<String, Object> map) {
        return hkNoticeAwordDao.list(map);
    }

    @Override
    public int count(Map<String, Object> map) {
        return hkNoticeAwordDao.count(map);
    }

    @Override
    public int save(HkNoticeAwordDO hkNoticeAword) {
        hkNoticeAword.setCreateTime(new Date());
        hkNoticeAword.setUpdateTime(new Date());
        return hkNoticeAwordDao.save(hkNoticeAword);
    }

    @Override
    public int update(HkNoticeAwordDO hkNoticeAword) {
        hkNoticeAword.setUpdateTime(new Date());
        return hkNoticeAwordDao.update(hkNoticeAword);
    }

    @Override
    public int remove(Integer id) {
        return hkNoticeAwordDao.remove(id);
    }

    @Override
    public int batchRemove(Integer[] ids) {
        return hkNoticeAwordDao.batchRemove(ids);
    }

    @Override
    public String manualRequestBill(String yearMonthStr) {
        log.info("手动调用获取成交通知单数据，参数：{}", yearMonthStr);
        return requestBillData(yearMonthStr);
    }

    @Override
    public String scheduledRequestBill() {
        // 获取上个月的年月字符串
        LocalDate lastMonth = LocalDate.now().minusMonths(1);
        String yearMonthStr = lastMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        log.info("定时任务调用获取成交通知单数据，参数：{}", yearMonthStr);
        return requestBillData(yearMonthStr);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String requestBillData(String yearMonthStr) {
        try {
            // 参数验证
            if (yearMonthStr == null || yearMonthStr.trim().isEmpty()) {
                throw new BusinessException("年月参数不能为空");
            }
            
            // 验证年月格式
            if (!yearMonthStr.matches("\\d{4}-\\d{2}")) {
                throw new BusinessException("年月格式错误，应为yyyy-MM格式");
            }

            // 构建请求参数
            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put("yearMonthStr", yearMonthStr);
            String requestJson = JSON.toJSONString(requestParams);
            
            // 调用第三方接口
            String url = interfaceConfig.getHkNoticePath() + "/api/requestBill";
            log.info("调用第三方接口，URL：{}，参数：{}", url, requestJson);
            
            // 构建请求参数Map
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("data", requestJson);

            String encryptedResponse = HttpClientUtil.doPost(url, paramMap, null);
            log.info("第三方接口返回加密数据：{}", encryptedResponse);
            
            // 解密响应数据
            String decryptedData = EncryptUtils.aesSha1prngDecrypt(encryptedResponse);
            log.info("解密后数据：{}", decryptedData);
            
            // 解析JSON响应
            JSONObject responseJson = JSON.parseObject(decryptedData);
            JSONArray dataArray = responseJson.getJSONArray("data");
            
            if (dataArray == null || dataArray.isEmpty()) {
                log.warn("第三方接口返回数据为空");
                return "获取数据成功，但数据为空";
            }
            
            // 转换并保存数据
            List<HkNoticeAwordDO> saveList = new ArrayList<>();
            int duplicateCount = 0;
            
            for (int i = 0; i < dataArray.size(); i++) {
                JSONObject item = dataArray.getJSONObject(i);
                HkNoticeAwordResponseDT responseDto = JSON.parseObject(item.toJSONString(), HkNoticeAwordResponseDT.class);
                
                // 转换为DO对象
                HkNoticeAwordDO noticeAword = convertResponseToEntity(responseDto);
                
                // 检查是否存在重复数据
                String effectiveDateStr = new SimpleDateFormat("yyyy-MM-dd").format(noticeAword.getEffectiveDate());
                int existCount = hkNoticeAwordDao.countByCapacityCodeAndDate(noticeAword.getCapacityCode(), effectiveDateStr);
                
                if (existCount > 0) {
                    log.warn("发现重复数据，运能编码：{}，生效日期：{}", noticeAword.getCapacityCode(), effectiveDateStr);
                    duplicateCount++;
                    continue;
                }
                
                saveList.add(noticeAword);
            }
            
            // 批量保存数据
            if (!saveList.isEmpty()) {
                int savedCount = hkNoticeAwordDao.batchSave(saveList);
                log.info("批量保存成功，保存数据条数：{}", savedCount);
            }
            
            String result = String.format("数据处理完成。总数据：%d条，保存：%d条，重复跳过：%d条", 
                    dataArray.size(), saveList.size(), duplicateCount);
            log.info(result);
            return result;
            
        } catch (Exception e) {
            log.error("获取成交通知单数据失败，参数：{}，错误：{}", yearMonthStr, e.getMessage(), e);
            throw new BusinessException("获取成交通知单数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 将第三方接口响应数据转换为实体对象
     */
    private HkNoticeAwordDO convertResponseToEntity(HkNoticeAwordResponseDT responseDto) {
        HkNoticeAwordDO entity = new HkNoticeAwordDO();
        
        // 字段映射
        entity.setCapacityCode(responseDto.getId());
        entity.setEmsPrice(parsePrice(responseDto.getEmsPrice()));
        entity.setEpacketPrice(parsePrice(responseDto.getEPacketPrice()));
        entity.setAirLettersPrice(parsePrice(responseDto.getAirLettersPrice()));
        entity.setAirParcelsPrice(parsePrice(responseDto.getAirParcelsPrice()));
        entity.setSalPrice(parsePrice(responseDto.getSalPrice()));
        entity.setCurrency(responseDto.getCurrency());
        
        // 日期转换：yyyyMMdd -> datetime
        entity.setEffectiveDate(parseDate(responseDto.getStartDatetime()));
        entity.setExpiryDate(parseDate(responseDto.getEndDatetime()));
        
        // 设置创建和更新时间
        Date now = new Date();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        
        return entity;
    }
    
    /**
     * 解析价格字符串为BigDecimal
     */
    private BigDecimal parsePrice(String priceStr) {
        if (priceStr == null || priceStr.trim().isEmpty()) {
            return null;
        }
        try {
            return new BigDecimal(priceStr.trim());
        } catch (NumberFormatException e) {
            log.warn("价格格式错误：{}", priceStr);
            return null;
        }
    }
    
    /**
     * 解析日期字符串：yyyyMMdd -> Date
     */
    private Date parseDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            return sdf.parse(dateStr.trim());
        } catch (Exception e) {
            log.warn("日期格式错误：{}", dateStr);
            return null;
        }
    }
}
