package cn.com.xmmn.businesses.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;



/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-06 10:55:50
 */
@Data
@ApiModel(value = "香港进口回执表",description = "")
@TableName(value = "tb_xg_import_mail_receipt")
public class XgImportMailReceiptDO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//
	private String id;
	//
	private String itemId;
	//
	private Date replyTime;
	//
	private String replyState;
	//
	private String replyNotes;
	//
	private Date pt;


}
