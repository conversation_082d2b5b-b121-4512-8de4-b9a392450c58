package cn.com.xmmn.example.controller;

import cn.com.xmmn.common.controller.BaseController;
import cn.com.xmmn.common.security.XssProtection;
import cn.com.xmmn.common.utils.R;
import cn.com.xmmn.common.utils.XssUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.List;

/**
 * XSS防护示例控制器
 * 
 * 展示如何在控制器中正确使用XSS防护工具
 * 便于安全扫描工具识别XSS防护措施
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Controller
@RequestMapping("/example/xss")
public class XssProtectedController extends BaseController {
    
    @Autowired
    private XssProtection xssProtection;
    
    /**
     * 搜索页面 - 使用静态工具类清洗参数
     */
    @GetMapping("/search")
    public String search(HttpServletRequest request, Model model) {
        // XSS防护：清洗搜索关键词
        String keyword = XssUtil.clean(request.getParameter("keyword"));
        String category = XssUtil.clean(request.getParameter("category"));
        String sortBy = XssUtil.clean(request.getParameter("sortBy"));
        
        // 验证输入安全性
        if (!XssUtil.isSafe(keyword)) {
            model.addAttribute("error", "搜索关键词包含不安全内容");
            return "error";
        }
        
        // 设置清洗后的参数到模型
        model.addAttribute("keyword", keyword);
        model.addAttribute("category", category);
        model.addAttribute("sortBy", sortBy);
        
        return "example/search";
    }
    
    /**
     * 高级搜索 - 使用Spring组件批量清洗
     */
    @GetMapping("/advancedSearch")
    public String advancedSearch(HttpServletRequest request, Model model) {
        try {
            // XSS防护：批量清洗所有请求参数
            Map<String, String> cleanParams = xssProtection.sanitizeRequestParams(request);
            
            // 验证关键参数
            String keyword = cleanParams.get("keyword");
            if (keyword != null && !xssProtection.validateInput(keyword)) {
                throw new SecurityException("搜索关键词不安全");
            }
            
            // 设置清洗后的参数
            model.addAllAttributes(cleanParams);
            
            return "example/advancedSearch";
        } catch (SecurityException e) {
            model.addAttribute("error", xssProtection.getSafeErrorMessage(e.getMessage()));
            return "error";
        }
    }
    
    /**
     * 用户信息保存 - POST请求XSS防护
     */
    @PostMapping("/saveUser")
    @ResponseBody
    public R saveUser(HttpServletRequest request) {
        try {
            // XSS防护：清洗用户输入
            String username = XssUtil.clean(request.getParameter("username"));
            String email = XssUtil.clean(request.getParameter("email"));
            String description = XssUtil.cleanStrict(request.getParameter("description")); // 严格清洗
            
            // 验证必填字段
            if (!XssUtil.isSafe(username) || !XssUtil.isSafe(email)) {
                return R.error("用户信息包含不安全内容");
            }
            
            // 这里应该调用服务层保存用户信息
            // userService.save(username, email, description);
            
            return R.ok("用户信息保存成功");
        } catch (Exception e) {
            // XSS防护：安全的错误消息
            String safeErrorMsg = XssUtil.getSafeErrorMessage(e.getMessage());
            return R.error("保存失败: " + safeErrorMsg);
        }
    }
    
    /**
     * 文件上传 - 文件名XSS防护
     */
    @PostMapping("/uploadFile")
    @ResponseBody
    public R uploadFile(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        try {
            // XSS防护：清洗文件名
            String originalFilename = file.getOriginalFilename();
            String safeFilename = XssUtil.cleanFileName(originalFilename);
            
            // XSS防护：清洗其他参数
            String description = XssUtil.clean(request.getParameter("description"));
            String category = XssUtil.clean(request.getParameter("category"));
            
            // 验证文件名安全性
            if (!XssUtil.isSafe(safeFilename)) {
                return R.error("文件名包含不安全字符");
            }
            
            // 这里应该调用文件上传服务
            // fileService.upload(file, safeFilename, description, category);
            
            return R.ok("文件上传成功").put("filename", safeFilename);
        } catch (Exception e) {
            return R.error("上传失败: " + XssUtil.getSafeErrorMessage(e.getMessage()));
        }
    }
    
    /**
     * API数据处理 - JSON数据XSS防护
     */
    @PostMapping("/api/processData")
    @ResponseBody
    public R processApiData(@RequestBody Map<String, Object> data) {
        try {
            // XSS防护：批量检查并清洗Map数据
            Map<String, Object> safeData = xssProtection.checkAndSanitizeMap(data, false);
            
            // 这里应该调用业务服务处理数据
            // businessService.processData(safeData);
            
            return R.ok("数据处理成功").put("data", safeData);
        } catch (SecurityException e) {
            return R.error("安全验证失败: " + xssProtection.getSafeErrorMessage(e.getMessage()));
        } catch (Exception e) {
            return R.error("处理失败: " + XssUtil.getSafeErrorMessage(e.getMessage()));
        }
    }
    
    /**
     * 列表数据处理 - List数据XSS防护
     */
    @PostMapping("/api/processList")
    @ResponseBody
    public R processListData(@RequestBody List<String> items) {
        try {
            // XSS防护：清洗列表中的所有字符串
            List<String> safeItems = xssProtection.sanitizeList(items);
            
            // 验证所有项目的安全性
            for (String item : safeItems) {
                if (!xssProtection.validateInput(item)) {
                    return R.error("列表中包含不安全内容");
                }
            }
            
            // 这里应该调用业务服务处理列表
            // businessService.processList(safeItems);
            
            return R.ok("列表处理成功").put("items", safeItems);
        } catch (Exception e) {
            return R.error("处理失败: " + XssUtil.getSafeErrorMessage(e.getMessage()));
        }
    }
    
    /**
     * 内容显示 - 输出编码防护
     */
    @GetMapping("/display")
    public String displayContent(HttpServletRequest request, Model model) {
        // XSS防护：清洗输入
        String userContent = XssUtil.clean(request.getParameter("content"));
        String title = XssUtil.clean(request.getParameter("title"));
        
        // XSS防护：输出编码
        String htmlSafeContent = XssUtil.htmlEncode(userContent);
        String htmlSafeTitle = XssUtil.htmlEncode(title);
        
        // 用于URL的编码
        String urlSafeContent = XssUtil.urlEncode(userContent);
        
        model.addAttribute("htmlContent", htmlSafeContent);
        model.addAttribute("htmlTitle", htmlSafeTitle);
        model.addAttribute("urlContent", urlSafeContent);
        
        return "example/display";
    }
    
    /**
     * 严格模式处理 - 敏感内容XSS防护
     */
    @PostMapping("/strictProcess")
    @ResponseBody
    public R strictProcess(HttpServletRequest request) {
        try {
            // XSS防护：使用严格模式检查并清洗
            String sensitiveContent = xssProtection.checkAndSanitize(
                request.getParameter("content"), true); // 严格模式
            
            String normalContent = xssProtection.checkAndSanitize(
                request.getParameter("description"), false); // 普通模式
            
            // 这里应该调用业务服务处理敏感内容
            // sensitiveService.process(sensitiveContent, normalContent);
            
            return R.ok("处理成功");
        } catch (SecurityException e) {
            return R.error("安全检查失败: " + e.getMessage());
        } catch (Exception e) {
            return R.error("处理失败: " + XssUtil.getSafeErrorMessage(e.getMessage()));
        }
    }
    
    /**
     * 批量参数处理 - 演示多种清洗方式
     */
    @PostMapping("/batchProcess")
    @ResponseBody
    public R batchProcess(HttpServletRequest request) {
        try {
            // 方式1：使用工具类批量清洗
            Map<String, String> cleanParams1 = XssUtil.cleanAll(request);
            
            // 方式2：使用组件批量清洗
            Map<String, String> cleanParams2 = xssProtection.sanitizeRequestParams(request);
            
            // 方式3：手动清洗特定参数
            String specialParam = XssUtil.cleanStrict(request.getParameter("specialParam"));
            String filename = XssUtil.cleanFileName(request.getParameter("filename"));
            
            // 组合结果
            cleanParams1.put("specialParam", specialParam);
            cleanParams1.put("filename", filename);
            
            return R.ok("批量处理成功").put("params", cleanParams1);
        } catch (Exception e) {
            return R.error("批量处理失败: " + XssUtil.getSafeErrorMessage(e.getMessage()));
        }
    }
}
