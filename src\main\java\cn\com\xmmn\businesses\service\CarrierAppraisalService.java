package cn.com.xmmn.businesses.service;

import cn.com.xmmn.businesses.domain.CarrierAppraisalDO;
import cn.com.xmmn.common.utils.Query;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;


/**
 * 承运商整体情况的考评表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-26 15:59:10
 */
public interface CarrierAppraisalService extends IService<CarrierAppraisalDO> {


    /**
     * 分页查询
     * @param params
     * @return
     */
    Page<CarrierAppraisalDO> page(Map<String, Object> params);
}
