package cn.com.xmmn.common.controller;

import org.springframework.stereotype.Controller;

import cn.com.xmmn.common.utils.ShiroUtils;
import cn.com.xmmn.system.domain.UserDO;
import cn.com.xmmn.system.domain.UserToken;

@Controller
public class BaseController {
	public UserDO getUser() {
		return ShiroUtils.getUser();
	}

	public Long getUserId() {
		return getUser().getUserId();
	}

	public String getUsername() {
		return getUser().getUsername();
	}

	public Long getDeptId() { return getUser().getDeptId();	}

	public String getDeptName() { return getUser().getDeptName(); }
}