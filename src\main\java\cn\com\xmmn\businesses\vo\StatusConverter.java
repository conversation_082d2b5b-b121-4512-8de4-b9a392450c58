package cn.com.xmmn.businesses.vo;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import lombok.Data;

/**
 *   导出String状态转化
 */
@Data
public class StatusConverter implements Converter<String> {

    public StatusConverter() {
    }

    @Override
    public Class supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public String convertToJavaData(CellData cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        // 获取Excel单元格的内容，转换为String类型
        String cellValue = cellData.getStringValue();
        if ("是".equals(cellValue)) {
            return "1"; // 如果Excel中的内容是"是"，转换为"1"
        } else if ("否".equals(cellValue)) {
            return "0"; // 如果Excel中的内容是"否"，转换为"0"
        }
        return cellValue; // 如果是其他值，原样返回
    }

    @Override
    public CellData convertToExcelData(String value, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if ("1".equals(value)) {
            return new CellData("是"); // 如果Java中的值是"1"，转换为Excel中的"是"
        } else if ("0".equals(value)) {
            return new CellData("否"); // 如果Java中的值是"0"，转换为Excel中的"否"
        }
        return new CellData(value); // 如果是其他值，原样返回
    }
}
