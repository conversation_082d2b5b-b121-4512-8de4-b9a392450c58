package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.XgImportMailDao;
import cn.com.xmmn.businesses.domain.XgImportMailDO;
import cn.com.xmmn.businesses.service.XgImportMailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class XgImportMailServiceImpl extends ServiceImpl<XgImportMailDao, XgImportMailDO>  implements XgImportMailService {


	@Override
	public void save(List<XgImportMailDO> xgImportMailList) {
		saveBatch(xgImportMailList);
	}
}
