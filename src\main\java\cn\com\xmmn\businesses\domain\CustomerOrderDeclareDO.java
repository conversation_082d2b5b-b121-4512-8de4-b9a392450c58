package cn.com.xmmn.businesses.domain;

import lombok.Data;

import java.io.Serializable;

/**
 * 香港邮政客户订单申报信息
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024年8月26日14:52:23
 */
@Data
public class CustomerOrderDeclareDO implements Serializable {



    //ID;批次号
    private Integer id;

   //客户订单id
    private Integer custometOrderId;
    /**
     * 申报币种
     */
    private String currency;

    /**
     * 海关申报类型：G D S O
     */
    private String customsType;


    /**
     * 申报英文名称
     */
    private String descriptionEn;

    /**
     * 申报中文名称
     *
     */
    private String descriptionCn;


    /**
     * 申报数量
     */
    private Integer quantity;

    /**
     * 申报重量
     */
    private String unitWeight;

    /**
     * 申报价值
     */
    private String unitValue;

    /**
     * 申报海关编码
     */
    private String hsCode;

    /**
     * 申报税号
     */
    private String taxCode;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 规格
     */
    private String specifications;

    /**
     * 原产国
     */
    private String originCountryCode;

}
