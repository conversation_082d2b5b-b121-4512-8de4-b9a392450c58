package cn.com.xmmn.businesses.controller;

import cn.com.xmmn.businesses.domain.CarrierDealerRecivDO;
import cn.com.xmmn.businesses.domain.ImportClearanceEfficiencyDO;
import cn.com.xmmn.businesses.domain.OutMailDO;
import cn.com.xmmn.businesses.dt.CarrierDealerRecivDetailDT;
import cn.com.xmmn.businesses.dt.OutMailDT;
import cn.com.xmmn.businesses.service.CarrierDealerRecivService;
import cn.com.xmmn.businesses.vo.CarrierDealerDepartListVo;
import cn.com.xmmn.businesses.vo.CarrierDealerRecivDetailListVo;
import cn.com.xmmn.businesses.vo.CarrierDealerRecivListVo;
import cn.com.xmmn.common.controller.BaseController;
import cn.com.xmmn.common.handler.CustomHeaderWriteHandler;
import cn.com.xmmn.common.utils.Convert;
import cn.com.xmmn.common.utils.PageUtils;
import cn.com.xmmn.common.utils.Query;
import cn.com.xmmn.common.utils.StringUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.handler.WriteHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpRequest;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 承运商邮袋接收
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-04 09:18:55
 */
@Slf4j
@Controller
@RequestMapping("/carrierDealerReciv/monitor")
public class CarrierDealerRecivController extends BaseController {
    @Autowired
    private CarrierDealerRecivService carrierDealerRecivService;

    @GetMapping()
    @RequiresPermissions("carrierDealerReciv:carrierDealerReciv")
    String carrierDealerReciv() {
        return "carrierDealerReciv/list";
    }


    /**
     * 分组数据
     *
     * @param params
     * @return
     */
    @ResponseBody
    @GetMapping("/group")
    @RequiresPermissions("carrierDealerReciv:carrierDealerReciv")
    public PageUtils group(@RequestParam Map<String, Object> params) {

        //查询列表数据
        Query query = new Query(params);
        log.info("承运商邮袋接收列表参数:" + query);
        List<CarrierDealerRecivListVo> carrierDealerRecivList = carrierDealerRecivService.group(query);

        List<CarrierDealerRecivListVo> carrierDealerCountRecivList= carrierDealerRecivService.groupCount(query);
        Integer total=carrierDealerCountRecivList.get(0).getTotal();
        PageUtils pageUtils = new PageUtils(carrierDealerRecivList, total);
        pageUtils.setDevNum(carrierDealerCountRecivList.get(0).getDevNum());
        pageUtils.setTotalWeight(carrierDealerCountRecivList.get(0).getTotalWeight());
        pageUtils.setReceiveNum(carrierDealerCountRecivList.get(0).getReceiveNum());
        pageUtils.setReceiverTotalWeight(carrierDealerCountRecivList.get(0).getReceiverTotalWeight());
        pageUtils.setAvgReceiverTime(carrierDealerCountRecivList.get(0).getAvgReceiverTime());
        pageUtils.setNotReceiverNum(carrierDealerCountRecivList.get(0).getNotReceiverNum());
        pageUtils.setNotReceiverTotalWeight(carrierDealerCountRecivList.get(0).getNotReceiverTotalWeight());
        pageUtils.setExceptionBagNum(carrierDealerCountRecivList.get(0).getExceptionBagNum());

        return pageUtils;

    }

    /**
     * 跳转详情页列表
     *
     * @return
     */
    @GetMapping("/detailPage")
    @RequiresPermissions("carrierDealerReciv:carrierDealerReciv")
    public String detailPage(HttpServletRequest request, Model model) {
        //点击的是哪个明细。
        String queryParameter = request.getParameter("queryParameter");
        model.addAttribute("queryParameter", queryParameter);

        String orgCode = request.getParameter("orgCode");
        String orgCodeStr = request.getParameter("orgCodeStr");
        String provinceCodeStr = request.getParameter("provinceCodeStr");
        String carrierCode = request.getParameter("carrierCode");
        String productCode = request.getParameter("productCode");
        String receiverCountryCode = request.getParameter("receiverCountryCode");
        //分组搜索的字段
        String opTime538Start = request.getParameter("opTime538Start");
        String opTime538End = request.getParameter("opTime538End");

        String opTime404Start = request.getParameter("opTime404Start");
        String opTime404End = request.getParameter("opTime404End");
        String provinceCode = request.getParameter("provinceCode");
        String oeDest = request.getParameter("oeDest");


        model.addAttribute("opTime538Start", opTime538Start);
        model.addAttribute("opTime538End", opTime538End);
        model.addAttribute("opTime404Start", opTime404Start);
        model.addAttribute("opTime404End", opTime404End);
        model.addAttribute("oeDest", oeDest);
        //交换站机构代码
        model.addAttribute("orgCodeStr", orgCodeStr);
        model.addAttribute("provinceCodeStr", provinceCodeStr);
        model.addAttribute("orgCode", orgCode);
        //承运商代码
        model.addAttribute("carrierCode", carrierCode);
        //业务种类代码
        model.addAttribute("productCode", productCode);
        //寄达国家/地区
        model.addAttribute("receiverCountryCode", receiverCountryCode);
        //交换站省份代码
        model.addAttribute("provinceCode", provinceCode);
        return "carrierDealerReciv/details";
    }

    @ResponseBody
    @GetMapping("/detailTable")
    @RequiresPermissions("carrierDealerReciv:carrierDealerReciv")
    public PageUtils detailTable(CarrierDealerRecivDetailDT dealerReceiveDetailDT) {

        log.info("承运商邮袋接收明细表参数:{}" + dealerReceiveDetailDT);
        List<CarrierDealerRecivDO> carrierDealerRecivList = carrierDealerRecivService.detail(dealerReceiveDetailDT);
        Integer total = carrierDealerRecivService.detailCount(dealerReceiveDetailDT);
        PageUtils pageUtils = new PageUtils(carrierDealerRecivList, total);
        return pageUtils;

    }

    /**
     * 导出数据
     *
     * @param map
     * @param response 入参
     * @return void
     **/
    @RequestMapping(value = "/exportExcel", method = RequestMethod.GET)
    public void exportExcel(@RequestParam Map<String, Object> map, HttpServletResponse response) throws IOException {

        List<CarrierDealerRecivListVo> carrierDealerRecivList = carrierDealerRecivService.group(map);
        List<CarrierDealerRecivListVo> carrierDealerCountRecivList= carrierDealerRecivService.groupCount(map);

//        setAvgReceiverTimeFormatted(carrierDealerRecivList);

        //导出
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");
        // 文件名
        String fileName = map.get("dateStart") + "承运商接收情况统计表" + ".xlsx";
        try {
            fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");

        //第二行，显示日期
//        String titile="承运商接收情况统计";
//        String opTime404Start = Convert.toStr(map.get("opTime404Start"));
//        String opTime404End = Convert.toStr(map.get("opTime404End"));
//        String dateShow="交航配发日期:"+opTime404Start+"至"+opTime404End;
       // WriteHandler customHeaderHandler = new CustomHeaderWriteHandler(titile,dateShow);
        try {

            CarrierDealerRecivListVo newRecord = new CarrierDealerRecivListVo();
            newRecord.setOrgName("合计");
            newRecord.setDevNum(carrierDealerCountRecivList.get(0).getDevNum());
            newRecord.setTotalWeight(carrierDealerCountRecivList.get(0).getTotalWeight());
            newRecord.setReceiveNum(carrierDealerCountRecivList.get(0).getReceiveNum());
            newRecord.setReceiverTotalWeight(carrierDealerCountRecivList.get(0).getReceiverTotalWeight());
            newRecord.setAvgReceiverTime(carrierDealerCountRecivList.get(0).getAvgReceiverTime());
            newRecord.setNotReceiverNum(carrierDealerCountRecivList.get(0).getNotReceiverNum());
            newRecord.setNotReceiverTotalWeight(carrierDealerCountRecivList.get(0).getNotReceiverTotalWeight());
            newRecord.setExceptionBagNum(carrierDealerCountRecivList.get(0).getExceptionBagNum());
            carrierDealerRecivList.add(newRecord);
            //导出
            EasyExcel.write(response.getOutputStream(), CarrierDealerRecivListVo.class)
                    //.registerWriteHandler(customHeaderHandler)
                    .sheet("承运商接收情况统计表")
                    .doWrite(carrierDealerRecivList);
        } catch (Exception e) {
            log.info("excel文档导出错误-异常信息：" + e);
            e.printStackTrace();
        }

    }

    @RequestMapping(value = "/exportDetailExcel",method = RequestMethod.GET)
    public void exportDetailExcel(CarrierDealerRecivDetailDT dealerReceiveDetailDT, HttpServletResponse response) throws IOException {
        List<CarrierDealerRecivDO> portbatchList = carrierDealerRecivService.detail(dealerReceiveDetailDT);


        //导出
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");
        // 文件名
        String fileName =   "承运商接收情况明细" + ".xlsx";
        try {
            fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");


        try {
            List<CarrierDealerRecivDetailListVo> exportList = new ArrayList<>();
            for (CarrierDealerRecivDO portbatch : portbatchList) {
                CarrierDealerRecivDetailListVo vo = new CarrierDealerRecivDetailListVo();
                BeanUtils.copyProperties(portbatch, vo);
                exportList.add(vo);
            }
            //导出
            EasyExcel.write(response.getOutputStream(), CarrierDealerRecivDetailListVo.class)
                    //.registerWriteHandler(customHeaderHandler)
                    .sheet("承运商接收情况明细")
                    .doWrite(exportList);
        } catch (Exception e) {
            log.info("excel文档导出错误-异常信息：" + e);
            e.printStackTrace();
        }
    }
//    void setAvgReceiverTimeFormatted( List<CarrierDealerRecivListVo> carrierDealerRecivList){
//        if (StringUtils.isNotEmpty(carrierDealerRecivList)) {
//            carrierDealerRecivList.forEach(carrierDealerRecivListVo -> {
//                Integer avgReceiverTime = carrierDealerRecivListVo.getAvgReceiverTime();
//                if (!StringUtils.isNull(avgReceiverTime)) {
//                    // 将秒数转换为天数和小时数
//                    int days = avgReceiverTime / (24 * 3600);
//                    int remainingSeconds = avgReceiverTime % (24 * 3600);
//                    int hours = remainingSeconds / 3600;
//
//                    // 只在小时数不为0时添加小时部分
//                    if (hours > 0) {
//                        carrierDealerRecivListVo.setAvgReceiverTimeFormatted(days + "." + hours);
//                    } else {
//                        carrierDealerRecivListVo.setAvgReceiverTimeFormatted(String.valueOf(days));
//                    }
//                }
//            });
//        }
//    }
}
