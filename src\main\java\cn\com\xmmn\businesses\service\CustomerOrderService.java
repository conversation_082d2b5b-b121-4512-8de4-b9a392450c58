package cn.com.xmmn.businesses.service;

import cn.com.xmmn.businesses.domain.CustomerOrderDO;

import java.util.List;
import java.util.Map;

/**
 * 客户订单表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-26 17:06:58
 */
public interface CustomerOrderService {
	
	CustomerOrderDO get(Integer id);
	
	List<CustomerOrderDO> list(Map<String, Object> map);
	
	int count(Map<String, Object> map);
	
	int save(CustomerOrderDO customerOrder);
	
	int update(CustomerOrderDO customerOrder);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);

	void receiveCustomerOrder(String encryptedData);
}
