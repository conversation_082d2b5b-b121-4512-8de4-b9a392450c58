package cn.com.xmmn.businesses.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 承运商邮袋接收
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-04 09:18:55
 */
@Data
public class CarrierDealerRecivDO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//ID;批次号
	private Integer id;
	//交换站机构名称
	private String orgName;
	//交换站省份代码
	private String provinceCode;
	//承运商
	private String carrierDealer;
	//业务种类code
	private String productCode;
	//业务种类
	private String product;
	//邮袋条码
	private String oeBagBarcode;
	//总重量
	private BigDecimal totalWeight;
	//计划航班
	private String flightNumber;
	//实际航班
	private String newFlightNo;
	//运输方式
	private String transType;
	//寄达国家/地区
	private String receiverCountryCode;
	//寄达国家名称
	private String receiverCountryName;
	//寄达互换局代码
	private String oeDest;
	//寄达互换局名称
	private String oeDestName;

    //承诺时长(小时)
	private String promiseTime;

	//直封路单开始时间
	private Date oeBillStartTime;
	//处理中心卸车时间
	private Date opTime352;
	//交航扫描时间(交航时间)
	private Date opTime404;
	//已送交承运局时间（接收时间）
	private Date opTime538;
	//航空公司启运时间（启运时间）
	private Date opTime457;
	//飞机到达进港时间(运抵时间 )
	private Date opTime505;
	//送交境外航站-交邮处理
	private Date opTime507;
	//总包到达寄达地时间
	private Date opTime516;
	//到达境外进口互换局时间
	private Date opTime459;
	//送交境外进口海关时间
	private Date opTime486;
	//离开境外进口互换局时间
	private Date opTime460;
	//到达境外投递局时间
	private Date opTime461;
	//境外试投时间
	private Date opTime462;
	//境外妥投时间
	private Date opTime463;
	//业务时间
	private Date pt;
    //路向：展示该邮袋的寄达地三字代码
	private String receiverCountry;

	private String proTime;
	private String proStatus;

}
