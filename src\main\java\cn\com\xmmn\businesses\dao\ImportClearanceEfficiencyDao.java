package cn.com.xmmn.businesses.dao;

import cn.com.xmmn.businesses.domain.ImportClearanceEfficiencyDO;
import cn.com.xmmn.businesses.domain.ImportClearanceEfficiencyProportionDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 进口邮件表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-24 15:06:38
 */
@Mapper
public interface ImportClearanceEfficiencyDao extends BaseMapper<ImportClearanceEfficiencyDO> {
    ImportClearanceEfficiencyDO get(Integer id);

    List<ImportClearanceEfficiencyDO> list(Map<String,Object> map);

    int count(Map<String,Object> map);

    int save(ImportClearanceEfficiencyDO importMail);

    int update(ImportClearanceEfficiencyDO importMail);

    int remove(Integer id);

    int batchRemove(Integer[] ids);

    /**
     * 进口通关效率占比信息
     * @return ImportClearanceEfficiencyProportionDO
     **/
    ImportClearanceEfficiencyProportionDO getProportion(Map<String, Object> map);
}
