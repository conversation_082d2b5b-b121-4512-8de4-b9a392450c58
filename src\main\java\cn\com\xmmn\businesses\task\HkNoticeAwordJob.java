package cn.com.xmmn.businesses.task;

import cn.com.xmmn.businesses.service.HkNoticeAwordService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 香港成交通知单定时任务
 * 每月1号1:30自动获取上个月的成交数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-31
 */
@Component
@Slf4j
public class HkNoticeAwordJob implements Job {
    
    @Autowired
    private HkNoticeAwordService hkNoticeAwordService;

    /**
     * Quartz任务执行方法
     */
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.info("香港成交通知单定时任务开始执行");
        executeTask();
    }

    /**
     * Spring定时任务
     * 每月1号1:30执行
     */
    @Scheduled(cron = "0 30 1 1 * ?")
    private void scheduledTask() {
        log.info("======香港成交通知单定时任务开始执行，时间：{}", new Date());
        executeTask();
        log.info("======香港成交通知单定时任务执行结束，时间：{}", new Date());
    }
    
    /**
     * 执行任务的核心逻辑
     */
    private void executeTask() {
        try {
            String result = hkNoticeAwordService.scheduledRequestBill();
            log.info("香港成交通知单定时任务执行成功，结果：{}", result);
        } catch (Exception e) {
            log.error("香港成交通知单定时任务执行失败，错误：{}", e.getMessage(), e);
        }
    }
}
