package cn.com.xmmn.businesses.service;

import cn.com.xmmn.businesses.domain.ImportClearanceEfficiencyDO;
import cn.com.xmmn.businesses.domain.ImportClearanceEfficiencyProportionDO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Map;

/**
 * 进口通关效率
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-24 15:06:38
 */
public interface ImportClearanceEfficiencyService {


    List<ImportClearanceEfficiencyDO> list(Map<String, Object> map);

    int count(Map<String, Object> map);


    /**
     *  获取占比信息
     * @return ImportClearanceEfficiencyProportionDO
     **/
    ImportClearanceEfficiencyProportionDO getProportion(Map<String, Object> map);


    Page<ImportClearanceEfficiencyDO> pageImport(Map<String, Object> map);
}
