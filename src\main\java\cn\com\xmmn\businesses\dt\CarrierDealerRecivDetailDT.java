package cn.com.xmmn.businesses.dt;

import cn.com.xmmn.common.utils.Query;
import lombok.Data;

import java.util.Map;

/**
 * 承运商邮袋接收明细搜索参数
 */
@Data
public class CarrierDealerRecivDetailDT{

    //搜索的参数：已交航袋数（），承运商已接收袋，承运商接收总重量（kg），平均接收处理时长（天)，承运商未接收袋数，承运商未接收总重量（kg）
    private String queryParameter;

    //交换站省份组多个使用逗号隔开
    private String provinceCodeStr;

    //交换站机构多个使用逗号隔开
    private String orgCodeStr;

    //承运商代码
    private String carrierCode;
    //承运商接收开始日期
    private String opTime538Start;
    //承运商接收结束日期
    private String opTime538End;
    //业务种类代码
    private String productCode;
    //寄达国家
    private String receiverCountryCode;

    //寄达国家
    private String receiverCountry;

    //统计周期开始
    private String opTime404Start;

    //统计周期结束
    private String opTime404End;

    //点击明细那条交换机机构数据的值
    private String orgCode;

    private String oeDest;

    private int offset;
    // 每页条数
    private int limit;
}
