package cn.com.xmmn.common.strategy;

import cn.com.xmmn.businesses.dt.ImportHhjDisposeExcelDT;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.style.column.AbstractHeadColumnWidthStyleStrategy;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.List;

/**
 * 进口互换局时限统计导出策略
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-02 15:02:00
 */
public class ImportHhjDisposeExcelStrategy extends AbstractHeadColumnWidthStyleStrategy {

    //处理重复调用问题
    private boolean mergedCellsProcessed = false;

    private final List<ImportHhjDisposeExcelDT> dataList;


    public ImportHhjDisposeExcelStrategy(List<ImportHhjDisposeExcelDT> dataList) {
        this.dataList = dataList;
    }

    @Override
    protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<CellData> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        if (isHead) {
            Sheet sheet = writeSheetHolder.getSheet();
            sheet.setColumnWidth(cell.getColumnIndex(), 20 * 256);
        }
    }

    @Override
    protected Integer columnWidth(Head head, Integer integer) {
        return null;
    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, List<CellData> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        Sheet sheet = writeSheetHolder.getSheet();
        if (isHead && cell.getRowIndex() == 0 && !mergedCellsProcessed) {
            // 合并单元格区域
            sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 0)); // 省份
            sheet.addMergedRegion(new CellRangeAddress(0, 1, 1, 1)); // 互换局
            sheet.addMergedRegion(new CellRangeAddress(0, 1, 2, 2)); // 机构代码
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 3, 11)); // (邮件)接收-开拆
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 12, 18)); // (邮件)海关待验
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 19, 23)); // (邮件)开拆-封发
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 24, 28)); // (邮件)封发-发运
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 29, 33)); // (邮件)接收-发运
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 34, 41)); // (邮件)发运-投递

            // 创建表头行并设置合并单元格内容
            Row row0 = sheet.createRow(0);
            Row row1 = sheet.createRow(1);

            createCell(row0, 0, "省份");
            createCell(row0, 1, "互换局");
            createCell(row0, 2, "机构代码");
            createCell(row0, 3, "(邮件)接收-开拆");
            createCell(row0, 12, "(邮件)海关待验");
            createCell(row0, 19, "(邮件)开拆-封发");
            createCell(row0, 24, "(邮件)封发-发运");
            createCell(row0, 29, "(邮件)接收-发运");
            createCell(row0, 34, "(邮件)发运-投递");

            // 设置第二行具体字段标题
            createCell(row1, 3, "接收量");
            createCell(row1, 4, "未接收量");
            createCell(row1, 5, "应处理量");
            createCell(row1, 6, "开拆量");
            createCell(row1, 7, "开拆率");
            createCell(row1, 8, "逾期开拆率");
            createCell(row1, 8, "逾期开拆量");
            createCell(row1, 9, "平均时长(h)");
            createCell(row1, 10, "未开拆量");

            //... 继续设置其他列的标题
            // "邮件海关待验"
            createCell(row1, 11, "通关量");
            createCell(row1, 12, "通关率");
            createCell(row1, 13, "通关平均时长");
            createCell(row1, 14, "待通关量");
            createCell(row1, 15, "未缴税量");
            createCell(row1, 16, "补充申报量");
            createCell(row1, 17, "未申报量");

            // "邮件开拆-封发"
            createCell(row1, 18, "已封发量");
            createCell(row1, 19, "封发率");
            createCell(row1, 20, "逾期封发量");
            createCell(row1, 21, "封发平均时长");
            createCell(row1, 22, "未封发量");

            // "邮件封发-发运"
            createCell(row1, 23, "已发运量");
            createCell(row1, 24, "发运率");
            createCell(row1, 25, "逾期发运量");
            createCell(row1, 26, "发运平均时长");
            createCell(row1, 27, "未发运量");

            // "邮件接收-发运"
            createCell(row1, 28, "时限内");
            createCell(row1, 29, "及时率");
            createCell(row1, 30, "逾期邮件量");
            createCell(row1, 31, "平均时长(h)");


            // "邮件发运-投递"
            createCell(row1, 32, "封发量");
            createCell(row1, 33, "发运量");
            createCell(row1, 34, "发运率");
            createCell(row1, 35, "未发运量");
            createCell(row1, 36, "下一环节接收量");
            createCell(row1, 37, "下一环节未接收量");
            createCell(row1, 38, "已投递量");
            createCell(row1, 39, "未投递量");
            createCell(row1, 40, "投递率");
            // 设置样式和背景色
            CellStyle style = sheet.getWorkbook().createCellStyle();
            style.setAlignment(HorizontalAlignment.CENTER);
            style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            Font font = sheet.getWorkbook().createFont();
            font.setBold(true);
            style.setFont(font);

            // 应用样式到每个单元格
            for (int i = 0; i <= 1; i++) {
                for (int j = 0; j <= 41; j++) {
                    Cell cellHeader = sheet.getRow(i).getCell(j);
                    if (cellHeader != null) {
                        cellHeader.setCellStyle(style);
                    }
                }
            }

            // 设置标志位为已处理
            mergedCellsProcessed = true;
        }
        if (!isHead) {
            // 合并数据行单元格
            mergeDataCells(sheet);
        }
    }

    private void createCell(Row row, int column, String value) {
        Cell cell = row.createCell(column);
        cell.setCellValue(value);
    }

    private void mergeDataCells(Sheet sheet) {
        int rowIndex = 1;
        String previousProvinceName = "";
        String previousOrgName = "";
        int startRow = rowIndex;

        for (int i = 0; i < dataList.size(); i++) {
            ImportHhjDisposeExcelDT data = dataList.get(i);
            String currentProvinceName = data.getProvinceName();
            String currentOrgName = data.getOrgName();

            boolean isLastRow = (i == dataList.size() - 1);

            if (!currentProvinceName.equals(previousProvinceName) || !currentOrgName.equals(previousOrgName) || isLastRow) {
                int endRow = rowIndex + (i - startRow);

                // 判断是否有至少两个单元格可合并
                if (endRow >= startRow) {
                    sheet.addMergedRegion(new CellRangeAddress(startRow, endRow, 1, 1));
                    sheet.addMergedRegion(new CellRangeAddress(startRow, endRow, 2, 2));
                }

                startRow = rowIndex + 1;
            }

            previousProvinceName = currentProvinceName;
            previousOrgName = currentOrgName;
            rowIndex++;
        }
    }

}
