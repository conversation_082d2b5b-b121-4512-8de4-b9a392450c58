package cn.com.xmmn.businesses.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 待通关导出信息
 */
@Data
@HeadRowHeight(value = 30) //头部行高
@ContentRowHeight(value = 25) //内容行高
@ColumnWidth(value = 20) //列宽
@HeadFontStyle(fontName = "宋体",fontHeightInPoints = 11)
public class DtgMaillExportVO implements Serializable {

    @ExcelProperty("客户名称")
    private String custName;
    @ExcelProperty("邮件号码")
    private String itemId;
    @ExcelProperty("报关行")
    private String brokerName;

    @ExcelProperty("报关行解车时间")
    private Date brokerTime;

    @ExcelProperty("最新海关回执返回时间")
    private Date replyTime;

    @ExcelProperty("海关回执代码")
    private String replyState;
    @ExcelProperty("最新海关回执")
    private String replyNotes;
    @ExcelProperty("客户补充资料时间")
    private Date custSubmitTime;
    @ExcelProperty(value = "是否缴税", converter = StatusConverter.class)
    private String ifCustTax;

    @ExcelProperty("客户缴税时间")
    private Date custTaxTime;

    @ExcelProperty("税金")
    private String taxTotalValue;


    public DtgMaillExportVO() {

    }


}
