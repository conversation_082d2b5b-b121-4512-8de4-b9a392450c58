package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.XydBillDetailDao;
import cn.com.xmmn.businesses.domain.CheckXydCarrierDO;
import cn.com.xmmn.businesses.domain.XydBillDetailDO;
import cn.com.xmmn.businesses.service.XydBillDetailService;
import io.swagger.models.auth.In;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;



@Service
public class XydBillDetailServiceImpl implements XydBillDetailService {
	@Autowired
	private XydBillDetailDao xydBillDetailDao;
	
	@Override
	public XydBillDetailDO get(Integer id){
		return xydBillDetailDao.get(id);
	}
	
	@Override
	public List<XydBillDetailDO> list(Map<String, Object> map){
		return xydBillDetailDao.list(map);
	}
	
	@Override
	public int count(Map<String, Object> map){
		return xydBillDetailDao.count(map);
	}
	
	@Override
	public int save(XydBillDetailDO xydBillDetail){
		return xydBillDetailDao.save(xydBillDetail);
	}
	
	@Override
	public int update(XydBillDetailDO xydBillDetail){
		return xydBillDetailDao.update(xydBillDetail);
	}
	
	@Override
	public int remove(Integer id){
		return xydBillDetailDao.remove(id);
	}
	
	@Override
	public void barcodesRemove(String[] barcodes){
		xydBillDetailDao.batchRemove(barcodes);
	}

	@Override
	public List<CheckXydCarrierDO> checkList(Map<String, Object> map) {
		return xydBillDetailDao.checkList(map);
	}

	@Override
	public int checkCount(Map<String, Object> map) {
		return xydBillDetailDao.checkCount(map);
	}

}
