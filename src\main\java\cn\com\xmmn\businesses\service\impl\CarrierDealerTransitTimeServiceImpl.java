package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.CarrierDealerTransitTimeDao;
import cn.com.xmmn.businesses.domain.CarrierDealerRecivDO;
import cn.com.xmmn.businesses.dt.CarrierDealerRecivDetailDT;
import cn.com.xmmn.businesses.service.CarrierDealerTransitTimeService;
import cn.com.xmmn.businesses.vo.CarrierDealerTransitTimeListVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Service
public class CarrierDealerTransitTimeServiceImpl extends ServiceImpl<CarrierDealerTransitTimeDao,CarrierDealerRecivDO> implements CarrierDealerTransitTimeService {

    @Autowired
    private CarrierDealerTransitTimeDao carrierDealerTransitTimeDao;

    @Override
    public List<CarrierDealerTransitTimeListVO> group(Map<String,Object> query) {


        return carrierDealerTransitTimeDao.group(query);
    }

    @Override
    public List<CarrierDealerTransitTimeListVO>  groupCount(Map<String,Object> query) {
        return carrierDealerTransitTimeDao.groupCount(query);
    }


    @Override
    public List<CarrierDealerRecivDO> detail(CarrierDealerRecivDetailDT dealerRecivDetailDT) {
        return carrierDealerTransitTimeDao.detail(dealerRecivDetailDT);
    }

    @Override
    public Integer detailCount(CarrierDealerRecivDetailDT dealerRecivDetailDT) {
        return carrierDealerTransitTimeDao.detailCount(dealerRecivDetailDT);
    }
}
