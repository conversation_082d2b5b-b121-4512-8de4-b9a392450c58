package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.CarrierBillLogDao;
import cn.com.xmmn.businesses.domain.CarrierBillLogDO;
import cn.com.xmmn.businesses.service.CarrierBillLogService;
import cn.com.xmmn.common.utils.Convert;
import cn.com.xmmn.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service
public class CarrierBillLogServiceImpl extends ServiceImpl<CarrierBillLogDao, CarrierBillLogDO> implements CarrierBillLogService {
	@Autowired
	private CarrierBillLogDao carrierBillLogDao;


	@Override
	public Page<CarrierBillLogDO> page(Map<String, Object> params) {
		QueryWrapper<CarrierBillLogDO> queryWrapper=new QueryWrapper();

		//承运商
		String carrierName = Convert.toStr(params.get("carrierName"));

		//财务日期 开始日期和结束日期
		String accountPeriodStart = Convert.toStr(params.get("accountPeriodStart"));
		String accountPeriodEnd = Convert.toStr(params.get("accountPeriodEnd"));

		//操作动作
		String operType = Convert.toStr(params.get("operType"));

		int limit = params.getOrDefault("limit", 10) instanceof Number ? ((Number) params.get("limit")).intValue() : 10;
		int page = params.getOrDefault("page", 1) instanceof Number ? ((Number) params.get("page")).intValue() : 1;
		if(StringUtils.isNotEmpty(carrierName)){
			queryWrapper.eq("carrier_name",carrierName);
		}
		if (StringUtils.isNotEmpty(accountPeriodStart) && StringUtils.isNotEmpty(accountPeriodEnd)){
			queryWrapper.between("account_period", accountPeriodStart, accountPeriodEnd);
		}
		if(StringUtils.isNotEmpty(operType)){
			queryWrapper.eq("oper_type",operType);
		}

		Page<CarrierBillLogDO> pageRequest =new Page(page,limit);

		Page<CarrierBillLogDO> pageResult    = carrierBillLogDao.selectPage(pageRequest, queryWrapper);
		return pageResult;
	}

}
