package cn.com.xmmn.common.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.resource.VersionResourceResolver;

/**
 * @Description  Web配置类，用于配置静态资源处理器。
 * <AUTHOR>
 * @Date 2024/6/3 16:57
 */
@Configuration
public class WebConfig  implements WebMvcConfigurer {


    /**配置静态资源处理器，实现静态资源版本控制。
     * @param registry 资源处理器注册表
     **/
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/js/**")//定义URL匹配规则
                .addResourceLocations("classpath:/static/js/")// 指定静态资源存放的位置
                .resourceChain(true)// 启用资源链
                .addResolver(new VersionResourceResolver()  // 添加版本解析器
                       // .addFixedVersionStrategy("v3.0.1","/**"));;// 使用固定版本策略，版本号为v2.0.1
                       .addContentVersionStrategy("/**"));// 或者使用内容版本策略（可选）
    }

}
