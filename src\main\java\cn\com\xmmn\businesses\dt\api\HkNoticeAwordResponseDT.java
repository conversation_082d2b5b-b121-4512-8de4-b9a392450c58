package cn.com.xmmn.businesses.dt.api;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 香港成交通知单响应DTO
 * 映射第三方接口返回的数据结构
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-31
 */
@Data
public class HkNoticeAwordResponseDT {

    /**
     * 运能编码（对应数据库的capacity_code）
     */
    @JSONField(name = "ID")
    private String id;

    /**
     * 特快价格（对应数据库的ems_price）
     */
    @JSONField(name = "emsPrice")
    private String emsPrice;

    /**
     * 邮宝价格（对应数据库的epacket_price）
     */
    @JSONField(name = "ePacketPrice")
    private String ePacketPrice;

    /**
     * 航空函件价格（对应数据库的air_letters_price）
     */
    @JSONField(name = "airLettersPrice")
    private String airLettersPrice;

    /**
     * 航空包裹价格（对应数据库的air_parcels_price）
     */
    @JSONField(name = "airParcelsPrice")
    private String airParcelsPrice;

    /**
     * 空运水路邮件价格（对应数据库的sal_price）
     */
    @JSONField(name = "salPrice")
    private String salPrice;

    /**
     * 生效时间（对应数据库的effective_date）
     * 格式：yyyyMMdd
     */
    @JSONField(name = "startDatetime")
    private String startDatetime;

    /**
     * 失效时间（对应数据库的expiry_date）
     * 格式：yyyyMMdd
     */
    @JSONField(name = "endDatetime")
    private String endDatetime;

    /**
     * 币种（对应数据库的currency）
     */
    @JSONField(name = "currency")
    private String currency;
}
