package cn.com.xmmn.businesses.dao;

import cn.com.xmmn.businesses.domain.PortbatchDO;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

/**
 * 口岸导入批次表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-22 01:30:58
 */
@Mapper
public interface PortbatchDao {

	PortbatchDO get(Integer id);
	
	List<PortbatchDO> list(Map<String,Object> map);
	
	int count(Map<String,Object> map);
	
	int save(PortbatchDO porbatch);
	
	int update(PortbatchDO porbatch);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);
}
