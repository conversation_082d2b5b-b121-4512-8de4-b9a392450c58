package cn.com.xmmn.businesses.controller.api;

import cn.com.xmmn.businesses.dt.api.DateRequest;
import cn.com.xmmn.businesses.service.HkCarrierDealerService;
import cn.com.xmmn.businesses.utils.AESUtils;
import cn.com.xmmn.common.utils.ApiResponseData;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 接收对外的邮袋信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024年7月25日09:44:35
 */
@Slf4j
@RestController
@RequestMapping("/api/receive/mailbag")
@RequiredArgsConstructor
public class APIReceiveMailbagController {


    private final HkCarrierDealerService carrierDealerService;



    /**
     *
     * @param request
     * @return
     */
    @PostMapping("")
    public ApiResponseData receiveMailbag(@RequestBody DateRequest request) {
        String encryptedData = request.getEncryptedData();

        carrierDealerService.receiveMailbag(encryptedData);

        return ApiResponseData.ok();
    }








    //order 订单邮件申报信息
    @PostMapping("/data")
    public ApiResponseData receiveEncryptedData(@RequestBody String encryptedData) {
        try {
            // 解密数据
            String decryptedData = AESUtils.decryptByBytes(encryptedData);

            // 将解密后的数据转换为Map对象
            Map<String, String> dataMap = JSONObject.parseObject(decryptedData, Map.class);

            // 输出解密后的数据，实际业务中可以根据需要处理
            log.info("Received decrypted data: {}", dataMap);
            // 返回成功响应
            return ApiResponseData.ok();
        } catch (Exception e) {
            log.error("Failed to decrypt and process data", e);
            return new ApiResponseData(HttpStatus.INTERNAL_SERVER_ERROR.value(),"Failed to decrypt data.");
        }
    }

    public static void main(String[] args) throws Exception {

//        List<Map<String,Object>> list =new ArrayList<>();
//        Map<String,Object> map=new HashMap<>();
//        map.put("bagBarcode", "CNBJSAESMADBACN44227002010020");
//        map.put("deliveryBillNo", "HKA4043106");
//        map.put("departureDatetime", "2024-06-03 18:00:00");
//        map.put("carrierName", "EK383/EK143");
//        list.add(map);

//        String conStr="{\n" +
//                "\t\"orderList\": [{\n" +
//                "\t\t\"CustomerName\": \"EASY(e7307)\",\n" +
//                "\t\t\"TrackingNo\": \"EX222222222HK\",\n" +
//                "\t\t\"ScanTime\": \"2024-08-14 12:30:59\",\n" +
//                "\t\t\"ScanWeight\": \"0.27\",\n" +
//                "\t\t\"ReceiverName\": \"TEST\",\n" +
//                "\t\t\"ReceiverCountryCode\": \"CN\",\n" +
//                "\t\t\"ReceiverState\": \"海南省\",\n" +
//                "\t\t\"ReceiverCity\": \"三亚市\",\n" +
//                "\t\t\"ReceiverAddress\": \"海南省三亚市天涯区河西区街道解放四路农垦医院11栋2106室\",\n" +
//                "\t\t\"ReceiverPhoneNumber\": \"13098968388\",\n" +
//                "\t\t\"ReceiverPostCode\": \"572000\",\n" +
//                "\t\t\"DutyPaid\": \"1\",\n" +
//                "\t\t\"OrderCustoms\": {\n" +
//                "\t\t\t\"Currency\": \"RMB\",\n" +
//                "\t\t\t\"CustomsType\": \"G\",\n" +
//                "\t\t\t\"CustomsItemList\": [{\n" +
//                "\t\t\t\t\t\"DescriptionEn\": \"Men's short sleeve TEE\",\n" +
//                "\t\t\t\t\t\"DescriptionCn\": \"短袖tee HERITAGE TEE GRAPHICS\",\n" +
//                "\t\t\t\t\t\"Quantity\": 1,\n" +
//                "\t\t\t\t\t\"UnitWeight\": \"0.22\",\n" +
//                "\t\t\t\t\t\"UnitValue\": \"38.34\",\n" +
//                "\t\t\t\t\t\"HsCode\": \"\",\n" +
//                "\t\t\t\t\t\"TaxCode\": \"04010400\",\n" +
//                "\t\t\t\t\t\"Brand\": \"Champion\",\n" +
//                "\t\t\t\t\t\"Specifications\": \"S/件\",\n" +
//                "\t\t\t\t\t\"OriginCountryCode\": \"\"\n" +
//                "\t\t\t\t},\n" +
//                "\t\t\t\t{\n" +
//                "\t\t\t\t\t\"DescriptionEn\": \"Men's short sleeve TEE\",\n" +
//                "\t\t\t\t\t\"DescriptionCn\": \"短袖tee HERITAGE TEE GRAPHICS\",\n" +
//                "\t\t\t\t\t\"Quantity\": 2,\n" +
//                "\t\t\t\t\t\"UnitWeight\": \"0.22\",\n" +
//                "\t\t\t\t\t\"UnitValue\": \"38.34\",\n" +
//                "\t\t\t\t\t\"HsCode\": \"\",\n" +
//                "\t\t\t\t\t\"TaxCode\": \"04010400\",\n" +
//                "\t\t\t\t\t\"Brand\": \"Champion\",\n" +
//                "\t\t\t\t\t\"Specifications\": \"S/件\",\n" +
//                "\t\t\t\t\t\"OriginCountryCode\": \"\"\n" +
//                "\t\t\t\t}\n" +
//                "\t\t\t]\n" +
//                "\t\t}\n" +
//                "\t}]\n" +
//                "}";
//        JSONObject jsonObject1 = JSONObject.parseObject(conStr);
//        JSONObject convertedJson = convertFieldNamesToLowerCase(jsonObject1);
//
//        String s1 = AESUtils.encryptToBytes(convertedJson.toJSONString());
//        System.out.println(s1);
        //String s = AESUtils.decryptByBytes(s1);

//
//
//        ReceiveMailOrderListDT receiveMailOrderListDT1 = JSON.parseObject(s, ReceiveMailOrderListDT.class);
//
//        //订单列表
//        List<ReceiveMailOrderDT> orderList = receiveMailOrderListDT1.getOrderList();
//        orderList.forEach(receiveMailOrderDT -> {
//
//            CustomerOrderDO customerOrderDT=new CustomerOrderDO();
//
//            //特殊处理字符串转为时间格式
//            String scanTime = receiveMailOrderDT.getScanTime();
//            customerOrderDT.setScanTime(DateUtils.convertStringToDate(scanTime,"yyyyMMddHHmmss"));
//
//            //拷贝客户订单信息
//            BeanUtils.copyProperties(receiveMailOrderDT,customerOrderDT);
//
//            //申报信息
//            ReceiveMailOrderCustomDT orderCustoms = receiveMailOrderDT.getOrderCustoms();
//
//            String customsType = orderCustoms.getCustomsType();
//            String currency = orderCustoms.getCurrency();
//            List<ReceiveMailDeclarationDT> customsItemList = orderCustoms.getCustomsItemList();
//
//            customsItemList.forEach(receiveMailDeclarationDT -> {
//                CustomerOrderDeclareDO customerOrderDeclareDT=new CustomerOrderDeclareDO();
//                customerOrderDeclareDT.setCustomsType(customsType);
//                customerOrderDeclareDT.setCurrency(currency);
//                //拷贝客户订单信息
//                BeanUtils.copyProperties(orderCustoms,customerOrderDeclareDT);
//            });
//        });

        //Object data = jsonObject.get("orderList");
        //JSONArray dataArray = jsonObject.getJSONArray("orderList");


     //   List<ReceiveMailOrderListDT> receiveMailOrderListDTS = dataArray.toJavaList(ReceiveMailOrderListDT.class);

       // receiveMailOrderListDTS.forEach(mailOrderListDT -> {

            //订单信息
           // List<ReceiveMailOrderDT> orderDTList = mailOrderListDT.getOrderDTList();

            //详情列表


       // });

      //  System.out.println(s);

    }
    // 转换 JSON 对象中的字段名称
//    private static JSONObject convertFieldNamesToLowerCase(JSONObject jsonObject) {
//        JSONObject newJsonObject = new JSONObject();
//        for (String key : jsonObject.keySet()) {
//            Object value = jsonObject.get(key);
//            if (value instanceof JSONObject) {
//                value = convertFieldNamesToLowerCase((JSONObject) value);
//            } else if (value instanceof com.alibaba.fastjson.JSONArray) {
//                value = convertArrayFieldNamesToLowerCase((com.alibaba.fastjson.JSONArray) value);
//            }
//            newJsonObject.put(toLowerCaseFirstChar(key), value);
//        }
//        return newJsonObject;
//    }
//    // 转换数组中的字段名称
//    private static com.alibaba.fastjson.JSONArray convertArrayFieldNamesToLowerCase(com.alibaba.fastjson.JSONArray jsonArray) {
//        com.alibaba.fastjson.JSONArray newArray = new com.alibaba.fastjson.JSONArray();
//        for (Object item : jsonArray) {
//            if (item instanceof JSONObject) {
//                newArray.add(convertFieldNamesToLowerCase((JSONObject) item));
//            } else {
//                newArray.add(item);
//            }
//        }
//        return newArray;
//    }
//
//    // 将字段名称首字母转换为小写
//    private static String toLowerCaseFirstChar(String str) {
//        if (str == null || str.length() == 0) {
//            return str;
//        }
//        return Character.toLowerCase(str.charAt(0)) + str.substring(1);
//    }
}
