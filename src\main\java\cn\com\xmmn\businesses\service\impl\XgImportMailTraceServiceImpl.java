package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.XgImportMailTraceDao;
import cn.com.xmmn.businesses.dao.XgOutMailDao;
import cn.com.xmmn.businesses.domain.XgImportMailTraceDO;
import cn.com.xmmn.businesses.domain.XgOutMailDO;
import cn.com.xmmn.businesses.service.XgImportMailTraceService;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;


@Service
public class XgImportMailTraceServiceImpl extends ServiceImpl<XgImportMailTraceDao, XgImportMailTraceDO>  implements XgImportMailTraceService {

	@Override
	public void save(List<XgImportMailTraceDO> xgImportMailTraceList) {
		saveBatch(xgImportMailTraceList);
	}


}
