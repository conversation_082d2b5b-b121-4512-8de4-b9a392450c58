package cn.com.xmmn.businesses.controller;

import cn.com.xmmn.businesses.service.InterfaceDeliveryService;
import cn.com.xmmn.businesses.service.InterfaceImportService;
import cn.com.xmmn.businesses.service.InterfaceTmsService;
import cn.com.xmmn.common.utils.GetTimeUtils;
import cn.com.xmmn.common.utils.PageUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Map;
/**
 * TMS订单表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-30 20:15:07
 */
@Controller
@RequestMapping("/businesses/interfaceTest")
public class interfaceController {

	@Autowired
	private InterfaceTmsService interfaceTmsService;
	@Autowired
	private InterfaceDeliveryService interfaceDeliveryService;
	@Autowired
	private InterfaceImportService interfaceImportService;

	
	@GetMapping()
	@RequiresPermissions("businesses:interfaceTest:interfaceTest")
	String OrdersTms(Model model){
		model.addAttribute("init", true);//初始化页面标识。进入页面不自动查询
		return "businesses/interfaceTest/interfaceTest";
	}


//	@ResponseBody
	@PostMapping("/list")
	@RequiresPermissions("businesses:interfaceTest:interfaceTest")
	public String list(@RequestParam Map<String, Object> params,Model model){
		String res = "";
		boolean init = Boolean.valueOf((String)params.get("init"));
		if(init){//初次进入页面不查询
			return "businesses/interfaceTest/interfaceTest";
		}
		String start = (String)params.get("dateStart");
		String end = (String)params.get("dateEnd");
		String interfaceType = (String)params.get("interfaceType");
		if("0".equals(interfaceType)) {
			//订单查询外部接口
			res = interfaceTmsService.getOrder(start += " 00:00:00", end += " 23:59:59");
		}else if("1".equals(interfaceType)){
			//投递订单查询外部接口
			res = interfaceDeliveryService.getDeliverOrder(start += " 00:00:00", end += " 23:59:59");
		}else if("2".equals(interfaceType)){
			//进口订单查询外部接口
			res = interfaceImportService.getOrderImport(start += " 00:00:00", end += " 23:59:59");
		}
		model.addAttribute("msg",res);
		model.addAttribute("dateStart",params.get("dateStart"));
		return "businesses/interfaceTest/interfaceTest";
	}


}
