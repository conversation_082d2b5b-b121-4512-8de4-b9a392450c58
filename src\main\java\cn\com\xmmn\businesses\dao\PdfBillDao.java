package cn.com.xmmn.businesses.dao;

import cn.com.xmmn.businesses.domain.ManualPriceDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

@Mapper
public interface PdfBillDao {

    @Select(" <script> select * from t_manual_price " +
            " where port_id in (" +
            " <foreach collection='portIdList' item='portId' separator=','>" +
            " #{portId}" +
            " </foreach>" +
            ") " +
            " and flight_number in (" +
            " <foreach collection='flightNoList' item='flightNo' separator=','>" +
            " #{flightNo}" +
            " </foreach>" +
            ") " +
            " </script>")
    List<ManualPriceDO> getManualPrice(@Param(value="portIdList") List<String> portIdList,@Param(value="flightNoList") List<String> flightNoList);
}
