package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.XgCarrierDealerRecivDao;
import cn.com.xmmn.businesses.domain.XgCarrierDealerRecivDO;
import cn.com.xmmn.businesses.service.XgCarrierDealerRecivService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 香港出口邮袋表;(TB_XG_CARRIER_DEALER_RECIV)表服务实现类
 * <AUTHOR> zjy
 * @date : 2024-12-27
 */
@Service
public class XgCarrierDealerRecivServiceImpl extends ServiceImpl<XgCarrierDealerRecivDao, XgCarrierDealerRecivDO> implements XgCarrierDealerRecivService {

    @Autowired
    private XgCarrierDealerRecivDao carrierDealerRecivDao;

    @Override
    public void save(List<XgCarrierDealerRecivDO> dealerRecivList) {
        saveBatch(dealerRecivList);
    }
}
