package cn.com.xmmn.businesses.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;



/**
 * 信息反馈推送日志
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-10 14:58:00
 */
@TableName(value = "t_feedback_records_push_log")
@Data
public class FeedbackRecordsPushLogDO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//主键
	private Integer id;
	//邮袋条码
	private String bagBarCode;
	//反馈id
	private Integer feebackId;
	//推送类型:1接收2启运3运抵4交邮
	private Integer pushType;
	//推送时间
	private Date createTime;
	//报错信息
	private String errMsg;
	//推送成功：0否1成功
	private Integer isOk;


}
