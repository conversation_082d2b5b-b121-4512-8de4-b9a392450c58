package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.common.PortEnum;
import cn.com.xmmn.businesses.domain.*;
import cn.com.xmmn.businesses.service.AirMailChannelBillsFristBillService;
import cn.com.xmmn.businesses.service.AirMailChannelBillsFristDetailService;
import cn.com.xmmn.common.utils.ShiroUtils;
import cn.com.xmmn.system.domain.UserDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.*;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

import cn.com.xmmn.businesses.dao.PortbatchDao;
import cn.com.xmmn.businesses.service.PortbatchService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

@Slf4j
@Service
public class PortbatchServiceImpl implements PortbatchService {
    @Autowired
    private PortbatchDao portbatchDao;

    @Autowired
    private PortbatchService batchService;

    @Autowired
    private AirMailChannelBillsFristBillService airMailChannelBillsFristBillService;

    @Autowired
    private AirMailChannelBillsFristDetailService airMailChannelBillsFristDetailService;

    @Override
    public PortbatchDO get(Integer id) {
        return portbatchDao.get(id);
    }

    @Override
    public List<PortbatchDO> list(Map<String, Object> map) {
        return portbatchDao.list(map);
    }

    @Override
    public int count(Map<String, Object> map) {
        return portbatchDao.count(map);
    }

    @Override
    public int save(PortbatchDO porbatch) {
        return portbatchDao.save(porbatch);
    }

    @Override
    public int update(PortbatchDO porbatch) {
        return portbatchDao.update(porbatch);
    }

    @Override
    public int remove(Integer id) {
        return portbatchDao.remove(id);
    }

    @Override
    public int batchRemove(Integer[] ids) {
        return portbatchDao.batchRemove(ids);
    }

    //下载导入模板
    public void downloadExcel(HttpServletResponse response) throws UnsupportedEncodingException {
/*//		String filepath = Thread.currentThread().getContextClassLoader().getResource("").getPath() + File.separator + "file";
		String filepath = "src/main/resources/file";
		log.info(filepath);
		String fileName = "port_import_template.xlsx";
		File file = new File(filepath, fileName);
		response.setContentType("application/force-download");
		response.addHeader("Content-Disposition", "attachment;fileName=" + new String(fileName.getBytes("UTF-8"), "ISO-8859-1"));     //解决文件中文名乱码问题
//		response.setCharacterEncoding("UTF-8");
		byte[] buffer = new byte[1024];
		try (FileInputStream fis = new FileInputStream(file);            //文件下载
			 BufferedInputStream bis = new BufferedInputStream(fis)) {
			OutputStream os = response.getOutputStream();

			int i = bis.read(buffer);
			while (i != -1) {
				os.write(buffer, 0, i);
				i = bis.read(buffer);
			}
			if (file != null || bis != null || os != null) {
				fis.close();
				bis.close();
				os.close();
			}


		} catch (IOException e) {
			e.printStackTrace();
		}*/

//特别注意：上面用file流读取文件只能在本地（windows）适用，项目打成jar包后再linux执行，会提示读取不到路径，linux想要读取jar包里的文件，只能通过以下流的形式来读
        String path = "file/port_import_template.xlsx";
        try {
            InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(path);
            OutputStream outputStream = response.getOutputStream();
            response.setContentType("application/x-download");
            response.addHeader("Content-Disposition", "attachment;filename=" + "port_import_template.xlsx");
            IOUtils.copy(inputStream, outputStream);
            outputStream.flush();
            inputStream.close();
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //导入新一代表
    //   上传用户数据
    @Transactional
    public int NewExcel(MultipartFile file, String modelType, String settlementType) throws Exception {
        Workbook workbook;
        String fileName = file.getOriginalFilename();
        String fileType = fileName.substring(fileName.indexOf(".") + 1);

        if (".xlsx".equals(fileType)) {//.xlsx类型的文件要用HSSFWorkbook这个来解析
            //        有内容的workbook工作薄
            workbook = new HSSFWorkbook(file.getInputStream());
        } else {//.xlsx的文件要用XSSFWorkbook来解析
            //        有内容的workbook工作薄
            workbook = new XSSFWorkbook(file.getInputStream());
        }


//        获取到第一个工作表
        Sheet sheet1 = workbook.getSheetAt(0);//核账单
        int lastRowIndex1 = sheet1.getLastRowNum();//当前sheet的最后一行的索引值
        int h1 = 0;

        //        获取到第2个工作表
        Sheet sheet2 = workbook.getSheetAt(1);//明细
        int lastRowIndex2 = sheet2.getLastRowNum();//当前sheet的最后一行的索引值
        int h2 = 0;
//        读取工作表中的内容
        Row row = null;
        //登录信息插批次表
        UserDO userDO = ShiroUtils.getUser();
        PortbatchDO batchDO = new PortbatchDO();
        long userId = userDO.getUserId();
        String UserName = userDO.getUsername();
        long deptId = userDO.getDeptId();
        String deptName = userDO.getDeptName();
        batchDO.setCreateUserId((int) userId);
        batchDO.setCreateUserName(UserName);
        batchDO.setCreateDeptId((int) deptId);
        batchDO.setCreateDeptName(deptName);
        batchDO.setCreateTime(new Date());
        batchDO.setSettlementType(settlementType);
        for (int i = 2; i <= lastRowIndex1 - 1; i++) {
            row = sheet1.getRow(i);
            if (row == null || row.getCell(0) == null) {
                continue;
            }
            h1++;
        }

        for (int i = 2; i <= lastRowIndex2 - 1; i++) {
            row = sheet2.getRow(i);
            if (row == null || row.getCell(0) == null) {
                continue;
            }
            h2++;
        }
        //log.info(str.substring(str.indexOf(".") + 1, str.lastIndexOf(".")));
        batchDO.setBillNum(h1);
        batchDO.setPortId(modelType);
        String portName = "";//口岸名称
		/*if ("CAN".equals(modelType)) {
			portName = "广州";
		} else if ("DGG".equals(modelType)) {
			portName = "东莞";
		} else if ("ZUH".equals(modelType)) {
			portName = "珠海";
		} else if ("SZX".equals(modelType)) {
			portName = "深圳";
		}*/
        Map<String, String> portamp = PortEnum.getPorts();//获取枚举值
        Set<Map.Entry<String, String>> entrySet = portamp.entrySet();
        for (Map.Entry<String, String> entry : entrySet) {
            if (entry.getKey().equals(modelType)) {
                portName = entry.getValue();
            }
        }
        batchDO.setPortName(portName);
        batchDO.setDetailNum(h2);
        batchDO.setChannelId(null);
        //获取渠道名称
        String biaotou = sheet1.getRow(0).getCell(0).getStringCellValue();
        String channelName = biaotou.substring(biaotou.indexOf("月") + 1, biaotou.lastIndexOf("核"));
        batchDO.setChannelName(channelName);
        batchService.save(batchDO);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sj = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>(); //用来存插入账单表成功的主键id（插入明细表是要用到），以及总包号（插入明细表的时候账单表跟明细表用总包号关联）
        log.info("开始解析并落库第一个sheet");
        log.info("sheet1行数lastRowIndex1=" + lastRowIndex1);
        for (int i = 2; i < h1 + 2; i++) {
            //log.info(i);
            row = sheet1.getRow(i);
            //判断为空的垃圾行
            if (row == null || row.getCell(0) == null) {
                continue;
            }
            //获取批次表的id,创建人id和姓名，创建部门id和名称
            int s = batchDO.getId();
            int createUserId = batchDO.getCreateUserId();
            String createUserName = batchDO.getCreateUserName();
            int createDeptId = batchDO.getCreateDeptId();
            String createDeptName = batchDO.getCreateDeptName();

            //开始解析excel   row.getCell(1)代表当前行第二列
            int serialNumber = new BigDecimal(row.getCell(0).getNumericCellValue()).intValue();//序号
            Date dateOfShipment = sdf.parse(sdf.format(row.getCell(1).getDateCellValue()));//发运日期
            String startPlace = row.getCell(2) == null ? "" : row.getCell(2).getStringCellValue();//出发地
            String airRoute = row.getCell(3) == null ? "" : row.getCell(3).getStringCellValue();//航线;日期+时间
            String packageNo = row.getCell(4) == null ? "" : new BigDecimal(row.getCell(4).getNumericCellValue()).toString();//总包号
            if (packageNo.length() < 4) {
                for (int j = 0; j < 4 - packageNo.length(); j++) {//由于.getNumericCellValue()获取的时候会把前面的0自动去掉，所以需要补0
                    packageNo = "0" + packageNo;
                }
            }
            int bags = new BigDecimal(row.getCell(5).getNumericCellValue()).intValue();//袋数
            String flightNumber = row.getCell(6) == null ? "" : row.getCell(6).getStringCellValue();//航班号
            BigDecimal weight = new BigDecimal(row.getCell(7).getNumericCellValue());//重量
            String emailType = row.getCell(8) == null ? "" : row.getCell(8).getStringCellValue();//邮件类型
            BigDecimal settlementPrice = new BigDecimal(row.getCell(9).getNumericCellValue());//结算单价
            BigDecimal settlementAmount = new BigDecimal(row.getCell(10).getNumericCellValue());//结算金额

            //赋值
            AirMailChannelBillsFristBillDO vo = new AirMailChannelBillsFristBillDO();
            vo.setRevision(null);
            vo.setCreateUserId(createUserId);
            vo.setCreateUserName(createUserName);
            vo.setCreateTime(new Date());
            vo.setUpdateUserId(null);
            vo.setUpdateUserName("");
            vo.setUpdateDeptId(null);
            vo.setUpdateDeptName("");
            vo.setSerialNumber(serialNumber);
            vo.setDateOfShipment(dateOfShipment);
            vo.setStartPlace(startPlace);
            vo.setAirRoute(airRoute);
            vo.setPackageNo(packageNo);
            vo.setBags(bags);
            vo.setWeight(weight);
            vo.setEmailType(emailType);
            vo.setSettlementPrice(settlementPrice);
            vo.setSettlementAmount(settlementAmount);
            vo.setBatchId(s);
            vo.setPortId(modelType);
            vo.setPortName(portName);
            vo.setChannelName(channelName);
            vo.setFlightNumber(flightNumber);
            //执行插入邮件空运渠道账单第一邮通账单表方法
            airMailChannelBillsFristBillService.save(vo);
            Map<String, Object> map = new HashMap<String, Object>();
            //接下去的三行代码是为插入明细表时插入关联的bills_id服务的
            map.put("billsId", vo.getId());
            map.put("packageNo", packageNo);
            list.add(map);
            //	log.info("test");
        }

        log.info("开始解析并落库第二个sheet");
        log.info("sheet2行数lastRowIndex2=" + lastRowIndex2);
        for (int i = 2; i < h2 + 2; i++) {
            //log.info(i);
            row = sheet2.getRow(i);
            //判断为空的垃圾行
            if (row == null || row.getCell(0) == null) {
                continue;
            }
            //获取批次表的id,创建人id和姓名，创建部门id和名称
            int s = batchDO.getId();
            int createUserId = batchDO.getCreateUserId();
            String createUserName = batchDO.getCreateUserName();
            int createDeptId = batchDO.getCreateDeptId();
            String createDeptName = batchDO.getCreateDeptName();


            //开始解析excel   row.getCell(1)代表当前行第二列
            int serialNumber = new BigDecimal(row.getCell(0).getNumericCellValue()).intValue();//序号
            String packageNo = row.getCell(1) == null ? "" : new BigDecimal(row.getCell(1).getNumericCellValue()).toString();//总包号
            if (packageNo.length() < 4) {
                for (int j = 0; j < 4 - packageNo.length(); j++) {//由于.getNumericCellValue()获取的时候会把前面的0自动去掉，所以需要补0
                    packageNo = "0" + packageNo;
                }
            }
            String bagsNo = row.getCell(2) == null ? "" : new BigDecimal(row.getCell(2).getNumericCellValue()).toString();//	袋号
            String sendOrg = row.getCell(3) == null ? "" : row.getCell(3).getStringCellValue();//	原寄局
            String arriveOrg = row.getCell(4) == null ? "" : row.getCell(4).getStringCellValue();//	寄达局
            Date dateOfShipment = sdf.parse(sdf.format(row.getCell(5).getDateCellValue()));//日期
            String airRoute = row.getCell(6) == null ? "" : row.getCell(6).getStringCellValue();//路由
            BigDecimal weight = new BigDecimal(row.getCell(7).getNumericCellValue());//重量
            String emailType = row.getCell(8) == null ? "" : row.getCell(8).getStringCellValue();//邮件类型
            String barcode = row.getCell(9) == null ? "" : row.getCell(9).getStringCellValue();//条码

            //赋值
            AirMailChannelBillsFristDetailDO vo = new AirMailChannelBillsFristDetailDO();
            vo.setRevision(null);
            vo.setCreateUserId(createUserId);
            vo.setCreateUserName(createUserName);
            vo.setCreateTime(new Date());
            vo.setUpdateUserId(null);
            vo.setUpdateUserName("");
            vo.setUpdateDeptId(null);
            vo.setUpdateDeptName("");
            vo.setSerialNumber(serialNumber);
            vo.setPackageNo(packageNo);
            vo.setBagsNo(bagsNo);
            vo.setSendOrg(sendOrg);
            vo.setArriveOrg(arriveOrg);
            vo.setDateOfShipment(dateOfShipment);
            vo.setAirRoute(airRoute);
            vo.setWeight(weight);
            vo.setEmailType(emailType);
            vo.setBarcode(barcode);
            //获取关联的账单id
            Map<String, Object> listMap = null;
            for (int k = 0; k < list.size(); k++) {
                listMap = list.get(k);
                String billPackageNo = (String) listMap.get("packageNo");
                if (packageNo.equals(billPackageNo)) {
                    int billNo = (int) listMap.get("billsId");
                    vo.setBillsId(billNo);
                }
            }
            //执行插入邮件空运渠道账单第一邮通明细表方法
            airMailChannelBillsFristDetailService.save(vo);
            //	log.info("test");
        }
        int c = batchDO.getId();
        return c;

    }

}
