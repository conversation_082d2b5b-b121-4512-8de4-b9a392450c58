package cn.com.xmmn.businesses.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 进口订单表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-30 12:26:11
 */
@Data
public class OrdersImportDO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//ID
	private Integer id;
	//乐观锁;数据版本号
	private Integer revision;
	//创建人id
	private Integer createUserId;
	//创建人
	private String createUserName;
	//创建部门id
	private Integer createDeptId;
	//创建部门
	private String createDeptName;
	//创建时间
	private Date createTime;
	//修改人id
	private Integer updateUserId;
	//修改人
	private String updateUserName;
	//修改部门id
	private Integer updateDeptId;
	//修改部门
	private String updateDeptName;
	//修改时间
	private Date updateTime;
	//shipwayId
	private String shipwayId;
	//orderTime
	private Date orderTime;
	//trackingNo
	private String trackingNo;
	//customerId
	private String customerId;
	//weight
	private BigDecimal weight;
	//customerName
	private String customerName;
	//shipwayName
	private String shipwayName;
	//feeList
	private List feeList;
	//totalFeesRmb
	private BigDecimal totalFeesRmb;
	//orderCreateTime
	private Date orderCreateTime;
	//itemName
	private String itemName;
	//currency
	private String currency;
	//quantity
	private Integer quantity;

	/**
	 * 设置：ID
	 */
	public void setId(Integer id) {
		this.id = id;
	}
	/**
	 * 获取：ID
	 */
	public Integer getId() {
		return id;
	}
	/**
	 * 设置：乐观锁;数据版本号
	 */
	public void setRevision(Integer revision) {
		this.revision = revision;
	}
	/**
	 * 获取：乐观锁;数据版本号
	 */
	public Integer getRevision() {
		return revision;
	}
	/**
	 * 设置：创建人id
	 */
	public void setCreateUserId(Integer createUserId) {
		this.createUserId = createUserId;
	}
	/**
	 * 获取：创建人id
	 */
	public Integer getCreateUserId() {
		return createUserId;
	}
	/**
	 * 设置：创建人
	 */
	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}
	/**
	 * 获取：创建人
	 */
	public String getCreateUserName() {
		return createUserName;
	}
	/**
	 * 设置：创建部门id
	 */
	public void setCreateDeptId(Integer createDeptId) {
		this.createDeptId = createDeptId;
	}
	/**
	 * 获取：创建部门id
	 */
	public Integer getCreateDeptId() {
		return createDeptId;
	}
	/**
	 * 设置：创建部门
	 */
	public void setCreateDeptName(String createDeptName) {
		this.createDeptName = createDeptName;
	}
	/**
	 * 获取：创建部门
	 */
	public String getCreateDeptName() {
		return createDeptName;
	}
	/**
	 * 设置：创建时间
	 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	/**
	 * 获取：创建时间
	 */
	public Date getCreateTime() {
		return createTime;
	}
	/**
	 * 设置：修改人id
	 */
	public void setUpdateUserId(Integer updateUserId) {
		this.updateUserId = updateUserId;
	}
	/**
	 * 获取：修改人id
	 */
	public Integer getUpdateUserId() {
		return updateUserId;
	}
	/**
	 * 设置：修改人
	 */
	public void setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
	}
	/**
	 * 获取：修改人
	 */
	public String getUpdateUserName() {
		return updateUserName;
	}
	/**
	 * 设置：修改部门id
	 */
	public void setUpdateDeptId(Integer updateDeptId) {
		this.updateDeptId = updateDeptId;
	}
	/**
	 * 获取：修改部门id
	 */
	public Integer getUpdateDeptId() {
		return updateDeptId;
	}
	/**
	 * 设置：修改部门
	 */
	public void setUpdateDeptName(String updateDeptName) {
		this.updateDeptName = updateDeptName;
	}
	/**
	 * 获取：修改部门
	 */
	public String getUpdateDeptName() {
		return updateDeptName;
	}
	/**
	 * 设置：修改时间
	 */
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	/**
	 * 获取：修改时间
	 */
	public Date getUpdateTime() {
		return updateTime;
	}
	/**
	 * 设置：shipwayId
	 */
	public void setShipwayId(String shipwayId) {
		this.shipwayId = shipwayId;
	}
	/**
	 * 获取：shipwayId
	 */
	public String getShipwayId() {
		return shipwayId;
	}
	/**
	 * 设置：orderTime
	 */
	public void setOrderTime(Date orderTime) {
		this.orderTime = orderTime;
	}
	/**
	 * 获取：orderTime
	 */
	public Date getOrderTime() {
		return orderTime;
	}
	/**
	 * 设置：trackingNo
	 */
	public void setTrackingNo(String trackingNo) {
		this.trackingNo = trackingNo;
	}
	/**
	 * 获取：trackingNo
	 */
	public String getTrackingNo() {
		return trackingNo;
	}
	/**
	 * 设置：customerId
	 */
	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}
	/**
	 * 获取：customerId
	 */
	public String getCustomerId() {
		return customerId;
	}
	/**
	 * 设置：weight
	 */
	public void setWeight(BigDecimal weight) {
		this.weight = weight;
	}
	/**
	 * 获取：weight
	 */
	public BigDecimal getWeight() {
		return weight;
	}
	/**
	 * 设置：customerName
	 */
	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}
	/**
	 * 获取：customerName
	 */
	public String getCustomerName() {
		return customerName;
	}
	/**
	 * 设置：shipwayName
	 */
	public void setShipwayName(String shipwayName) {
		this.shipwayName = shipwayName;
	}
	/**
	 * 获取：shipwayName
	 */
	public String getShipwayName() {
		return shipwayName;
	}
}
