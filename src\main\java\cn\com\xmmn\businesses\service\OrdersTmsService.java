package cn.com.xmmn.businesses.service;

import cn.com.xmmn.businesses.domain.OrdersTmsDO;

import java.util.List;
import java.util.Map;

/**
 * TMS订单表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-30 20:15:07
 */
public interface OrdersTmsService {
	
	OrdersTmsDO get(Integer id);
	
	List<OrdersTmsDO> list(Map<String, Object> map);

	Map<String, Object> countList(Map<String, Object> map);

	int count(Map<String, Object> map);
	
	int save(OrdersTmsDO ordersTms);
	
	int update(OrdersTmsDO ordersTms);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);

	//<!--统计分别按渠道、客户商业出口占比-->
	List<Map<String,Object>> percent(Map<String,Object> map);

	//<!--按月统计件数-->
	List<Map<String,Object>> totalCount(Map<String,Object> map);

	//<!--按月统计重量-->
	List<Map<String,Object>> totalWeight(Map<String,Object> map);

	//<!--按月统计收入-->
	List<Map<String,Object>> totalAmount(Map<String,Object> map);

	//查询环比升降
	List<Map<String,Object>> huanbiList(Map<String,Object> map);
}
