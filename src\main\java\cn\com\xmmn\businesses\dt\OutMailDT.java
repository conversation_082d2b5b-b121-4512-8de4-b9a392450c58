package cn.com.xmmn.businesses.dt;

import lombok.Data;

/**
 * 承运商邮袋接收明细搜索参数
 */
@Data
public class OutMailDT {

    //搜索的参数：已交航袋数（），已封发袋数（）
    private String queryParameter;

    //交换站省份组多个使用逗号隔开
    //private String provinceCodeStr;

    //交换站机构多个使用逗号隔开
    private String orgCodeStr;
    private String opOrgName;
    //交换站代码
    private String orgCode;
    private String oeDest;
    private String cltProvinceCodeStr;
    //业务种类代码
    private String cltProvinceCode;
    //寄达国家
    private String receiverCountryCode;

    //统计周期开始
    private String opTime404Start;

    //统计周期结束
    private String opTime404End;




    private int offset;
    // 每页条数
    private int limit;
}
