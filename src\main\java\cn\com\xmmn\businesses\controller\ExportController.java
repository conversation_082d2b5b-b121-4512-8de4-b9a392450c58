package cn.com.xmmn.businesses.controller;

import cn.com.xmmn.businesses.common.PortEnum;
import cn.com.xmmn.businesses.domain.AirMailChannelBillsFristBillDO;
import cn.com.xmmn.businesses.domain.AirMailChannelBillsFristDetailDO;
import cn.com.xmmn.businesses.domain.PortbatchDO;
import cn.com.xmmn.businesses.service.AirMailChannelBillsFristBillService;
import cn.com.xmmn.businesses.service.AirMailChannelBillsFristDetailService;
import cn.com.xmmn.businesses.service.ExportService;
import cn.com.xmmn.businesses.service.impl.ExportServiceImpl;
import cn.com.xmmn.common.utils.PageUtils;
import cn.com.xmmn.common.utils.Query;
import cn.com.xmmn.common.utils.R;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 口岸导入批次表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-22 01:30:58
 */
 
@Controller
@RequestMapping("/businesses/export")
public class ExportController {
	@Autowired
	private ExportService exportService;

	@Autowired
	private ExportServiceImpl exportServiceImpl;

	@Autowired
	private AirMailChannelBillsFristBillService airMailChannelBillsFristBillService;

	@Autowired
	private AirMailChannelBillsFristDetailService airMailChannelBillsFristDetailService;
	
	@GetMapping()
	@RequiresPermissions("businesses:export:export")
	String Portbatch(Model model){
		Map<String, String> map = PortEnum.getSettlementPorts();
		model.addAttribute("init", true);//初始化页面标识。进入页面不自动查询
		model.addAttribute("enumType", map);
		return "businesses/export/export";
	}
	
	@ResponseBody
	@GetMapping("/list")
	@RequiresPermissions("businesses:export:export")
	public PageUtils list(@RequestParam Map<String, Object> params){
		PageUtils pageUtils = null;
		boolean init = Boolean.valueOf((String)params.get("init"));
		if(init){//初次进入页面不查询
			return pageUtils;
		}
//		params.put("startDate",params.get("startDate"));//开始日期
//		params.put("endDate",params.get("endDate"));//结束日期
		//查询列表数据
        Query query = new Query(params);
		List<AirMailChannelBillsFristBillDO> portbatchList = exportService.list(query);
		int total = exportService.count(query);
		pageUtils = new PageUtils(portbatchList, total);
		return pageUtils;
	}
	
	@GetMapping("/add")
	@RequiresPermissions("businesses:portbatch:add")
	String add(){
	    return "businesses/portbatch/add";
	}

	@GetMapping("/edit/{id}")
	@RequiresPermissions("businesses:portbatch:edit")
	String edit(@PathVariable("id") Integer id,Model model){
		AirMailChannelBillsFristBillDO airMailChannelBillsFristBill = new AirMailChannelBillsFristBillDO();
		airMailChannelBillsFristBill.setBatchId(id);
		List billList = airMailChannelBillsFristBillService.detail(airMailChannelBillsFristBill);
		model.addAttribute("billList", billList);
	    return "businesses/portbatch/billDetail";
	}


	@GetMapping("/detail/{id}")
	@RequiresPermissions("businesses:export:detail")
	String detail(@PathVariable("id") Integer id,Model model){
		AirMailChannelBillsFristDetailDO airMailChannelBillsFristDetailDO = new AirMailChannelBillsFristDetailDO();
		airMailChannelBillsFristDetailDO.setBillsId(id);
		List detailList = airMailChannelBillsFristDetailService.detailOfBill(airMailChannelBillsFristDetailDO);
		model.addAttribute("detailList", detailList);
		return "businesses/export/exportDetail";
	}
	
	/**
	 * 保存
	 */
	@ResponseBody
	@PostMapping("/save")
	@RequiresPermissions("businesses:portbatch:add")
	public R save( AirMailChannelBillsFristBillDO portbatch){
		if(exportService.save(portbatch)>0){
			return R.ok();
		}
		return R.error();
	}
	/**
	 * 修改
	 */
	@ResponseBody
	@RequestMapping("/update")
	@RequiresPermissions("businesses:portbatch:edit")
	public R update( AirMailChannelBillsFristBillDO portbatch){
		exportService.update(portbatch);
		return R.ok();
	}
	
	/**
	 * 删除
	 */
	@PostMapping( "/remove")
	@ResponseBody
	@RequiresPermissions("businesses:portbatch:remove")
	public R remove( Integer id){
		if(exportService.remove(id)>0){
		return R.ok();
		}
		return R.error();
	}
	
	/**
	 * 删除
	 */
	@PostMapping( "/batchRemove")
	@ResponseBody
	@RequiresPermissions("businesses:portbatch:batchRemove")
	public R remove(@RequestParam("ids[]") Integer[] ids){
		exportService.batchRemove(ids);
		return R.ok();
	}

	@RequestMapping(value = "/exportExcel",method = RequestMethod.GET)
	public void exportExcel(@RequestParam Map<String, Object> map, HttpServletResponse response) throws IOException {
		List<AirMailChannelBillsFristBillDO> portbatchList = exportService.list(map);//账单
		List<AirMailChannelBillsFristDetailDO> detailList = exportService.detailList(map);//明细
		List<Map<String,Object>> airList = exportService.airList(map);//查询航线分组
			exportService.exportExcel(map,portbatchList,detailList,airList,response);
	}

}
