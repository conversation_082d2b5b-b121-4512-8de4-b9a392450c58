package cn.com.xmmn.businesses.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * 推送配置信息
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "push.track")
public class PushServiceConfig {
    //推送地址
    private String url;
    //标识
    private String identifier;
    //秘钥
    private String key;
}
