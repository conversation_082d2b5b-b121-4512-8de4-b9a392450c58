package cn.com.xmmn.businesses.task;


import cn.com.xmmn.businesses.service.FeedbackRecordsService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 定时推送反馈信息到新一代
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-08 15:45:24
 */
@Slf4j
@Component
public class FeedbackRecordsJob implements Job {


    @Autowired
    private FeedbackRecordsService feedbackRecordsService;

    @Value("${spring.profiles.active}")
    private String profile;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {

        if ("pro".equals(profile)) {

            log.info("触发反馈定时器");
            try {
                log.info("触发推送接收状态未推送的记录");
                // 推送接收状态未推送的记录
                feedbackRecordsService.pushReceiveStatus();

                log.info("触发推送启运状态未推送的记录");
                //推送启运状态未推送的记录
                feedbackRecordsService.pushDepartureStatus();

                //推送运抵状态未推送的记录
                log.info("触发推送运抵状态未推送的记录");
                feedbackRecordsService.pushDeliveryStatus();

                //推送交邮状态未推送的记录
                log.info("触发推送交邮状态未推送的记录");
                feedbackRecordsService.pushPostStatus();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
