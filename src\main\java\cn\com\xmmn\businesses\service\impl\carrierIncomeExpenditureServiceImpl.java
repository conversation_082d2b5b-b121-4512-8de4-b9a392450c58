package cn.com.xmmn.businesses.service.impl;


import cn.com.xmmn.businesses.dao.carrierIncomeExpenditureDao;
import cn.com.xmmn.businesses.domain.carrierIncomeExpenditureDO;
import cn.com.xmmn.businesses.service.carrierIncomeExpenditureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Service
public class carrierIncomeExpenditureServiceImpl implements carrierIncomeExpenditureService {
	@Autowired
	private carrierIncomeExpenditureDao carrierIncomeExpenditureDao;
	
	@Override
	public carrierIncomeExpenditureDO get(Integer id){
		return carrierIncomeExpenditureDao.get(id);
	}
	
	@Override
	public List<carrierIncomeExpenditureDO> list(Map<String, Object> map){
		return carrierIncomeExpenditureDao.list(map);
	}
	
	@Override
	public int count(Map<String, Object> map){
		return carrierIncomeExpenditureDao.count(map);
	}
	
	@Override
	public int save(carrierIncomeExpenditureDO carrierBillDetail){
		return carrierIncomeExpenditureDao.save(carrierBillDetail);
	}
	
	@Override
	public int update(carrierIncomeExpenditureDO carrierBillDetail){
		return carrierIncomeExpenditureDao.update(carrierBillDetail);
	}
	
	@Override
	public int remove(Integer id){
		return carrierIncomeExpenditureDao.remove(id);
	}
	
	@Override
	public int batchRemove(Integer[] ids){
		return carrierIncomeExpenditureDao.batchRemove(ids);
	}
	
}
