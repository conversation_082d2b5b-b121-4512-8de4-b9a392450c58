package cn.com.xmmn.example.controller;

import cn.com.xmmn.common.controller.BaseController;
import cn.com.xmmn.common.utils.R;
import cn.com.xmmn.common.utils.XssUtil;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 增强的XSS防护控制器
 * 
 * 按照安全扫描工具建议实现的XSS防护示例
 * 包含：数据验证、不同上下文编码、HttpOnly Cookie设置
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Controller
@RequestMapping("/example/enhanced")
public class EnhancedXssController extends BaseController {
    
    /**
     * 用户注册 - 展示数据验证和不同编码方式
     */
    @PostMapping("/register")
    @ResponseBody
    public R register(HttpServletRequest request, HttpServletResponse response) {
        try {
            // 1. 获取用户输入
            String username = request.getParameter("username");
            String email = request.getParameter("email");
            String age = request.getParameter("age");
            String phone = request.getParameter("phone");
            String description = request.getParameter("description");
            
            // 2. 数据验证（如年龄只能是数字）
            if (!XssUtil.isValidNumber(age, 1, 150)) {
                return R.error("年龄必须是1-150之间的数字");
            }
            
            if (!XssUtil.isValidEmail(email)) {
                return R.error("邮箱格式不正确");
            }
            
            if (!XssUtil.isValidPhone(phone)) {
                return R.error("手机号格式不正确");
            }
            
            // 3. XSS防护：清洗用户输入
            username = XssUtil.clean(username);
            email = XssUtil.clean(email);
            description = XssUtil.cleanStrict(description); // 描述使用严格清洗
            
            // 4. 验证清洗后的数据安全性
            if (!XssUtil.isSafe(username) || !XssUtil.isSafe(email)) {
                return R.error("输入包含不安全内容");
            }
            
            // 5. 设置HttpOnly Cookie（防止Cookie劫持）
            XssUtil.setHttpOnlyCookie(response, "user_session", "session_value", 
                                     3600, ".example.com", "/");
            
            // 6. 保存用户信息（这里省略实际保存逻辑）
            // userService.save(username, email, age, phone, description);
            
            return R.ok("注册成功");
            
        } catch (Exception e) {
            return R.error("注册失败: " + XssUtil.getSafeErrorMessage(e.getMessage()));
        }
    }
    
    /**
     * 显示用户信息 - 展示不同上下文的输出编码
     */
    @GetMapping("/profile")
    public String profile(HttpServletRequest request, Model model) {
        // 获取用户输入
        String username = request.getParameter("username");
        String bio = request.getParameter("bio");
        String website = request.getParameter("website");
        String style = request.getParameter("style");
        
        // 清洗输入
        username = XssUtil.clean(username);
        bio = XssUtil.clean(bio);
        website = XssUtil.clean(website);
        style = XssUtil.clean(style);
        
        // 根据不同的HTML上下文进行恰当的输出编码
        
        // 1. HTML标签内容编码 - 用于 <div>{{content}}</div>
        String htmlSafeUsername = XssUtil.encodeForHTML(username);
        String htmlSafeBio = XssUtil.encodeForHTML(bio);
        
        // 2. HTML属性编码 - 用于 <input value="{{value}}">
        String attrSafeUsername = XssUtil.encodeForHTMLAttribute(username);
        
        // 3. JavaScript编码 - 用于 <script>var name = '{{name}}';</script>
        String jsSafeUsername = XssUtil.encodeForJavaScript(username);
        
        // 4. CSS编码 - 用于 <style>.user::before { content: '{{content}}'; }</style>
        String cssSafeStyle = XssUtil.encodeForCSS(style);
        
        // 5. URL编码 - 用于 <a href="/user?name={{name}}">
        String urlSafeUsername = XssUtil.encodeForURL(username);
        String urlSafeWebsite = XssUtil.encodeForURL(website);
        
        // 设置到模型中
        model.addAttribute("htmlUsername", htmlSafeUsername);
        model.addAttribute("htmlBio", htmlSafeBio);
        model.addAttribute("attrUsername", attrSafeUsername);
        model.addAttribute("jsUsername", jsSafeUsername);
        model.addAttribute("cssStyle", cssSafeStyle);
        model.addAttribute("urlUsername", urlSafeUsername);
        model.addAttribute("urlWebsite", urlSafeWebsite);
        
        return "example/profile";
    }
    
    /**
     * 搜索功能 - 展示输入过滤和验证
     */
    @GetMapping("/search")
    public String search(HttpServletRequest request, Model model) {
        String keyword = request.getParameter("keyword");
        String category = request.getParameter("category");
        
        if (keyword != null) {
            // 过滤特殊字符
            keyword = XssUtil.filterSpecialChars(keyword);
            
            // XSS清洗
            keyword = XssUtil.clean(keyword);
            
            // 验证安全性
            if (!XssUtil.isSafe(keyword)) {
                model.addAttribute("error", "搜索关键词包含不安全内容");
                return "error";
            }
            
            // 限制长度
            if (keyword.length() > 100) {
                keyword = keyword.substring(0, 100);
            }
        }
        
        if (category != null) {
            category = XssUtil.clean(category);
        }
        
        // 输出编码
        model.addAttribute("safeKeyword", XssUtil.encodeForHTML(keyword));
        model.addAttribute("safeCategory", XssUtil.encodeForHTML(category));
        model.addAttribute("urlKeyword", XssUtil.encodeForURL(keyword));
        
        return "example/search";
    }
    
    /**
     * API接口 - 展示JSON数据的XSS防护
     */
    @PostMapping("/api/comment")
    @ResponseBody
    public R addComment(@RequestBody CommentRequest request) {
        try {
            // 验证必填字段
            if (request.getContent() == null || request.getContent().trim().isEmpty()) {
                return R.error("评论内容不能为空");
            }
            
            // 长度验证
            if (request.getContent().length() > 1000) {
                return R.error("评论内容不能超过1000字符");
            }
            
            // XSS清洗
            String safeContent = XssUtil.cleanStrict(request.getContent());
            String safeAuthor = XssUtil.clean(request.getAuthor());
            
            // 安全验证
            if (!XssUtil.isSafe(safeContent) || !XssUtil.isSafe(safeAuthor)) {
                return R.error("评论包含不安全内容");
            }
            
            // 保存评论（省略实际保存逻辑）
            // commentService.save(safeContent, safeAuthor);
            
            return R.ok("评论添加成功");
            
        } catch (Exception e) {
            return R.error("添加评论失败: " + XssUtil.getSafeErrorMessage(e.getMessage()));
        }
    }
    
    /**
     * 评论请求对象
     */
    public static class CommentRequest {
        private String content;
        private String author;
        
        public String getContent() {
            return content;
        }
        
        public void setContent(String content) {
            this.content = content;
        }
        
        public String getAuthor() {
            return author;
        }
        
        public void setAuthor(String author) {
            this.author = author;
        }
    }
}
