package cn.com.xmmn.businesses.service;

import cn.com.xmmn.businesses.domain.CarrierDealerRecivDO;
import cn.com.xmmn.businesses.domain.OutMailDO;
import cn.com.xmmn.businesses.dt.CarrierDealerRecivDetailDT;
import cn.com.xmmn.businesses.dt.OutMailDT;


import java.util.List;
import java.util.Map;

/**
 * 出口邮件表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-13 14:08:34
 */
public interface OutMailService {
	
	OutMailDO get(Integer id);
	
	List<OutMailDO> list(Map<String, Object> map);

	List<OutMailDO> hjList(Map<String, Object> map);
	

	int update(OutMailDO outMail);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);
	/**
	 * 封发时间邮袋详情
	 * @param outMailDT
	 * @return
	 */
	List<OutMailDO> detail(OutMailDT outMailDT);

	/**
	 * 封发时间邮袋总数
	 * @param dealerRecivDetailDT
	 * @return
	 */
	Integer detailCount(OutMailDT dealerRecivDetailDT);
}
