package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.AirMailChannelBillsFristBillDao;
import cn.com.xmmn.businesses.dao.AirMailChannelBillsFristDetailDao;
import cn.com.xmmn.businesses.dao.ExportDao;
import cn.com.xmmn.businesses.domain.AirMailChannelBillsFristBillDO;
import cn.com.xmmn.businesses.domain.AirMailChannelBillsFristDetailDO;
import cn.com.xmmn.businesses.service.ExportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Service
public class ExportServiceImpl implements ExportService {
    @Autowired
    private ExportDao ExportDao;

    @Autowired
    private AirMailChannelBillsFristBillDao airMailChannelBillsFristBillDao;

    @Autowired
    private AirMailChannelBillsFristDetailDao airMailChannelBillsFristDetailDao;

    @Override
    public AirMailChannelBillsFristBillDO get(Integer id) {
        return ExportDao.get(id);
    }

    @Override
    public List<AirMailChannelBillsFristBillDO> list(Map<String, Object> map) {
        return airMailChannelBillsFristBillDao.billList(map);
    }

    @Override
    public List<AirMailChannelBillsFristDetailDO> detailList(Map<String, Object> map) {
        return airMailChannelBillsFristDetailDao.detailList(map);
    }

    @Override
    public List<Map<String, Object>> airList(Map<String, Object> map) {
        return airMailChannelBillsFristDetailDao.airList(map);
    }

    @Override
    public int count(Map<String, Object> map) {
        return airMailChannelBillsFristBillDao.billCount(map);
    }

    @Override
    public int save(AirMailChannelBillsFristBillDO AirMailChannelBillsFristBill) {
        return ExportDao.save(AirMailChannelBillsFristBill);
    }

    @Override
    public int update(AirMailChannelBillsFristBillDO AirMailChannelBillsFristBill) {
        return ExportDao.update(AirMailChannelBillsFristBill);
    }

    @Override
    public int remove(Integer id) {
        return ExportDao.remove(id);
    }

    @Override
    public int batchRemove(Integer[] ids) {
        return ExportDao.batchRemove(ids);
    }

    //导出
    @Override
    public void exportExcel(Map<String, Object> map, List<AirMailChannelBillsFristBillDO> list, List<AirMailChannelBillsFristDetailDO> detailList, List<Map<String, Object>> airList, HttpServletResponse response) {


        HSSFWorkbook wb = new HSSFWorkbook();//创建HSSFWorkbook对象,  excel的文档对象

        //表头字体
        Font headerFont = wb.createFont();
        headerFont.setFontName("宋体");
        headerFont.setFontHeightInPoints((short) 18);
        headerFont.setBold(true);
        headerFont.setColor(Font.COLOR_NORMAL);
        //正文字体
        Font contextFont = wb.createFont();
        contextFont.setFontName("宋体");
        contextFont.setFontHeightInPoints((short) 12);
        headerFont.setBold(true);
        //表头样式，左右上下居中
        CellStyle headerStyle = wb.createCellStyle();
        headerStyle.setFont(headerFont);
        // 左右居中
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        // 上下居中
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setLocked(true);
        // 自动换行
        headerStyle.setWrapText(false);
        //单元格样式，左右上下居中 边框
        CellStyle commonStyle = wb.createCellStyle();
        commonStyle.setFont(contextFont);
        // 左右居中
        commonStyle.setAlignment(HorizontalAlignment.CENTER);
        // 上下居中
        commonStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        commonStyle.setLocked(true);
        // 自动换行
        commonStyle.setWrapText(false);


        //货币格式
        HSSFCellStyle huobiStyle = wb.createCellStyle();
        HSSFDataFormat format = wb.createDataFormat();
        //设置货比格式为￥开头的且带有两位小数的格式
        huobiStyle.setDataFormat(format.getFormat("￥#,##0.00"));

        //数值格式
        HSSFCellStyle shuzhi = wb.createCellStyle();
        //设置货比格式为￥开头的且带有两位小数的格式
        shuzhi.setDataFormat(format.getBuiltinFormat("#,##0.00"));

        //单元格样式，左右上下居中 边框
        CellStyle commonWrapStyle = wb.createCellStyle();
        commonWrapStyle.setFont(contextFont);
        //单元格样式，竖向 边框
        CellStyle verticalStyle = wb.createCellStyle();
        verticalStyle.setFont(contextFont);
        CellStyle commonStyleNoBorder = wb.createCellStyle();
        commonStyleNoBorder.setFont(contextFont);
        commonStyleNoBorder.setLocked(true);
        // 自动换行
        commonStyleNoBorder.setWrapText(false);
        CellStyle alignLeftStyle = wb.createCellStyle();
        alignLeftStyle.setFont(contextFont);
        alignLeftStyle.setLocked(true);
        // 自动换行
        alignLeftStyle.setWrapText(false);
        //单元格样式，左对齐 无边框
        CellStyle alignLeftNoBorderStyle = wb.createCellStyle();
        alignLeftNoBorderStyle.setFont(contextFont);
        alignLeftNoBorderStyle.setLocked(true);
        // 自动换行
        alignLeftNoBorderStyle.setWrapText(false);
        //单元格样式，右对齐
        CellStyle alignRightStyle = wb.createCellStyle();
        alignRightStyle.setFont(contextFont);
        alignRightStyle.setLocked(true);
        // 自动换行
        alignRightStyle.setWrapText(false);
        alignRightStyle.setAlignment(HorizontalAlignment.RIGHT);

        //List<Map<String, Object>> feeList = new ArrayList<Map<String, Object>>();//存放费用明细，核账单查询后放入feeList，生成明细sheet的时候可以直接用这个list，不需要再次查询数据库
        /**********四个导出模板（广州、东莞，深圳，珠海，根据页面选择的口岸来决定走哪个模板通道）**************/
        String qianzhui = "(";//导出文件名前缀
        if ("CAN".equals(map.get("modelType"))) {//广州的导出模板
            qianzhui += "广航";
            //调用方法生成第一个核账单sheet
            log.info("开始生成广州excel第一个sheet：核账单");
            guangZhouBill(wb, headerStyle, commonStyle, alignRightStyle, huobiStyle, map, list, shuzhi);
            log.info("生成广州excel第一个sheet：核账单，结束");
            log.info("开始生成广州excel第二个sheet：核对明细");
            //调用方法生成第二个核对明细sheet
            guangZhouDetail(wb, headerStyle, commonStyle, alignRightStyle, map, detailList, shuzhi, huobiStyle);
            log.info("开始生成广州excel第二个sheet：核对明细  结束");
            log.info("开始生成广州excel第3-N个sheet：核对明细拆分");
            //调用方法生成后续的按航线分类的循环sheet
            for (int i = 0; i < airList.size(); i++) {//airList.size有多大，就创建多少个sheet，用航线最后三位来区分
                //获取航线最后三位
                Map<String, Object> airMap = airList.get(i);
                String air = (String) airMap.get("air");
//            BigDecimal weight = (BigDecimal) airMap.get("weight");
                guangZhouAirList(wb, headerStyle, commonStyle, alignRightStyle, map, detailList, air, shuzhi);
            }
            log.info("生成广州excel第3-N个sheet：核对明细拆分  结束");
        } else if ("DGG".equals(map.get("modelType"))) {//东莞的导出模板
            qianzhui += "东莞";
            //调用方法生成第一个核账单sheet
            log.info("开始生成东莞excel第一个sheet：核账单");
            dongGuanBill(wb, headerStyle, commonStyle, alignRightStyle, huobiStyle, map, list, shuzhi);
            log.info("生成东莞excel第一个sheet：核账单，结束");
            log.info("开始生成东莞excel第二个sheet：核对明细");
            //调用方法生成第二个核对明细sheet
            dongGuanDetail(wb, headerStyle, commonStyle, alignRightStyle, map, detailList, shuzhi, huobiStyle);
            log.info("开始生成东莞excel第二个sheet：核对明细  结束");
            log.info("开始生成东莞excel第3-N个sheet：核对明细拆分");
            //调用方法生成后续的按航线分类的循环sheet
            for (int i = 0; i < airList.size(); i++) {//airList.size有多大，就创建多少个sheet，用航线最后三位来区分
                //获取航线最后三位
                Map<String, Object> airMap = airList.get(i);
                String air = (String) airMap.get("air");
//            BigDecimal weight = (BigDecimal) airMap.get("weight");
                dongGuanAirList(wb, headerStyle, commonStyle, alignRightStyle, map, detailList, air, shuzhi);
            }
            log.info("生成东莞excel第3-N个sheet：核对明细拆分  结束");
        } else if ("ZUH".equals(map.get("modelType"))) {//珠海的导出模板
            qianzhui += "珠海";
            //调用方法生成第一个核账单sheet
            log.info("开始生成珠海excel第一个sheet：核账单");
            zhuHaiBill(wb, headerStyle, commonStyle, alignRightStyle, huobiStyle, map, list, shuzhi);
            log.info("生成珠海excel第一个sheet：核账单，结束");
            log.info("开始生成珠海excel第二个sheet：核对明细");
            //调用方法生成第二个核对明细sheet
            zhuHaiDetail(wb, headerStyle, commonStyle, alignRightStyle, map, detailList, shuzhi, huobiStyle);
            log.info("开始生成珠海excel第二个sheet：核对明细  结束");
        } else {//深圳的导出模板
            qianzhui += "深航";
            //调用方法生成第一个核账单sheet
            log.info("开始生成深圳excel第一个sheet：核账单");
            shenZhenBill(wb, headerStyle, commonStyle, alignRightStyle, huobiStyle, map, list, shuzhi);
            log.info("生成深圳excel第一个sheet：核账单，结束");
            log.info("开始生成深圳excel第二个sheet：核对明细");
            //调用方法生成第二个核对明细sheet
            shenZhenDetail(wb, headerStyle, commonStyle, alignRightStyle, map, detailList, shuzhi, huobiStyle);
            log.info("开始生成深圳excel第二个sheet：核对明细  结束");
        }

        //导出
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");


        if ("0".equals(map.get("settlementType"))) {
            qianzhui += "黄包)";
        } else {
            qianzhui += "蓝包)";
        }
        String fileName = qianzhui + map.get("dateStart") + "——" + map.get("dateEnd") + "中国快递服务有限公司核账单明细" + ".xlsx";
        try {
            fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        // 文件名
//		String fileName = map.get("dateStart")+"————"+ map.get("dateEnd")+  "中国快递服务有限公司核账单明细"+".xlsx";
        response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
        OutputStream stream = null;
        try {
            stream = response.getOutputStream();
        } catch (IOException e) {
            e.printStackTrace();
        }
        try {
            if (null != wb && null != stream) {
                wb.write(stream);
                stream.close();
            }
        } catch (Exception e) {
            log.info("excel文档导出错误-异常信息：" + e);
        }

    }

    //口岸导出核账单sheet页（广州模板）
    public void guangZhouBill(HSSFWorkbook wb, CellStyle headerStyle, CellStyle commonStyle, CellStyle alignRightStyle, CellStyle huobiStyle, Map<String, Object> map, List<AirMailChannelBillsFristBillDO> list, CellStyle shuzhi) {
        // 创建sheet
        Sheet sheet = wb.createSheet("核账单");

        // 行号
        int rowNum = 0;
        //设置列宽
        for (int i = 0; i < 12; i++) {
            if (i == 2 || i == 4 || i == 10 || i == 11) {
                sheet.setColumnWidth(i, 6000);
            } else {
                sheet.setColumnWidth(i, 4000);
            }
        }

		/*//第一行
		Row r0 = sheet.createRow(rowNum++);
		r0.setHeight((short) 800);
		Cell c00 = r0.createCell(0);
		c00.setCellValue("表二：");
		c00.setCellStyle(alignLeftNoBorderStyle);
		//合并单元格
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 8));*/
        //第一行中国快递服务有限公司核账单
        Row r0 = sheet.createRow(rowNum++);
        r0.setHeight((short) 600);
        Cell c0 = r0.createCell(0);
        c0.setCellValue("中国快递服务有限公司核账单");
        c0.setCellStyle(headerStyle);
        //合并单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 12));
        //第二行 结算周期
        Row r1 = sheet.createRow(rowNum++);
        r1.setHeight((short) 500);
        Cell c1 = r1.createCell(0);
        c1.setCellValue("结算周期：" + map.get("dateStart") + "───" + map.get("dateEnd"));
        c1.setCellStyle(alignRightStyle);
        //合并单元格
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 12));
        //第三行  表头
        Row r2 = sheet.createRow(rowNum++);
        r2.setHeight((short) 500);
        String[] rowSecond = {"序号", "出发地", "航线", "邮件类型", "航班", "发运费用", "", "", "附加费用", "", "", "合计金额", "备注"};

        for (int i = 0; i < rowSecond.length; i++) {
            Cell tempCell = r2.createCell(i);
            tempCell.setCellValue(rowSecond[i]);
            tempCell.setCellStyle(commonStyle);
            tempCell.setCellStyle(commonStyle);
        }
        //第4行  合并行
        Row r3 = sheet.createRow(rowNum++);
        String[] rowSecond4 = {"结算重量（KG)", "结算单价", "金额", "结算重量（KG)", "结算单价", "金额"};
        for (int i = 0; i < rowSecond4.length; i++) {
            Cell tempCell = r3.createCell(i + 5);
            tempCell.setCellValue(rowSecond4[i]);
            tempCell.setCellStyle(commonStyle);
        }
        //合并单元格   //"序号", "出发地", "航线","邮件类型","航班" ,"发运费用","","","附加费用","","","合计金额","备注"
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 0, 0));
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 1, 1));
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 2, 2));
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 3, 3));
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 4, 4));
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 5, 7));//发运费用
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 8, 10));//附加费用
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 11, 11));
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 12, 12));
        //开始写正文

        BigDecimal weight = new BigDecimal(0);
        BigDecimal totalWeight = new BigDecimal(0);//累计金额

        String rowBeginString = CellReference.convertNumToColString(7); //将第8列转成ABC列，插入求和公式用
        String rowEndString = CellReference.convertNumToColString(10); //将第11列转成ABC列，插入求和公式用
        for (int i = 0; i < list.size(); i++) {
            AirMailChannelBillsFristBillDO vo = list.get(i);
            weight = vo.getWeight();
//            String airRoute = vo.getAirRoute() == null ? "": vo.getAirRoute().substring(vo.getAirRoute().length() - 3);//取航线最后三位
//            String flightNumber = vo.getFlightNumber() == null ? "":vo.getFlightNumber().substring(0, 5);//取航班号前五位
            String airRoute = "";
            //取航线最后三位
            if (vo.getAirRoute() != null && !"".equals(vo.getAirRoute()) && vo.getAirRoute().length() >= 3) {
                airRoute = vo.getAirRoute().substring(vo.getAirRoute().length() - 3);
            }
            String flightNumber = "";
            //取航班号前五位
            if (vo.getFlightNumber() != null && !"".equals(vo.getFlightNumber()) && vo.getFlightNumber().length() >= 5) {
                flightNumber = vo.getFlightNumber().substring(0, 5);
            }
            Date dateOfShipment = vo.getDateOfShipment(); //发运日期
            String emailType = "";
            String type = vo.getEmailType();//邮件类型
            if ("EMS".equals(type)) {
                emailType = "01";
            } else if ("E邮宝".equals(type) || "E 邮宝".equals(type)) {
                emailType = "02";
            } else if ("航空函件".equals(type)) {
                emailType = "03";
            } else if ("包裹".equals(type)) {
                emailType = "04";
            } else if ("水陆".equals(type)) {
                emailType = "05";
            }
            Map<String, Object> mapTemp = new HashMap<String, Object>();
            mapTemp.put("portId", airRoute);
            mapTemp.put("flightNumber", flightNumber);
            mapTemp.put("dateOfShipment", dateOfShipment);
            mapTemp.put("emailType", emailType);
            //查询航运费用明细
            List<Map<String, Object>> feeTemp = airMailChannelBillsFristBillDao.feeList(mapTemp);
            //存放费用明细，核账单查询后放入feeList，生成明细sheet的时候可以直接用这个list，不需要再次查询数据库
//            feeList.addAll(feeTemp);
            BigDecimal shippingPrice = BigDecimal.valueOf(0.00);
            BigDecimal addPrice = BigDecimal.valueOf(0.00);
            if (feeTemp.size() > 0) {
                shippingPrice = (BigDecimal) feeTemp.get(0).get("shippingPrice");//发运费单价
                addPrice = (BigDecimal) feeTemp.get(0).get("addPrice");//附加费单价
            }
            Row tempRow = sheet.createRow(rowNum++);
            tempRow.setRowStyle(commonStyle);
            //列表数据
            tempRow.createCell(0).setCellValue(i + 1);
            tempRow.createCell(1).setCellValue(vo.getStartPlace());//出发地
            tempRow.createCell(2).setCellValue(vo.getAirRoute());//航线
            tempRow.createCell(3).setCellValue(vo.getEmailType());//邮件类型
            tempRow.createCell(4).setCellValue(vo.getFlightNumber());//航班
            tempRow.createCell(5).setCellFormula(weight.setScale(2, BigDecimal.ROUND_HALF_UP).toString());//发运结算重量（KG)
            tempRow.getCell(5).setCellStyle(shuzhi);
            Cell tempCell = tempRow.createCell(6);
            tempCell.setCellFormula(shippingPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());//发运结算单价
            tempCell.setCellStyle(huobiStyle);

            tempRow.createCell(7).setCellFormula(CellReference.convertNumToColString(5) + "" + rowNum + "*" + CellReference.convertNumToColString(6) + "" + rowNum);//发运金额 拼接公式
            tempRow.getCell(7).setCellStyle(huobiStyle);
            tempRow.createCell(8).setCellFormula(weight.setScale(2, BigDecimal.ROUND_HALF_UP).toString());//费用的结算重量（KG)
            tempRow.createCell(9).setCellFormula(addPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());//费用的结算单价
            tempRow.getCell(8).setCellStyle(shuzhi);
            tempRow.getCell(9).setCellStyle(huobiStyle);
            tempRow.createCell(10).setCellFormula(CellReference.convertNumToColString(8) + "" + rowNum + "*" + CellReference.convertNumToColString(9) + "" + rowNum);//费用金额
            tempRow.getCell(10).setCellStyle(huobiStyle);
            Cell tempHeji = tempRow.createCell(11);//合计金额
            String sumstring = rowBeginString + "" + rowNum + "+" + rowEndString + "" + rowNum;//求和公式
            tempHeji.setCellFormula(sumstring);
            tempHeji.setCellStyle(huobiStyle);
//            tempRow.createCell(11).setCellValue(0);//合计金额
            if("广州".equals(vo.getPortName())) {
                tempRow.createCell(12).setCellValue("");//备注
            }else{
                tempRow.createCell(12).setCellValue(vo.getPortName());//备注
            }
            totalWeight.add(weight);
        }
        Row heji = sheet.createRow(rowNum);//合计行
        heji.createCell(0).setCellValue("Total:");
        heji.getCell(0).setCellStyle(headerStyle);
        heji.createCell(1).setCellValue("");
        heji.createCell(2).setCellValue("");
        heji.createCell(3).setCellValue("");
        heji.createCell(4).setCellValue("");
        heji.createCell(5).setCellFormula("SUM(" + CellReference.convertNumToColString(5) + "5:" + CellReference.convertNumToColString(5) + rowNum + ")");
//        heji.createCell(6).setCellValue("");
        String colString = CellReference.convertNumToColString(6); //将当前行长度转成ABC列
        String sumstring = "SUM(" + colString + "5:" + colString + rowNum + ")";//求和公式
//该求和公式的意思就是：sum（第几列+第几个 ： 第几列+需要累计到的第几个单元格位置）
//        Cell tempCell = heji.createCell(6);
//        tempCell.setCellFormula(sumstring);
////        tempCell.setCellValue(sumstring);
//        tempCell.setCellStyle(huobiStyle);
        heji.createCell(7).setCellFormula("SUM(" + CellReference.convertNumToColString(7) + "5:" + CellReference.convertNumToColString(7) + rowNum + ")");
        heji.getCell(7).setCellStyle(huobiStyle);
        heji.createCell(8).setCellFormula("SUM(" + CellReference.convertNumToColString(8) + "5:" + CellReference.convertNumToColString(8) + rowNum + ")");
        heji.createCell(10).setCellFormula("SUM(" + CellReference.convertNumToColString(10) + "5:" + CellReference.convertNumToColString(10) + rowNum + ")");
        heji.getCell(10).setCellStyle(huobiStyle);
        heji.createCell(11).setCellFormula(rowBeginString + "" + (rowNum + 1) + "+" + rowEndString + "" + (rowNum + 1));
        heji.getCell(11).setCellStyle(huobiStyle);
        heji.createCell(12).setCellValue("");
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 2));
        rowNum += 2;

        //开始拼接sheet页底部信息
        //公章
        Row gongzhang = sheet.createRow(rowNum);//17
        gongzhang.createCell(2).setCellValue("经办部门盖章：（公章）");
        gongzhang.createCell(7).setCellValue("制表单位：");
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 7, 8));
        rowNum++;
        //部门负责人
        Row bumen = sheet.createRow(rowNum);
        bumen.createCell(2).setCellValue("部门负责人:");
        bumen.createCell(7).setCellValue("部门负责人:");
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum + 2, 2, 3));
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum + 2, 7, 7));

        //会记审核
        rowNum += 3;
        Row kuiji = sheet.createRow(rowNum);
        kuiji.createCell(2).setCellValue("会计审核:");
        kuiji.createCell(7).setCellValue("制表人:");
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum + 2, 2, 3));
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum + 2, 7, 7));

        //业务主管
        rowNum += 3;
        Row yewu = sheet.createRow(rowNum);
        yewu.createCell(2).setCellValue("业务主管:");
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum + 2, 2, 3));

        //业务主管
        rowNum += 3;
        Row fuheren = sheet.createRow(rowNum);
        fuheren.createCell(2).setCellValue("复核:");
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum + 2, 2, 3));

        //业务主管
        rowNum += 3;
        Row jingbanren = sheet.createRow(rowNum);
        jingbanren.createCell(2).setCellValue("经办人:");
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum + 2, 2, 3));

        //联系电话
        rowNum += 3;
        Row phone = sheet.createRow(rowNum);
        phone.createCell(2).setCellValue("联系电话:");
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum + 1, 2, 3));
    }

    //口岸导出核对明细sheet页（广州模板）
    public void guangZhouDetail(HSSFWorkbook wb, CellStyle headerStyle, CellStyle commonStyle, CellStyle alignRightStyle, Map<String, Object> map, List<AirMailChannelBillsFristDetailDO> list, CellStyle shuzhi, CellStyle huobiStyle) {
        // 创建sheet
        Sheet sheet2 = wb.createSheet("核对明细");

        // 行号
        int rowNum = 0;
        //设置列宽
        for (int i = 0; i < 12; i++) {
            if (i == 2 || i == 4 || i == 10 || i == 11) {
                sheet2.setColumnWidth(i, 6000);
            } else {
                sheet2.setColumnWidth(i, 4000);
            }
        }

        //第一行中国快递服务有限公司核账单
        Row r0 = sheet2.createRow(rowNum++);
        r0.setHeight((short) 600);
        Cell c0 = r0.createCell(0);
        c0.setCellValue(map.get("dateStart") + "───" + map.get("dateEnd") + "中国快递服务有限公司核账单明细");
        c0.setCellStyle(headerStyle);
        //合并单元格
        sheet2.addMergedRegion(new CellRangeAddress(0, 0, 0, 11));

        //第二行  表头
        Row r1 = sheet2.createRow(rowNum++);
        r1.setHeight((short) 500);
        String[] rowSecond = {"序号", "发运日期", "航班号", "原寄局", "航线", "邮件类型", "总包", "袋数", "重量", "结算单价", "附加费单价", "结算金额", "备注"};
        for (int i = 0; i < rowSecond.length; i++) {
            Cell tempCell = r1.createCell(i);
            tempCell.setCellValue(rowSecond[i]);
            tempCell.setCellStyle(commonStyle);
            tempCell.setCellStyle(commonStyle);
        }
        //开始写正文

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        for (int i = 0; i < list.size(); i++) {
            AirMailChannelBillsFristDetailDO vo = list.get(i);

            String airRoute = "";
            //取航线最后三位
            if (vo.getAirRoute() != null && !"".equals(vo.getAirRoute()) && vo.getAirRoute().length() >= 3) {
                airRoute = vo.getAirRoute().substring(vo.getAirRoute().length() - 3);
            }
            String flightNumber = "";
            //取航班号前五位
            if (vo.getFlightNumber() != null && !"".equals(vo.getFlightNumber()) && vo.getFlightNumber().length() >= 5) {
                flightNumber = vo.getFlightNumber().substring(0, 5);
            }
            Date dateOfShipment1 = vo.getDateOfShipment(); //发运日期
            String emailType = "";
            String type = vo.getEmailType();//邮件类型
            if ("EMS".equals(type)) {
                emailType = "01";
            } else if ("E邮宝".equals(type) || "E 邮宝".equals(type)) {
                emailType = "02";
            } else if ("航空函件".equals(type)) {
                emailType = "03";
            } else if ("包裹".equals(type)) {
                emailType = "04";
            } else if ("水陆".equals(type)) {
                emailType = "05";
            }
            Map<String, Object> mapTemp = new HashMap<String, Object>();
            mapTemp.put("portId", airRoute);
            mapTemp.put("flightNumber", flightNumber);
            mapTemp.put("dateOfShipment", dateOfShipment1);
            mapTemp.put("emailType", emailType);
            //查询航运费用明细
            List<Map<String, Object>> feeTemp = airMailChannelBillsFristBillDao.feeList(mapTemp);

            BigDecimal shippingPrice = BigDecimal.valueOf(0.00);
            BigDecimal addPrice = BigDecimal.valueOf(0.00);
            if (feeTemp.size() > 0) {
                shippingPrice = (BigDecimal) feeTemp.get(0).get("shippingPrice");//发运费单价
                addPrice = (BigDecimal) feeTemp.get(0).get("addPrice");//附加费单价
            }

            String dateOfShipment = sdf.format(vo.getDateOfShipment());
            Row tempRow = sheet2.createRow(rowNum++);
            tempRow.setRowStyle(commonStyle);
            //列表数据
            tempRow.createCell(0).setCellValue(i + 1);
            tempRow.createCell(1).setCellValue(dateOfShipment);//日期
            tempRow.createCell(2).setCellValue(vo.getFlightNumber());//航班
            tempRow.createCell(3).setCellValue(vo.getSendOrg());//原寄局
            tempRow.createCell(4).setCellValue(vo.getAirRoute());//航线
            tempRow.createCell(5).setCellValue(vo.getEmailType());//邮件类型
            tempRow.createCell(6).setCellFormula(vo.getPackageNo());//总包
            tempRow.createCell(7).setCellFormula(vo.getBagsNo());//袋数
            tempRow.createCell(8).setCellFormula(vo.getWeight().setScale(2, BigDecimal.ROUND_HALF_UP).toString());//重量（KG)
            tempRow.getCell(6).setCellStyle(shuzhi);
            tempRow.getCell(7).setCellStyle(shuzhi);
            tempRow.getCell(8).setCellStyle(shuzhi);
            tempRow.createCell(9).setCellFormula(shippingPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());//结算单价
            tempRow.createCell(10).setCellFormula(addPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());//附加费
            tempRow.getCell(9).setCellStyle(huobiStyle);
            tempRow.getCell(10).setCellStyle(huobiStyle);
            tempRow.createCell(11).setCellFormula("(" + CellReference.convertNumToColString(9) + "" + rowNum + "+" + CellReference.convertNumToColString(10) + "" + rowNum + ")" + "*" + CellReference.convertNumToColString(8) + "" + rowNum);//结算金额
            tempRow.getCell(11).setCellStyle(huobiStyle);
            tempRow.createCell(12).setCellValue("");//备注
        }

        rowNum++;
        Row heji = sheet2.createRow(rowNum);//合计行
        heji.setHeight((short) 400);
        Cell hejiCell = heji.createCell(0);
        hejiCell.setCellStyle(headerStyle);
        hejiCell.setCellValue("合计");
        heji.createCell(7).setCellFormula("SUM(" + CellReference.convertNumToColString(7) + "3:" + CellReference.convertNumToColString(7) + rowNum + ")");
        heji.createCell(8).setCellFormula("SUM(" + CellReference.convertNumToColString(8) + "3:" + CellReference.convertNumToColString(8) + rowNum + ")");
        heji.createCell(11).setCellFormula("SUM(" + CellReference.convertNumToColString(11) + "3:" + CellReference.convertNumToColString(11) + rowNum + ")");
        heji.getCell(7).setCellStyle(shuzhi);
        heji.getCell(8).setCellStyle(shuzhi);
        heji.getCell(11).setCellStyle(huobiStyle);
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 2));
        rowNum += 2;

        //开始拼接sheet2页底部信息
        //公章
        Row gongzhang = sheet2.createRow(rowNum);//
        gongzhang.createCell(1).setCellValue("盖章单位：");
        gongzhang.createCell(6).setCellValue("盖章单位：");
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 1, 2));
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 6, 9));
        rowNum++;

        //盖章日期
        Row riqi = sheet2.createRow(rowNum);//
        riqi.createCell(6).setCellValue("盖章日期：");
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 6, 9));
        rowNum++;

        //经办人
        Row bumen = sheet2.createRow(rowNum);
        bumen.createCell(1).setCellValue("经办人:");
        bumen.createCell(6).setCellValue("核对人:");
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum + 2, 1, 2));
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum + 2, 6, 9));

        //盖章日期
        rowNum += 3;
        Row kuiji = sheet2.createRow(rowNum);
        kuiji.createCell(1).setCellValue("盖章日期:");
        kuiji.createCell(6).setCellValue("制表人:");
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 1, 2));
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum + 2, 6, 9));

    }


    //口岸导出核对明细细分sheet页（广州模板）                                                                                                                        air:航线倒数三位，用来创建sheet时给sheet命名用     weight分sheet也合计重量用
    public void guangZhouAirList(HSSFWorkbook wb, CellStyle headerStyle, CellStyle commonStyle, CellStyle alignRightStyle, Map<String, Object> map, List<AirMailChannelBillsFristDetailDO> list, String air, CellStyle shuzhi) {
        // 创建sheet
        Sheet sheet = wb.createSheet(air);
        // 行号
        int rowNum = 0;
        //设置列宽
        for (int i = 0; i < 9; i++) {
            if (i == 2 || i == 4 || i == 9) {
                sheet.setColumnWidth(i, 6000);
            } else {
                sheet.setColumnWidth(i, 4000);
            }
        }

        //第一行中国快递服务有限公司核账单
        Row r0 = sheet.createRow(rowNum++);
        r0.setHeight((short) 600);
        Cell c0 = r0.createCell(0);
        c0.setCellValue(map.get("dateStart") + "───" + map.get("dateEnd") + "中国快递服务有限公司核账单明细");
        c0.setCellStyle(headerStyle);
        //合并单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 12));

        //第二行  表头
        Row r1 = sheet.createRow(rowNum++);
        r1.setHeight((short) 500);
        String[] rowSecond = {"序号", "日期", "航班号", "原寄局", "航线", "邮件类型", "总包", "袋号", "重量", "条码"};
        for (int i = 0; i < rowSecond.length; i++) {
            Cell tempCell = r1.createCell(i);
            tempCell.setCellValue(rowSecond[i]);
            tempCell.setCellStyle(commonStyle);
            tempCell.setCellStyle(commonStyle);
        }
        sheet.addMergedRegion(new CellRangeAddress(rowNum - 1, rowNum - 1, 9, 12));
        //开始写正文

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        for (int i = 0; i < list.size(); i++) {
            AirMailChannelBillsFristDetailDO vo = list.get(i);
            //比较明细数据的航线字段的最后三位是否与air相等，若相等则加入列表，若不相等则跳过
            String airRoute = vo.getAirRoute();
            airRoute = airRoute.substring(airRoute.length() - 3);
            if (air.equals(airRoute)) {
                String dateOfShipment = sdf.format(vo.getDateOfShipment());
                Row tempRow = sheet.createRow(rowNum++);
                tempRow.setRowStyle(commonStyle);
                //列表数据
                tempRow.createCell(0).setCellValue(rowNum - 2);
                tempRow.createCell(1).setCellValue(dateOfShipment);//日期
                tempRow.createCell(2).setCellValue(vo.getFlightNumber());//航班
                tempRow.createCell(3).setCellValue(vo.getSendOrg());//原寄局
                tempRow.createCell(4).setCellValue(vo.getAirRoute());//航线
                tempRow.createCell(5).setCellValue(vo.getEmailType());//邮件类型
                tempRow.createCell(6).setCellFormula(vo.getPackageNo());//总包
                tempRow.createCell(7).setCellFormula(vo.getBagsNo());//袋数
                tempRow.createCell(8).setCellFormula(vo.getWeight().setScale(2, BigDecimal.ROUND_HALF_UP).toString());//重量（KG)
                tempRow.getCell(6).setCellStyle(shuzhi);
                tempRow.getCell(7).setCellStyle(shuzhi);
                tempRow.getCell(8).setCellStyle(shuzhi);
                tempRow.createCell(9).setCellValue(vo.getBarcode());//条形码
                sheet.addMergedRegion(new CellRangeAddress(rowNum - 1, rowNum - 1, 9, 12));
            }
        }

        Row heji = sheet.createRow(rowNum);//合计行
        heji.setHeight((short) 400);
        Cell hejiCell = heji.createCell(0);
        hejiCell.setCellStyle(headerStyle);
        hejiCell.setCellValue("合计");
        heji.createCell(8).setCellFormula("SUM(" + CellReference.convertNumToColString(8) + "3:" + CellReference.convertNumToColString(8) + rowNum + ")");//合计重量
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 2));


    }


    //口岸导出核账单sheet页（东莞模板）
    public void dongGuanBill(HSSFWorkbook wb, CellStyle headerStyle, CellStyle commonStyle, CellStyle alignRightStyle, CellStyle huobiStyle, Map<String, Object> map, List<AirMailChannelBillsFristBillDO> list, CellStyle shuzhi) {
        // 创建sheet
        Sheet sheet = wb.createSheet("核账单");

        // 行号
        int rowNum = 0;
        //设置列宽
        for (int i = 0; i < 12; i++) {
            if (i == 2 || i == 4 || i == 10 || i == 11) {
                sheet.setColumnWidth(i, 6000);
            } else {
                sheet.setColumnWidth(i, 4000);
            }
        }

		/*//第一行
		Row r0 = sheet.createRow(rowNum++);
		r0.setHeight((short) 800);
		Cell c00 = r0.createCell(0);
		c00.setCellValue("表二：");
		c00.setCellStyle(alignLeftNoBorderStyle);
		//合并单元格
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 8));*/
        //第一行中国快递服务有限公司核账单
        Row r0 = sheet.createRow(rowNum++);
        r0.setHeight((short) 600);
        Cell c0 = r0.createCell(0);
        c0.setCellValue("中国快递服务有限公司核账单");
        c0.setCellStyle(headerStyle);
        //合并单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 11));
        //第二行 结算周期
        Row r1 = sheet.createRow(rowNum++);
        r1.setHeight((short) 500);
        Cell c1 = r1.createCell(0);
        c1.setCellValue("结算周期：" + map.get("dateStart") + "───" + map.get("dateEnd"));
        c1.setCellStyle(alignRightStyle);
        //合并单元格
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 11));
        //第三行  表头
        Row r2 = sheet.createRow(rowNum++);
        r2.setHeight((short) 500);
        String[] rowSecond = {};
        if ("0".equals(map.get("settlementType"))) {//黄包
            rowSecond = new String[]{"序号", "出发地", "航线", "邮件类型", "航班", "发运费用", "", "", "附加费用", "", "", "合计金额"};
        } else {//蓝包
            rowSecond = new String[]{"序号", "出发地", "航线", "航班", "邮件类型", "发运费用", "", "", "附加费用", "", "", "合计金额"};
        }
        for (int i = 0; i < rowSecond.length; i++) {
            Cell tempCell = r2.createCell(i);
            tempCell.setCellValue(rowSecond[i]);
            tempCell.setCellStyle(commonStyle);
            tempCell.setCellStyle(commonStyle);
        }
        //第4行  合并行
        Row r3 = sheet.createRow(rowNum++);
        String[] rowSecond4 = {"结算重量（KG)", "结算单价", "金额", "结算重量（KG)", "结算单价", "金额"};
        for (int i = 0; i < rowSecond4.length; i++) {
            Cell tempCell = r3.createCell(i + 5);
            tempCell.setCellValue(rowSecond4[i]);
            tempCell.setCellStyle(commonStyle);
        }
        //合并单元格   //"序号", "出发地", "航线","邮件类型","航班" ,"发运费用","","","附加费用","","","合计金额","备注"
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 0, 0));
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 1, 1));
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 2, 2));
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 3, 3));
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 4, 4));
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 5, 7));//发运费用
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 8, 10));//附加费用
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 11, 11));
        //开始写正文

        BigDecimal weight = new BigDecimal(0);
        BigDecimal totalWeight = new BigDecimal(0);//累计金额

        String rowBeginString = CellReference.convertNumToColString(7); //将第8列转成ABC列，插入求和公式用
        String rowEndString = CellReference.convertNumToColString(10); //将第11列转成ABC列，插入求和公式用
        for (int i = 0; i < list.size(); i++) {
            AirMailChannelBillsFristBillDO vo = list.get(i);
            weight = vo.getWeight();
//            String airRoute = vo.getAirRoute() == null ? "": vo.getAirRoute().substring(vo.getAirRoute().length() - 3);//取航线最后三位
//            String flightNumber = vo.getFlightNumber() == null ? "":vo.getFlightNumber().substring(0, 5);//取航班号前五位
            String airRoute = "";
            //取航线最后三位
            if (vo.getAirRoute() != null && !"".equals(vo.getAirRoute()) && vo.getAirRoute().length() >= 3) {
                airRoute = vo.getAirRoute().substring(vo.getAirRoute().length() - 3);
            }
            String flightNumber = "";
            //取航班号前五位
            if (vo.getFlightNumber() != null && !"".equals(vo.getFlightNumber()) && vo.getFlightNumber().length() >= 5) {
                flightNumber = vo.getFlightNumber().substring(0, 5);
            }
            Date dateOfShipment = vo.getDateOfShipment(); //发运日期
            String emailType = "";
            String type = vo.getEmailType();//邮件类型
            if ("EMS".equals(type)) {
                emailType = "01";
            } else if ("E邮宝".equals(type) || "E 邮宝".equals(type)) {
                emailType = "02";
            } else if ("航空函件".equals(type)) {
                emailType = "03";
            } else if ("包裹".equals(type)) {
                emailType = "04";
            } else if ("水陆".equals(type)) {
                emailType = "05";
            }
            Map<String, Object> mapTemp = new HashMap<String, Object>();
            mapTemp.put("portId", airRoute);
            mapTemp.put("flightNumber", flightNumber);
            mapTemp.put("dateOfShipment", dateOfShipment);
            mapTemp.put("emailType", emailType);
            //查询航运费用明细
            List<Map<String, Object>> feeTemp = airMailChannelBillsFristBillDao.feeList(mapTemp);
            //存放费用明细，核账单查询后放入feeList，生成明细sheet的时候可以直接用这个list，不需要再次查询数据库
//            feeList.addAll(feeTemp);
            BigDecimal shippingPrice = BigDecimal.valueOf(0.00);
            BigDecimal addPrice = BigDecimal.valueOf(0.00);
            if (feeTemp.size() > 0) {
                shippingPrice = (BigDecimal) feeTemp.get(0).get("shippingPrice");//发运费单价
                addPrice = (BigDecimal) feeTemp.get(0).get("addPrice");//附加费单价
            }
            Row tempRow = sheet.createRow(rowNum++);
            tempRow.setRowStyle(commonStyle);
            //列表数据
            tempRow.createCell(0).setCellValue(i + 1);
            tempRow.createCell(1).setCellValue(vo.getStartPlace());//出发地
            tempRow.createCell(2).setCellValue(vo.getAirRoute());//航线
            if ("0".equals(map.get("settlementType"))) {//黄包
                tempRow.createCell(3).setCellValue(vo.getEmailType());//邮件类型
                tempRow.createCell(4).setCellValue(vo.getFlightNumber());//航班
            } else {//蓝包
                tempRow.createCell(3).setCellValue(vo.getFlightNumber());//航班
                tempRow.createCell(4).setCellValue(vo.getEmailType());//邮件类型
            }
            tempRow.createCell(5).setCellFormula(weight.setScale(2, BigDecimal.ROUND_HALF_UP).toString());//发运结算重量（KG)
            tempRow.getCell(5).setCellStyle(shuzhi);
            Cell tempCell = tempRow.createCell(6);
            tempCell.setCellFormula(shippingPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());//发运结算单价
            tempCell.setCellStyle(huobiStyle);

            tempRow.createCell(7).setCellFormula(CellReference.convertNumToColString(5) + "" + rowNum + "*" + CellReference.convertNumToColString(6) + "" + rowNum);//发运金额 拼接公式
            tempRow.getCell(7).setCellStyle(huobiStyle);
            tempRow.createCell(8).setCellFormula(weight.setScale(2, BigDecimal.ROUND_HALF_UP).toString());//费用的结算重量（KG)
            tempRow.createCell(9).setCellFormula(addPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());//费用的结算单价
            tempRow.getCell(8).setCellStyle(shuzhi);
            tempRow.getCell(9).setCellStyle(huobiStyle);
            tempRow.createCell(10).setCellFormula(CellReference.convertNumToColString(8) + "" + rowNum + "*" + CellReference.convertNumToColString(9) + "" + rowNum);//费用金额
            tempRow.getCell(10).setCellStyle(huobiStyle);
            Cell tempHeji = tempRow.createCell(11);//合计金额
            String sumstring = rowBeginString + "" + rowNum + "+" + rowEndString + "" + rowNum;//求和公式
            tempHeji.setCellFormula(sumstring);
            tempHeji.setCellStyle(huobiStyle);
//            tempRow.createCell(11).setCellValue(0);//合计金额
            totalWeight.add(weight);
        }
        Row heji = sheet.createRow(rowNum);//合计行
        heji.createCell(0).setCellValue("合计");
        heji.getCell(0).setCellStyle(headerStyle);
        heji.createCell(5).setCellFormula("SUM(" + CellReference.convertNumToColString(5) + "5:" + CellReference.convertNumToColString(5) + rowNum + ")");
        String colString = CellReference.convertNumToColString(6); //将当前行长度转成ABC列
        String sumstring = "SUM(" + colString + "5:" + colString + rowNum + ")";//求和公式
        heji.createCell(7).setCellFormula("SUM(" + CellReference.convertNumToColString(7) + "5:" + CellReference.convertNumToColString(7) + rowNum + ")");
        heji.getCell(7).setCellStyle(huobiStyle);
        heji.createCell(8).setCellFormula("SUM(" + CellReference.convertNumToColString(8) + "5:" + CellReference.convertNumToColString(8) + rowNum + ")");
        heji.createCell(10).setCellFormula("SUM(" + CellReference.convertNumToColString(10) + "5:" + CellReference.convertNumToColString(10) + rowNum + ")");
        heji.getCell(10).setCellStyle(huobiStyle);
        heji.createCell(11).setCellFormula(rowBeginString + "" + (rowNum + 1) + "+" + rowEndString + "" + (rowNum + 1));
        heji.getCell(11).setCellStyle(huobiStyle);
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 3));
        rowNum += 3;

        //开始拼接sheet页底部信息
        //公章
        Row gongzhang = sheet.createRow(rowNum);//17
        gongzhang.setHeight((short) 500);
        gongzhang.createCell(0).setCellValue("盖章单位：");
        gongzhang.createCell(8).setCellValue("盖章单位：");
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 7));
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 8, 9));
        rowNum += 2;
        //确认人
        Row bumen = sheet.createRow(rowNum);
        bumen.setHeight((short) 400);
        bumen.createCell(0).setCellValue("确认人:");
        bumen.createCell(8).setCellValue("确认人:");
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 7));
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 8, 9));

        //盖章日期
        rowNum += 2;
        Row riqi = sheet.createRow(rowNum);
        riqi.setHeight((short) 400);
        riqi.createCell(0).setCellValue("盖章日期:");
        riqi.createCell(8).setCellValue("盖章日期:");
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 7));
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 8, 9));
    }


    //口岸导出核对明细sheet页（东莞模板）
    public void dongGuanDetail(HSSFWorkbook wb, CellStyle headerStyle, CellStyle commonStyle, CellStyle alignRightStyle, Map<String, Object> map, List<AirMailChannelBillsFristDetailDO> list, CellStyle shuzhi, CellStyle huobiStyle) {
        // 创建sheet
        Sheet sheet2 = wb.createSheet("核对明细");

        // 行号
        int rowNum = 0;
        //设置列宽
        for (int i = 0; i < 13; i++) {
            if (i == 0 || i == 4 || i == 8 || i == 11) {
                sheet2.setColumnWidth(i, 6000);
            } else {
                sheet2.setColumnWidth(i, 4000);
            }
        }

        //第一行中国快递服务有限公司核账单
        Row r0 = sheet2.createRow(rowNum++);
        r0.setHeight((short) 600);
        Cell c0 = r0.createCell(0);
        c0.setCellValue(map.get("dateStart") + "───" + map.get("dateEnd") + "中国快递服务有限公司核账单明细");
        c0.setCellStyle(headerStyle);
        //合并单元格
        sheet2.addMergedRegion(new CellRangeAddress(0, 0, 0, 12));
        if ("1".equals(map.get("settlementType"))) {//蓝包才有这个第二行
            //第二行 结算周期
            Row r1 = sheet2.createRow(rowNum++);
            r1.setHeight((short) 500);
            Cell c1 = r1.createCell(0);
            c1.setCellValue("结算周期：" + map.get("dateStart") + "───" + map.get("dateEnd"));
            c1.setCellStyle(alignRightStyle);
            //合并单元格
            sheet2.addMergedRegion(new CellRangeAddress(1, 1, 0, 12));

            //第三行  表头
            Row r2 = sheet2.createRow(rowNum++);
            r2.setHeight((short) 500);
            String[] rowSecond = {"序号", "航班号", "发运日期", "出发地", "航线", "邮件类型", "总包号", "袋数", "结算重量(KG)", "结算单价", "附加费单价", "附加费金额", "结算金额"};
            for (int i = 0; i < rowSecond.length; i++) {
                Cell tempCell = r2.createCell(i);
                tempCell.setCellValue(rowSecond[i]);
                tempCell.setCellStyle(commonStyle);
                tempCell.setCellStyle(commonStyle);
            }
        } else {//黄包

            //第三行  表头
            Row r2 = sheet2.createRow(rowNum++);
            r2.setHeight((short) 500);
            String[] rowSecond = {"序号", "发运日期", "出发地", "航线", "总包号", "袋数", "航班", "重量(KG)", "邮件类型", "结算单价", "附加费单价", "附加费金额", "结算金额"};
            for (int i = 0; i < rowSecond.length; i++) {
                Cell tempCell = r2.createCell(i);
                tempCell.setCellValue(rowSecond[i]);
                tempCell.setCellStyle(commonStyle);
                tempCell.setCellStyle(commonStyle);
            }
        }
        //开始写正文

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        for (int i = 0; i < list.size(); i++) {
            AirMailChannelBillsFristDetailDO vo = list.get(i);

            String airRoute = "";
            //取航线最后三位
            if (vo.getAirRoute() != null && !"".equals(vo.getAirRoute()) && vo.getAirRoute().length() >= 3) {
                airRoute = vo.getAirRoute().substring(vo.getAirRoute().length() - 3);
            }
            String flightNumber = "";
            //取航班号前五位
            if (vo.getFlightNumber() != null && !"".equals(vo.getFlightNumber()) && vo.getFlightNumber().length() >= 5) {
                flightNumber = vo.getFlightNumber().substring(0, 5);
            }
            Date dateOfShipment1 = vo.getDateOfShipment(); //发运日期
            String emailType = "";
            String type = vo.getEmailType();//邮件类型
            if ("EMS".equals(type)) {
                emailType = "01";
            } else if ("E邮宝".equals(type) || "E 邮宝".equals(type)) {
                emailType = "02";
            } else if ("航空函件".equals(type)) {
                emailType = "03";
            } else if ("包裹".equals(type)) {
                emailType = "04";
            } else if ("水陆".equals(type)) {
                emailType = "05";
            }
            Map<String, Object> mapTemp = new HashMap<String, Object>();
            mapTemp.put("portId", airRoute);
            mapTemp.put("flightNumber", flightNumber);
            mapTemp.put("dateOfShipment", dateOfShipment1);
            mapTemp.put("emailType", emailType);
            //查询航运费用明细
            List<Map<String, Object>> feeTemp = airMailChannelBillsFristBillDao.feeList(mapTemp);

            BigDecimal shippingPrice = BigDecimal.valueOf(0.00);
            BigDecimal addPrice = BigDecimal.valueOf(0.00);
            if (feeTemp.size() > 0) {
                shippingPrice = (BigDecimal) feeTemp.get(0).get("shippingPrice");//发运费单价
                addPrice = (BigDecimal) feeTemp.get(0).get("addPrice");//附加费单价
            }

            String dateOfShipment = sdf.format(vo.getDateOfShipment());
            Row tempRow = sheet2.createRow(rowNum++);
            tempRow.setRowStyle(commonStyle);
            //列表数据
            tempRow.createCell(0).setCellValue(i + 1);
            if ("1".equals(map.get("settlementType"))) {//蓝包
                tempRow.createCell(1).setCellValue(vo.getFlightNumber());//航班
                tempRow.createCell(2).setCellValue(dateOfShipment);//日期
                tempRow.createCell(3).setCellValue(vo.getStartPlace());//出发地
                tempRow.createCell(4).setCellValue(vo.getAirRoute());//航线
                tempRow.createCell(5).setCellValue(vo.getEmailType());//邮件类型
                tempRow.createCell(6).setCellFormula(vo.getPackageNo());//总包
                tempRow.createCell(7).setCellFormula(vo.getBagsNo());//袋数
                tempRow.createCell(8).setCellFormula(vo.getWeight().setScale(2, BigDecimal.ROUND_HALF_UP).toString());//重量（KG)
                tempRow.getCell(6).setCellStyle(shuzhi);
                tempRow.getCell(7).setCellStyle(shuzhi);
                tempRow.getCell(8).setCellStyle(shuzhi);
                tempRow.createCell(9).setCellFormula(shippingPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());//结算单价
                tempRow.createCell(10).setCellFormula(addPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());//附加费
                tempRow.createCell(11).setCellFormula(CellReference.convertNumToColString(8) + "" + rowNum + "*" + CellReference.convertNumToColString(10) + "" + rowNum);//附加费金额
                tempRow.getCell(9).setCellStyle(huobiStyle);
                tempRow.getCell(10).setCellStyle(huobiStyle);
                tempRow.getCell(11).setCellStyle(huobiStyle);
                tempRow.createCell(12).setCellFormula(CellReference.convertNumToColString(8) + "" + rowNum + "*" + CellReference.convertNumToColString(9) + "" + rowNum + "+" + CellReference.convertNumToColString(11) + "" + rowNum);//结算金额
                tempRow.getCell(12).setCellStyle(huobiStyle);
            } else {//黄包
                tempRow.createCell(1).setCellValue(dateOfShipment);//日期
                tempRow.createCell(2).setCellValue(vo.getStartPlace());//出发地
                tempRow.createCell(3).setCellValue(vo.getAirRoute());//航线
                tempRow.createCell(4).setCellFormula(vo.getPackageNo());//总包
                tempRow.createCell(5).setCellFormula(vo.getBagsNo());//袋数
                tempRow.createCell(6).setCellValue(vo.getFlightNumber());//航班
                tempRow.createCell(7).setCellFormula(vo.getWeight().setScale(2, BigDecimal.ROUND_HALF_UP).toString());//重量（KG)
                tempRow.createCell(8).setCellValue(vo.getEmailType());//邮件类型
                tempRow.getCell(4).setCellStyle(shuzhi);
                tempRow.getCell(5).setCellStyle(shuzhi);
                tempRow.getCell(7).setCellStyle(shuzhi);
            tempRow.createCell(9).setCellFormula(shippingPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());//结算单价
            tempRow.createCell(10).setCellFormula(addPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());//附加费
            tempRow.createCell(11).setCellFormula(CellReference.convertNumToColString(7) + "" + rowNum + "*" + CellReference.convertNumToColString(10) + "" + rowNum);//附加费金额
            tempRow.getCell(9).setCellStyle(huobiStyle);
            tempRow.getCell(10).setCellStyle(huobiStyle);
            tempRow.getCell(11).setCellStyle(huobiStyle);
            tempRow.createCell(12).setCellFormula(CellReference.convertNumToColString(7) + "" + rowNum + "*" + CellReference.convertNumToColString(9) + "" + rowNum + "+" + CellReference.convertNumToColString(11) + "" + rowNum);//结算金额
            tempRow.getCell(12).setCellStyle(huobiStyle);
            }
        }

        if ("1".equals(map.get("settlementType"))) {//蓝包
            rowNum++;
            Row heji = sheet2.createRow(rowNum);//合计行
            heji.setHeight((short) 400);
            Cell hejiCell = heji.createCell(0);
            hejiCell.setCellStyle(headerStyle);
            hejiCell.setCellValue("合计");
            heji.createCell(7).setCellFormula("SUM(" + CellReference.convertNumToColString(7) + "4:" + CellReference.convertNumToColString(7) + rowNum + ")");
            heji.createCell(8).setCellFormula("SUM(" + CellReference.convertNumToColString(8) + "4:" + CellReference.convertNumToColString(8) + rowNum + ")");
            heji.createCell(11).setCellFormula("SUM(" + CellReference.convertNumToColString(11) + "4:" + CellReference.convertNumToColString(11) + rowNum + ")");
            heji.createCell(12).setCellFormula("SUM(" + CellReference.convertNumToColString(12) + "4:" + CellReference.convertNumToColString(12) + rowNum + ")");
            heji.getCell(7).setCellStyle(shuzhi);
            heji.getCell(8).setCellStyle(shuzhi);
            heji.getCell(11).setCellStyle(huobiStyle);
            heji.getCell(12).setCellStyle(huobiStyle);
            sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 2));
            rowNum += 2;
        } else {
            rowNum++;
            Row heji = sheet2.createRow(rowNum);//合计行
            heji.setHeight((short) 400);
            Cell hejiCell = heji.createCell(0);
            hejiCell.setCellStyle(headerStyle);
            hejiCell.setCellValue("合计");
            heji.createCell(7).setCellFormula("SUM(" + CellReference.convertNumToColString(7) + "3:" + CellReference.convertNumToColString(7) + rowNum + ")");
            heji.createCell(11).setCellFormula("SUM(" + CellReference.convertNumToColString(11) + "3:" + CellReference.convertNumToColString(11) + rowNum + ")");
            heji.createCell(12).setCellFormula("SUM(" + CellReference.convertNumToColString(12) + "3:" + CellReference.convertNumToColString(12) + rowNum + ")");
            heji.getCell(7).setCellStyle(shuzhi);
            heji.getCell(11).setCellStyle(huobiStyle);
            heji.getCell(12).setCellStyle(huobiStyle);
            sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 2));
            rowNum += 2;
        }

        //开始拼接sheet页底部信息
        //公章
        Row gongzhang = sheet2.createRow(rowNum);//17
        gongzhang.setHeight((short) 500);
        gongzhang.createCell(0).setCellValue("盖章单位：");
        gongzhang.createCell(8).setCellValue("盖章单位：");
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 7));
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 8, 9));
        rowNum += 2;
        //确认人
        Row bumen = sheet2.createRow(rowNum);
        bumen.setHeight((short) 400);
        bumen.createCell(0).setCellValue("确认人:");
        bumen.createCell(8).setCellValue("确认人:");
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 7));
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 8, 9));

        //盖章日期
        rowNum += 2;
        Row riqi = sheet2.createRow(rowNum);
        riqi.setHeight((short) 400);
        riqi.createCell(0).setCellValue("盖章日期:");
        riqi.createCell(8).setCellValue("盖章日期:");
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 7));
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 8, 9));

    }

    //口岸导出核对明细细分sheet页（东莞模板）                                                                                                                        air:航线倒数三位，用来创建sheet时给sheet命名用     weight分sheet也合计重量用
    public void dongGuanAirList(HSSFWorkbook wb, CellStyle headerStyle, CellStyle commonStyle, CellStyle alignRightStyle, Map<String, Object> map, List<AirMailChannelBillsFristDetailDO> list, String air, CellStyle shuzhi) {
        // 创建sheet
        Sheet sheet = wb.createSheet(air);
        // 行号
        int rowNum = 0;
        //设置列宽
        for (int i = 0; i < 5; i++) {
            if (i == 2) {
                sheet.setColumnWidth(i, 6000);
            } else {
                sheet.setColumnWidth(i, 4000);
            }
        }


        //第一行  表头
        Row r1 = sheet.createRow(rowNum++);
        r1.setHeight((short) 500);
        String[] rowSecond = {};
        if ("0".equals(map.get("settlementType"))) {//黄包
            rowSecond = new String[]{"发运日期", "封发日期", "航线", "总包号", "重量(KG)"};
        } else {
            rowSecond = new String[]{"日期", "封发日期", "路由", "总包号", "袋重(KG)"};
        }
        for (int i = 0; i < rowSecond.length; i++) {
            Cell tempCell = r1.createCell(i);
            tempCell.setCellValue(rowSecond[i]);
            tempCell.setCellStyle(commonStyle);
            tempCell.setCellStyle(commonStyle);
        }
        //开始写正文

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyyMMdd");
        for (int i = 0; i < list.size(); i++) {
            AirMailChannelBillsFristDetailDO vo = list.get(i);
            //比较明细数据的航线字段的最后三位是否与air相等，若相等则加入列表，若不相等则跳过
            String airRoute = vo.getAirRoute();
            airRoute = airRoute.substring(airRoute.length() - 3);
            if (air.equals(airRoute)) {
                String dateOfShipment = sdf.format(vo.getDateOfShipment());
                Row tempRow = sheet.createRow(rowNum++);
                tempRow.setRowStyle(commonStyle);
                //列表数据
                tempRow.createCell(0).setCellValue(dateOfShipment);//日期
                tempRow.createCell(1).setCellValue(sdf2.format(vo.getDateOfShipment()));//封发日期
                tempRow.createCell(2).setCellValue(vo.getAirRoute());//路由
                tempRow.createCell(3).setCellFormula(vo.getPackageNo());//总包
                tempRow.createCell(4).setCellFormula(vo.getWeight().setScale(2, BigDecimal.ROUND_HALF_UP).toString());//重量（KG)
                tempRow.getCell(4).setCellStyle(shuzhi);
            }
        }

        Row heji = sheet.createRow(rowNum);//合计行
        heji.createCell(4).setCellFormula("SUM(" + CellReference.convertNumToColString(4) + "2:" + CellReference.convertNumToColString(4) + rowNum + ")");//合计重量
    }


    //口岸导出核账单sheet页（深圳模板）
    public void shenZhenBill(HSSFWorkbook wb, CellStyle headerStyle, CellStyle commonStyle, CellStyle alignRightStyle, CellStyle huobiStyle, Map<String, Object> map, List<AirMailChannelBillsFristBillDO> list, CellStyle shuzhi) {
        // 创建sheet
        Sheet sheet = wb.createSheet("核账单");

        // 行号
        int rowNum = 0;
        //设置列宽
        for (int i = 0; i < 8; i++) {
            if (i == 2 || i == 3 || i == 5 || i == 7) {
                sheet.setColumnWidth(i, 6000);
            } else {
                sheet.setColumnWidth(i, 4000);
            }
        }
        //第一行中国快递服务有限公司核账单
        Row r0 = sheet.createRow(rowNum++);
        r0.setHeight((short) 600);
        Cell c0 = r0.createCell(0);
        c0.setCellValue("中国快递服务有限公司核账单");
        c0.setCellStyle(headerStyle);
        //合并单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 7));
        //第二行 结算周期
        Row r1 = sheet.createRow(rowNum++);
        r1.setHeight((short) 500);
        Cell c1 = r1.createCell(0);
        c1.setCellValue("结算周期：" + map.get("dateStart") + "───" + map.get("dateEnd"));
        c1.setCellStyle(alignRightStyle);
        //合并单元格
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 7));
        //第三行  表头
        Row r2 = sheet.createRow(rowNum++);
        r2.setHeight((short) 500);
        String[] rowSecond = {"序号", "出发地", "航线", "航班", "邮件类型", "结算重量（KG)", "结算单价", "金额"};

        for (int i = 0; i < rowSecond.length; i++) {
            Cell tempCell = r2.createCell(i);
            tempCell.setCellValue(rowSecond[i]);
            tempCell.setCellStyle(commonStyle);
            tempCell.setCellStyle(commonStyle);
        }
        //开始写正文

        BigDecimal weight = new BigDecimal(0);
        BigDecimal totalWeight = new BigDecimal(0);//累计金额

        String rowBeginString = CellReference.convertNumToColString(7); //将第8列转成ABC列，插入求和公式用
        String rowEndString = CellReference.convertNumToColString(10); //将第11列转成ABC列，插入求和公式用
        for (int i = 0; i < list.size(); i++) {
            AirMailChannelBillsFristBillDO vo = list.get(i);
            weight = vo.getWeight();
//            String airRoute = vo.getAirRoute() == null ? "": vo.getAirRoute().substring(vo.getAirRoute().length() - 3);//取航线最后三位
//            String flightNumber = vo.getFlightNumber() == null ? "":vo.getFlightNumber().substring(0, 5);//取航班号前五位
            String airRoute = "";
            //取航线最后三位
            if (vo.getAirRoute() != null && !"".equals(vo.getAirRoute()) && vo.getAirRoute().length() >= 3) {
                airRoute = vo.getAirRoute().substring(vo.getAirRoute().length() - 3);
            }
            String flightNumber = "";
            //取航班号前五位
            if (vo.getFlightNumber() != null && !"".equals(vo.getFlightNumber()) && vo.getFlightNumber().length() >= 5) {
                flightNumber = vo.getFlightNumber().substring(0, 5);
            }
            Date dateOfShipment = vo.getDateOfShipment(); //发运日期
            String emailType = "";
            String type = vo.getEmailType();//邮件类型
            if ("EMS".equals(type)) {
                emailType = "01";
            } else if ("E邮宝".equals(type) || "E 邮宝".equals(type)) {
                emailType = "02";
            } else if ("航空函件".equals(type)) {
                emailType = "03";
            } else if ("包裹".equals(type)) {
                emailType = "04";
            } else if ("水陆".equals(type)) {
                emailType = "05";
            }
            Map<String, Object> mapTemp = new HashMap<String, Object>();
            mapTemp.put("portId", airRoute);
            mapTemp.put("flightNumber", flightNumber);
            mapTemp.put("dateOfShipment", dateOfShipment);
            mapTemp.put("emailType", emailType);
            //查询航运费用明细
            List<Map<String, Object>> feeTemp = airMailChannelBillsFristBillDao.feeList(mapTemp);
            //存放费用明细，核账单查询后放入feeList，生成明细sheet的时候可以直接用这个list，不需要再次查询数据库
//            feeList.addAll(feeTemp);
            BigDecimal shippingPrice = BigDecimal.valueOf(0.00);
            BigDecimal addPrice = BigDecimal.valueOf(0.00);
            if (feeTemp.size() > 0) {
                shippingPrice = (BigDecimal) feeTemp.get(0).get("shippingPrice");//发运费单价
                addPrice = (BigDecimal) feeTemp.get(0).get("addPrice");//附加费单价
            }
            Row tempRow = sheet.createRow(rowNum++);
            tempRow.setRowStyle(commonStyle);
            //列表数据
            tempRow.createCell(0).setCellValue(i + 1);
            tempRow.createCell(1).setCellValue(vo.getStartPlace());//出发地
            tempRow.createCell(2).setCellValue(vo.getAirRoute());//航线
            tempRow.createCell(3).setCellValue(vo.getFlightNumber());//航班
            tempRow.createCell(4).setCellValue(vo.getEmailType());//邮件类型
            tempRow.createCell(5).setCellFormula(weight.setScale(2, BigDecimal.ROUND_HALF_UP).toString());//结算重量（KG)
            tempRow.getCell(5).setCellStyle(shuzhi);
            Cell tempCell = tempRow.createCell(6);
            tempCell.setCellFormula(shippingPrice.add(addPrice).setScale(2, BigDecimal.ROUND_HALF_UP).toString());//结算单价 = 发运结算单价+费用结算单价
            tempCell.setCellStyle(huobiStyle);
            tempRow.createCell(7).setCellFormula(CellReference.convertNumToColString(5) + "" + rowNum + "*" + CellReference.convertNumToColString(6) + "" + rowNum);//金额 拼接公式
            tempRow.getCell(7).setCellStyle(huobiStyle);
        }
        Row heji = sheet.createRow(rowNum);//合计行
        if ("0".equals(map.get("settlementType"))) {//黄包
            heji.createCell(0).setCellValue("合计");
        } else {//蓝包
            heji.createCell(0).setCellValue("小计");
        }
        heji.getCell(0).setCellStyle(headerStyle);
        heji.createCell(5).setCellFormula("SUM(" + CellReference.convertNumToColString(5) + "4:" + CellReference.convertNumToColString(5) + rowNum + ")");
//        String colString = CellReference.convertNumToColString(6); //将当前行长度转成ABC列
        heji.createCell(7).setCellFormula("SUM(" + CellReference.convertNumToColString(7) + "4:" + CellReference.convertNumToColString(7) + rowNum + ")");
        heji.getCell(7).setCellStyle(huobiStyle);
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 4));
        rowNum += 3;

        //开始拼接sheet页底部信息
        //公章
        Row gongzhang = sheet.createRow(rowNum);//17
        gongzhang.setHeight((short) 500);
        gongzhang.createCell(0).setCellValue("盖章单位：");
        gongzhang.createCell(8).setCellValue("盖章单位：");
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 5));
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 6, 7));
        rowNum += 2;
        //确认人
        Row bumen = sheet.createRow(rowNum);
        bumen.setHeight((short) 400);
        if ("0".equals(map.get("settlementType"))) {//黄包
            bumen.createCell(0).setCellValue("核对人:");
            bumen.createCell(8).setCellValue("核对人:");
        } else {//蓝包
            bumen.createCell(0).setCellValue("确认人:");
            bumen.createCell(8).setCellValue("确认人:");
        }
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 5));
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 6, 7));

        //盖章日期
        rowNum += 2;
        Row riqi = sheet.createRow(rowNum);
        riqi.setHeight((short) 400);
        riqi.createCell(0).setCellValue("盖章日期:");
        riqi.createCell(8).setCellValue("盖章日期:");
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 5));
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 6, 7));
    }


    //口岸导出核对明细sheet页（深圳模板）
    public void shenZhenDetail(HSSFWorkbook wb, CellStyle headerStyle, CellStyle commonStyle, CellStyle alignRightStyle, Map<String, Object> map, List<AirMailChannelBillsFristDetailDO> list, CellStyle shuzhi, CellStyle huobiStyle) {
        // 创建sheet
        Sheet sheet2 = wb.createSheet("核对明细");

        // 行号
        int rowNum = 0;
        //设置列宽
        for (int i = 0; i < 11; i++) {
            if (i == 0 || i == 4 || i == 9) {
                sheet2.setColumnWidth(i, 6000);
            } else {
                sheet2.setColumnWidth(i, 4000);
            }
        }

        //第一行中国快递服务有限公司核账单
        Row r0 = sheet2.createRow(rowNum++);
        r0.setHeight((short) 600);
        Cell c0 = r0.createCell(0);
        c0.setCellValue(map.get("dateStart") + "───" + map.get("dateEnd") + "中国快递服务有限公司核账单明细");
        c0.setCellStyle(headerStyle);
        //合并单元格
        sheet2.addMergedRegion(new CellRangeAddress(0, 0, 0, 10));
        String[] rowSecond = {};
        if ("1".equals(map.get("settlementType"))) {//蓝包才有这个第二行
            //第二行 结算周期
            Row r1 = sheet2.createRow(rowNum++);
            r1.setHeight((short) 500);
            Cell c1 = r1.createCell(0);
            c1.setCellValue("结算周期：" + map.get("dateStart") + "───" + map.get("dateEnd"));
            c1.setCellStyle(alignRightStyle);
            //合并单元格
            sheet2.addMergedRegion(new CellRangeAddress(1, 1, 0, 10));
            //第三行  表头
            Row r2 = sheet2.createRow(rowNum++);
            r2.setHeight((short) 500);
            rowSecond = new String[]{"序号", "航班号", "发运日期", "出发地", "航线", "邮件类型", "总包号", "袋数", "结算重量(KG)", "结算单价", "结算金额"};
            for (int i = 0; i < rowSecond.length; i++) {
                Cell tempCell = r2.createCell(i);
                tempCell.setCellValue(rowSecond[i]);
                tempCell.setCellStyle(commonStyle);
                tempCell.setCellStyle(commonStyle);
            }
        } else {//黄包

            //第三行  表头
            Row r2 = sheet2.createRow(rowNum++);
            r2.setHeight((short) 500);
            rowSecond = new String[]{"序号", "航班", "发运日期", "出发地", "航线", "邮件类型", "总包号", "袋数", "重量(KG)", "结算单价", "结算金额"};
            for (int i = 0; i < rowSecond.length; i++) {
                Cell tempCell = r2.createCell(i);
                tempCell.setCellValue(rowSecond[i]);
                tempCell.setCellStyle(commonStyle);
                tempCell.setCellStyle(commonStyle);
            }
        }
        //开始写正文

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        for (int i = 0; i < list.size(); i++) {
            AirMailChannelBillsFristDetailDO vo = list.get(i);

            String airRoute = "";
            //取航线最后三位
            if (vo.getAirRoute() != null && !"".equals(vo.getAirRoute()) && vo.getAirRoute().length() >= 3) {
                airRoute = vo.getAirRoute().substring(vo.getAirRoute().length() - 3);
            }
            String flightNumber = "";
            //取航班号前五位
            if (vo.getFlightNumber() != null && !"".equals(vo.getFlightNumber()) && vo.getFlightNumber().length() >= 5) {
                flightNumber = vo.getFlightNumber().substring(0, 5);
            }
            Date dateOfShipment1 = vo.getDateOfShipment(); //发运日期
            String emailType = "";
            String type = vo.getEmailType();//邮件类型
            if ("EMS".equals(type)) {
                emailType = "01";
            } else if ("E邮宝".equals(type) || "E 邮宝".equals(type)) {
                emailType = "02";
            } else if ("航空函件".equals(type)) {
                emailType = "03";
            } else if ("包裹".equals(type)) {
                emailType = "04";
            } else if ("水陆".equals(type)) {
                emailType = "05";
            }
            Map<String, Object> mapTemp = new HashMap<String, Object>();
            mapTemp.put("portId", airRoute);
            mapTemp.put("flightNumber", flightNumber);
            mapTemp.put("dateOfShipment", dateOfShipment1);
            mapTemp.put("emailType", emailType);
            //查询航运费用明细
            List<Map<String, Object>> feeTemp = airMailChannelBillsFristBillDao.feeList(mapTemp);

            BigDecimal shippingPrice = BigDecimal.valueOf(0.00);
            BigDecimal addPrice = BigDecimal.valueOf(0.00);
            if (feeTemp.size() > 0) {
                shippingPrice = (BigDecimal) feeTemp.get(0).get("shippingPrice");//发运费单价
                addPrice = (BigDecimal) feeTemp.get(0).get("addPrice");//附加费单价
            }

            String dateOfShipment = sdf.format(vo.getDateOfShipment());
            Row tempRow = sheet2.createRow(rowNum++);
            tempRow.setRowStyle(commonStyle);
            //列表数据
            tempRow.createCell(0).setCellValue(i + 1);
            tempRow.createCell(1).setCellValue(vo.getFlightNumber());//航班
            tempRow.createCell(2).setCellValue(dateOfShipment);//日期
            tempRow.createCell(3).setCellValue(vo.getStartPlace());//出发地
            tempRow.createCell(4).setCellValue(vo.getAirRoute());//航线
            tempRow.createCell(5).setCellValue(vo.getEmailType());//邮件类型
            tempRow.createCell(6).setCellFormula(vo.getPackageNo());//总包
            tempRow.createCell(7).setCellFormula(vo.getBagsNo());//袋数
            tempRow.createCell(8).setCellFormula(vo.getWeight().setScale(2, BigDecimal.ROUND_HALF_UP).toString());//重量（KG)
            tempRow.getCell(6).setCellStyle(shuzhi);
            tempRow.getCell(7).setCellStyle(shuzhi);
            tempRow.getCell(8).setCellStyle(shuzhi);
            tempRow.createCell(9).setCellFormula(shippingPrice.add(addPrice).setScale(2, BigDecimal.ROUND_HALF_UP).toString());//结算单价 = 发运结算单价+费用结算单价
            tempRow.getCell(9).setCellStyle(huobiStyle);
            tempRow.createCell(10).setCellFormula(CellReference.convertNumToColString(8) + "" + rowNum + "*" + CellReference.convertNumToColString(9) + "" + rowNum);//结算金额
            tempRow.getCell(10).setCellStyle(huobiStyle);
        }

        rowNum++;
        Row heji = sheet2.createRow(rowNum);//合计行
        heji.setHeight((short) 400);
        Cell hejiCell = heji.createCell(0);
        hejiCell.setCellStyle(headerStyle);
        hejiCell.setCellValue("合计");
        if ("0".equals(map.get("settlementType"))) {//黄包
            heji.createCell(7).setCellFormula("SUM(" + CellReference.convertNumToColString(7) + "3:" + CellReference.convertNumToColString(7) + rowNum + ")");
            heji.createCell(8).setCellFormula("SUM(" + CellReference.convertNumToColString(8) + "3:" + CellReference.convertNumToColString(8) + rowNum + ")");
            heji.createCell(10).setCellFormula("SUM(" + CellReference.convertNumToColString(10) + "3:" + CellReference.convertNumToColString(10) + rowNum + ")");
        } else {//蓝包
            heji.createCell(7).setCellFormula("SUM(" + CellReference.convertNumToColString(7) + "4:" + CellReference.convertNumToColString(7) + rowNum + ")");
            heji.createCell(8).setCellFormula("SUM(" + CellReference.convertNumToColString(8) + "4:" + CellReference.convertNumToColString(8) + rowNum + ")");
            heji.createCell(10).setCellFormula("SUM(" + CellReference.convertNumToColString(10) + "4:" + CellReference.convertNumToColString(10) + rowNum + ")");
        }
        heji.getCell(7).setCellStyle(shuzhi);
        heji.getCell(8).setCellStyle(shuzhi);
        heji.getCell(10).setCellStyle(huobiStyle);
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 6));
        rowNum += 3;

        //开始拼接sheet页底部信息
        //公章
        Row gongzhang = sheet2.createRow(rowNum);//17
        gongzhang.setHeight((short) 500);
        gongzhang.createCell(0).setCellValue("盖章单位：");
        gongzhang.createCell(8).setCellValue("盖章单位：");
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 7));
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 8, 9));
        rowNum += 2;
        //确认人
        Row bumen = sheet2.createRow(rowNum);
        bumen.setHeight((short) 400);
        if ("0".equals(map.get("settlementType"))) {//黄包
            bumen.createCell(0).setCellValue("核对人:");
            bumen.createCell(8).setCellValue("核对人:");
        } else {//蓝包
            bumen.createCell(0).setCellValue("确认人:");
            bumen.createCell(8).setCellValue("确认人:");
        }
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 7));
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 8, 9));

        //盖章日期
        rowNum += 2;
        Row riqi = sheet2.createRow(rowNum);
        riqi.setHeight((short) 400);
        riqi.createCell(0).setCellValue("盖章日期:");
        riqi.createCell(8).setCellValue("盖章日期:");
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 7));
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 8, 9));

    }


    //口岸导出核账单sheet页（珠海模板）
    public void zhuHaiBill(HSSFWorkbook wb, CellStyle headerStyle, CellStyle commonStyle, CellStyle alignRightStyle, CellStyle huobiStyle, Map<String, Object> map, List<AirMailChannelBillsFristBillDO> list, CellStyle shuzhi) {
        // 创建sheet
        Sheet sheet = wb.createSheet("核账单");

        // 行号
        int rowNum = 0;
        //设置列宽
        for (int i = 0; i < 12; i++) {
            if (i == 2 || i == 4 || i == 10 || i == 11) {
                sheet.setColumnWidth(i, 6000);
            } else {
                sheet.setColumnWidth(i, 4000);
            }
        }

		/*//第一行
		Row r0 = sheet.createRow(rowNum++);
		r0.setHeight((short) 800);
		Cell c00 = r0.createCell(0);
		c00.setCellValue("表二：");
		c00.setCellStyle(alignLeftNoBorderStyle);
		//合并单元格
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 8));*/
        //第一行中国快递服务有限公司核账单
        Row r0 = sheet.createRow(rowNum++);
        r0.setHeight((short) 600);
        Cell c0 = r0.createCell(0);
        c0.setCellValue("中国快递服务有限公司核账单");
        c0.setCellStyle(headerStyle);
        //合并单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 11));
        //第二行 结算周期
        Row r1 = sheet.createRow(rowNum++);
        r1.setHeight((short) 500);
        Cell c1 = r1.createCell(0);
        c1.setCellValue("结算周期：" + map.get("dateStart") + "───" + map.get("dateEnd"));
        c1.setCellStyle(alignRightStyle);
        //合并单元格
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 11));
        //第三行  表头
        Row r2 = sheet.createRow(rowNum++);
        r2.setHeight((short) 500);
        String[] rowSecond = {"序号", "出发地", "航线", "邮件类型", "航班", "发运费用", "", "", "附加费用", "", "", "合计金额"};

        for (int i = 0; i < rowSecond.length; i++) {
            Cell tempCell = r2.createCell(i);
            tempCell.setCellValue(rowSecond[i]);
            tempCell.setCellStyle(commonStyle);
            tempCell.setCellStyle(commonStyle);
        }
        //第4行  合并行
        Row r3 = sheet.createRow(rowNum++);
        String[] rowSecond4 = {"结算重量（KG)", "结算单价", "金额", "结算重量（KG)", "结算单价", "金额"};
        for (int i = 0; i < rowSecond4.length; i++) {
            Cell tempCell = r3.createCell(i + 5);
            tempCell.setCellValue(rowSecond4[i]);
            tempCell.setCellStyle(commonStyle);
        }
        //合并单元格   //"序号", "出发地", "航线","邮件类型","航班" ,"发运费用","","","附加费用","","","合计金额","备注"
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 0, 0));
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 1, 1));
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 2, 2));
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 3, 3));
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 4, 4));
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 5, 7));//发运费用
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 8, 10));//附加费用
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 11, 11));
        //开始写正文

        BigDecimal weight = new BigDecimal(0);
        BigDecimal totalWeight = new BigDecimal(0);//累计金额

        String rowBeginString = CellReference.convertNumToColString(7); //将第8列转成ABC列，插入求和公式用
        String rowEndString = CellReference.convertNumToColString(10); //将第11列转成ABC列，插入求和公式用
        for (int i = 0; i < list.size(); i++) {
            AirMailChannelBillsFristBillDO vo = list.get(i);
            weight = vo.getWeight();
//            String airRoute = vo.getAirRoute() == null ? "": vo.getAirRoute().substring(vo.getAirRoute().length() - 3);//取航线最后三位
//            String flightNumber = vo.getFlightNumber() == null ? "":vo.getFlightNumber().substring(0, 5);//取航班号前五位
            String airRoute = "";
            //取航线最后三位
            if (vo.getAirRoute() != null && !"".equals(vo.getAirRoute()) && vo.getAirRoute().length() >= 3) {
                airRoute = vo.getAirRoute().substring(vo.getAirRoute().length() - 3);
            }
            String flightNumber = "";
            //取航班号前五位
            if (vo.getFlightNumber() != null && !"".equals(vo.getFlightNumber()) && vo.getFlightNumber().length() >= 5) {
                flightNumber = vo.getFlightNumber().substring(0, 5);
            }
            Date dateOfShipment = vo.getDateOfShipment(); //发运日期
            String emailType = "";
            String type = vo.getEmailType();//邮件类型
            if ("EMS".equals(type)) {
                emailType = "01";
            } else if ("E邮宝".equals(type) || "E 邮宝".equals(type)) {
                emailType = "02";
            } else if ("航空函件".equals(type)) {
                emailType = "03";
            } else if ("包裹".equals(type)) {
                emailType = "04";
            } else if ("水陆".equals(type)) {
                emailType = "05";
            }
            Map<String, Object> mapTemp = new HashMap<String, Object>();
            mapTemp.put("portId", airRoute);
            mapTemp.put("flightNumber", flightNumber);
            mapTemp.put("dateOfShipment", dateOfShipment);
            mapTemp.put("emailType", emailType);
            //查询航运费用明细
            List<Map<String, Object>> feeTemp = airMailChannelBillsFristBillDao.feeList(mapTemp);
            //存放费用明细，核账单查询后放入feeList，生成明细sheet的时候可以直接用这个list，不需要再次查询数据库
//            feeList.addAll(feeTemp);
            BigDecimal shippingPrice = BigDecimal.valueOf(0.00);
            BigDecimal addPrice = BigDecimal.valueOf(0.00);
            if (feeTemp.size() > 0) {
                shippingPrice = (BigDecimal) feeTemp.get(0).get("shippingPrice");//发运费单价
                addPrice = (BigDecimal) feeTemp.get(0).get("addPrice");//附加费单价
            }
            Row tempRow = sheet.createRow(rowNum++);
            tempRow.setRowStyle(commonStyle);
            //列表数据
            tempRow.createCell(0).setCellValue(i + 1);
            tempRow.createCell(1).setCellValue(vo.getStartPlace());//出发地
            tempRow.createCell(2).setCellValue(vo.getAirRoute());//航线
            tempRow.createCell(3).setCellValue(vo.getEmailType());//邮件类型
            tempRow.createCell(4).setCellValue(vo.getFlightNumber());//航班
            tempRow.createCell(5).setCellFormula(weight.setScale(2, BigDecimal.ROUND_HALF_UP).toString());//发运结算重量（KG)
            tempRow.getCell(5).setCellStyle(shuzhi);
            Cell tempCell = tempRow.createCell(6);
            tempCell.setCellFormula(shippingPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());//发运结算单价
            tempCell.setCellStyle(huobiStyle);

            tempRow.createCell(7).setCellFormula(CellReference.convertNumToColString(5) + "" + rowNum + "*" + CellReference.convertNumToColString(6) + "" + rowNum);//发运金额 拼接公式
            tempRow.getCell(7).setCellStyle(huobiStyle);
            tempRow.createCell(8).setCellFormula(weight.setScale(2, BigDecimal.ROUND_HALF_UP).toString());//费用的结算重量（KG)
            tempRow.createCell(9).setCellFormula(addPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());//费用的结算单价
            tempRow.getCell(8).setCellStyle(shuzhi);
            tempRow.getCell(9).setCellStyle(huobiStyle);
            tempRow.createCell(10).setCellFormula(CellReference.convertNumToColString(8) + "" + rowNum + "*" + CellReference.convertNumToColString(9) + "" + rowNum);//费用金额
            tempRow.getCell(10).setCellStyle(huobiStyle);
            Cell tempHeji = tempRow.createCell(11);//合计金额
            String sumstring = rowBeginString + "" + rowNum + "+" + rowEndString + "" + rowNum;//求和公式
            tempHeji.setCellFormula(sumstring);
            tempHeji.setCellStyle(huobiStyle);
//            tempRow.createCell(11).setCellValue(0);//合计金额
            totalWeight.add(weight);
        }
        Row heji = sheet.createRow(rowNum);//合计行
        heji.createCell(0).setCellValue("合计");
        heji.getCell(0).setCellStyle(headerStyle);
        heji.createCell(5).setCellFormula("SUM(" + CellReference.convertNumToColString(5) + "5:" + CellReference.convertNumToColString(5) + rowNum + ")");
        String colString = CellReference.convertNumToColString(6); //将当前行长度转成ABC列
        String sumstring = "SUM(" + colString + "4:" + colString + rowNum + ")";//求和公式
        heji.createCell(7).setCellFormula("SUM(" + CellReference.convertNumToColString(7) + "5:" + CellReference.convertNumToColString(7) + rowNum + ")");
        heji.getCell(7).setCellStyle(huobiStyle);
        heji.createCell(8).setCellFormula("SUM(" + CellReference.convertNumToColString(8) + "5:" + CellReference.convertNumToColString(8) + rowNum + ")");
        heji.createCell(10).setCellFormula("SUM(" + CellReference.convertNumToColString(10) + "5:" + CellReference.convertNumToColString(10) + rowNum + ")");
        heji.getCell(10).setCellStyle(huobiStyle);
        heji.createCell(11).setCellFormula(rowBeginString + "" + (rowNum + 1) + "+" + rowEndString + "" + (rowNum + 1));
        heji.getCell(11).setCellStyle(huobiStyle);
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 3));
        rowNum += 3;

        //开始拼接sheet页底部信息
        //公章
        Row gongzhang = sheet.createRow(rowNum);//17
        gongzhang.setHeight((short) 500);
        gongzhang.createCell(0).setCellValue("盖章单位：");
        gongzhang.createCell(8).setCellValue("盖章单位：");
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 7));
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 8, 9));
        rowNum += 3;
        //确认人
        Row bumen = sheet.createRow(rowNum);
        bumen.setHeight((short) 400);
        bumen.createCell(0).setCellValue("核对人:");
        bumen.createCell(8).setCellValue("核对人:");
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 7));
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 8, 9));

        //盖章日期
        rowNum += 3;
        Row riqi = sheet.createRow(rowNum);
        riqi.setHeight((short) 400);
        riqi.createCell(0).setCellValue("盖章日期:");
        riqi.createCell(8).setCellValue("盖章日期:");
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 7));
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 8, 9));
    }


    //口岸导出核对明细sheet页（珠海模板）
    public void zhuHaiDetail(HSSFWorkbook wb, CellStyle headerStyle, CellStyle commonStyle, CellStyle alignRightStyle, Map<String, Object> map, List<AirMailChannelBillsFristDetailDO> list, CellStyle shuzhi, CellStyle huobiStyle) {
        // 创建sheet
        Sheet sheet2 = wb.createSheet("核对明细");

        // 行号
        int rowNum = 0;
        //设置列宽
        for (int i = 0; i < 13; i++) {
            if (i == 0 || i == 4 || i == 8 || i == 11) {
                sheet2.setColumnWidth(i, 6000);
            } else {
                sheet2.setColumnWidth(i, 4000);
            }
        }

        //第一行中国快递服务有限公司核账单
        Row r0 = sheet2.createRow(rowNum++);
        r0.setHeight((short) 600);
        Cell c0 = r0.createCell(0);
        c0.setCellValue(map.get("dateStart") + "───" + map.get("dateEnd") + "中国快递服务有限公司核账单明细");
        c0.setCellStyle(headerStyle);
        //合并单元格
        sheet2.addMergedRegion(new CellRangeAddress(0, 0, 0, 12));


        if ("1".equals(map.get("settlementType"))) {//蓝包才有这个第二行
            //第二行 结算周期
            Row r1 = sheet2.createRow(rowNum++);
            r1.setHeight((short) 500);
            Cell c1 = r1.createCell(0);
            c1.setCellValue("结算周期：" + map.get("dateStart") + "───" + map.get("dateEnd"));
            c1.setCellStyle(alignRightStyle);
            //合并单元格
            sheet2.addMergedRegion(new CellRangeAddress(1, 1, 0, 12));


            //第三行  表头
            Row r2 = sheet2.createRow(rowNum++);
            r2.setHeight((short) 500);
            String[] rowSecond = {"序号", "航班号", "发运日期", "出发地", "航线", "邮件类型", "总包号", "袋数", "结算重量(KG)", "结算单价", "附加费单价", "附加费金额", "结算金额"};
            for (int i = 0; i < rowSecond.length; i++) {
                Cell tempCell = r2.createCell(i);
                tempCell.setCellValue(rowSecond[i]);
                tempCell.setCellStyle(commonStyle);
                tempCell.setCellStyle(commonStyle);
            }
        }else {//黄包
            //第er行  表头
            Row r2 = sheet2.createRow(rowNum++);
            r2.setHeight((short) 500);
            String[] rowSecond = {"序号", "发运日期", "出发地", "航线", "总包号", "袋数", "航班", "重量(KG)", "邮件类型", "结算单价", "附加费单价", "附加费金额", "结算金额"};
            for (int i = 0; i < rowSecond.length; i++) {
                Cell tempCell = r2.createCell(i);
                tempCell.setCellValue(rowSecond[i]);
                tempCell.setCellStyle(commonStyle);
                tempCell.setCellStyle(commonStyle);
            }
        }
        //开始写正文

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        for (int i = 0; i < list.size(); i++) {
            AirMailChannelBillsFristDetailDO vo = list.get(i);

            String airRoute = "";
            //取航线最后三位
            if (vo.getAirRoute() != null && !"".equals(vo.getAirRoute()) && vo.getAirRoute().length() >= 3) {
                airRoute = vo.getAirRoute().substring(vo.getAirRoute().length() - 3);
            }
            String flightNumber = "";
            //取航班号前五位
            if (vo.getFlightNumber() != null && !"".equals(vo.getFlightNumber()) && vo.getFlightNumber().length() >= 5) {
                flightNumber = vo.getFlightNumber().substring(0, 5);
            }
            Date dateOfShipment1 = vo.getDateOfShipment(); //发运日期
            String emailType = "";
            String type = vo.getEmailType();//邮件类型
            if ("EMS".equals(type)) {
                emailType = "01";
            } else if ("E邮宝".equals(type) || "E 邮宝".equals(type)) {
                emailType = "02";
            } else if ("航空函件".equals(type)) {
                emailType = "03";
            } else if ("包裹".equals(type)) {
                emailType = "04";
            } else if ("水陆".equals(type)) {
                emailType = "05";
            }
            Map<String, Object> mapTemp = new HashMap<String, Object>();
            mapTemp.put("portId", airRoute);
            mapTemp.put("flightNumber", flightNumber);
            mapTemp.put("dateOfShipment", dateOfShipment1);
            mapTemp.put("emailType", emailType);
            //查询航运费用明细
            List<Map<String, Object>> feeTemp = airMailChannelBillsFristBillDao.feeList(mapTemp);

            BigDecimal shippingPrice = BigDecimal.valueOf(0.00);
            BigDecimal addPrice = BigDecimal.valueOf(0.00);
            if (feeTemp.size() > 0) {
                shippingPrice = (BigDecimal) feeTemp.get(0).get("shippingPrice");//发运费单价
                addPrice = (BigDecimal) feeTemp.get(0).get("addPrice");//附加费单价
            }

            String dateOfShipment = sdf.format(vo.getDateOfShipment());
            Row tempRow = sheet2.createRow(rowNum++);
            tempRow.setRowStyle(commonStyle);
            //列表数据
            tempRow.createCell(0).setCellValue(i + 1);
            if ("0".equals(map.get("settlementType"))) {//黄包
                tempRow.createCell(1).setCellValue(dateOfShipment);//日期
                tempRow.createCell(2).setCellValue(vo.getStartPlace());//出发地
                tempRow.createCell(3).setCellValue(vo.getAirRoute());//航线
                tempRow.createCell(4).setCellFormula(vo.getPackageNo());//总包
                tempRow.createCell(5).setCellFormula(vo.getBagsNo());//袋数
                tempRow.createCell(6).setCellValue(vo.getFlightNumber());//航班
                tempRow.createCell(7).setCellFormula(vo.getWeight().setScale(2, BigDecimal.ROUND_HALF_UP).toString());//重量（KG)
                tempRow.createCell(8).setCellValue(vo.getEmailType());//邮件类型
                tempRow.getCell(4).setCellStyle(shuzhi);
                tempRow.getCell(5).setCellStyle(shuzhi);
                tempRow.getCell(7).setCellStyle(shuzhi);
                tempRow.createCell(9).setCellFormula(shippingPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());//结算单价
                tempRow.createCell(10).setCellFormula(addPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());//附加费
                tempRow.createCell(11).setCellFormula(CellReference.convertNumToColString(7) + "" + rowNum + "*" + CellReference.convertNumToColString(10) + "" + rowNum);//附加费金额
                tempRow.getCell(9).setCellStyle(huobiStyle);
                tempRow.getCell(10).setCellStyle(huobiStyle);
                tempRow.getCell(11).setCellStyle(huobiStyle);
                tempRow.createCell(12).setCellFormula(CellReference.convertNumToColString(7) + "" + rowNum + "*" + CellReference.convertNumToColString(9) + "" + rowNum + "+" + CellReference.convertNumToColString(11) + "" + rowNum);//结算金额
                tempRow.getCell(12).setCellStyle(huobiStyle);
            }else{//蓝包
                tempRow.createCell(1).setCellValue(vo.getFlightNumber());//航班
                tempRow.createCell(2).setCellValue(dateOfShipment);//日期
                tempRow.createCell(3).setCellValue(vo.getStartPlace());//出发地
                tempRow.createCell(4).setCellValue(vo.getAirRoute());//航线
                tempRow.createCell(5).setCellValue(vo.getEmailType());//邮件类型
                tempRow.createCell(6).setCellFormula(vo.getPackageNo());//总包
                tempRow.createCell(7).setCellFormula(vo.getBagsNo());//袋数
                tempRow.createCell(8).setCellFormula(vo.getWeight().setScale(2, BigDecimal.ROUND_HALF_UP).toString());//重量（KG)
                tempRow.getCell(6).setCellStyle(shuzhi);
                tempRow.getCell(7).setCellStyle(shuzhi);
                tempRow.getCell(8).setCellStyle(shuzhi);
                tempRow.createCell(9).setCellFormula(shippingPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());//结算单价
                tempRow.createCell(10).setCellFormula(addPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());//附加费
                tempRow.createCell(11).setCellFormula(CellReference.convertNumToColString(8) + "" + rowNum + "*" + CellReference.convertNumToColString(10) + "" + rowNum);//附加费金额
                tempRow.getCell(9).setCellStyle(huobiStyle);
                tempRow.getCell(10).setCellStyle(huobiStyle);
                tempRow.getCell(11).setCellStyle(huobiStyle);
                tempRow.createCell(12).setCellFormula(CellReference.convertNumToColString(8) + "" + rowNum + "*" + CellReference.convertNumToColString(9) + "" + rowNum + "+" + CellReference.convertNumToColString(11) + "" + rowNum);//结算金额
                tempRow.getCell(12).setCellStyle(huobiStyle);
            }

        }

        rowNum++;
        Row heji = sheet2.createRow(rowNum);//合计行
        heji.setHeight((short) 400);
        Cell hejiCell = heji.createCell(0);
        hejiCell.setCellStyle(headerStyle);
        hejiCell.setCellValue("合计");
        if ("1".equals(map.get("settlementType"))) {//蓝包
            heji.createCell(7).setCellFormula("SUM(" + CellReference.convertNumToColString(7) + "4:" + CellReference.convertNumToColString(7) + rowNum + ")");
            heji.createCell(8).setCellFormula("SUM(" + CellReference.convertNumToColString(8) + "4:" + CellReference.convertNumToColString(8) + rowNum + ")");
            heji.createCell(11).setCellFormula("SUM(" + CellReference.convertNumToColString(11) + "4:" + CellReference.convertNumToColString(11) + rowNum + ")");
            heji.createCell(12).setCellFormula("SUM(" + CellReference.convertNumToColString(12) + "4:" + CellReference.convertNumToColString(12) + rowNum + ")");
            heji.getCell(7).setCellStyle(shuzhi);
            heji.getCell(8).setCellStyle(shuzhi);
            heji.getCell(11).setCellStyle(huobiStyle);
            heji.getCell(12).setCellStyle(huobiStyle);
        }else{//黄包
            heji.createCell(7).setCellFormula("SUM(" + CellReference.convertNumToColString(7) + "4:" + CellReference.convertNumToColString(7) + rowNum + ")");
            heji.createCell(11).setCellFormula("SUM(" + CellReference.convertNumToColString(11) + "4:" + CellReference.convertNumToColString(11) + rowNum + ")");
            heji.createCell(12).setCellFormula("SUM(" + CellReference.convertNumToColString(12) + "4:" + CellReference.convertNumToColString(12) + rowNum + ")");
            heji.getCell(7).setCellStyle(shuzhi);
            heji.getCell(11).setCellStyle(huobiStyle);
            heji.getCell(12).setCellStyle(huobiStyle);
        }
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 6));
        rowNum += 3;

        //开始拼接sheet页底部信息
        //公章
        Row gongzhang = sheet2.createRow(rowNum);//17
        gongzhang.setHeight((short) 700);
        gongzhang.createCell(0).setCellValue("盖章单位：");
        gongzhang.createCell(8).setCellValue("盖章单位：");
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 7));
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 8, 9));
        rowNum += 2;
        //确认人
        Row bumen = sheet2.createRow(rowNum);
        bumen.setHeight((short) 400);
        if ("0".equals(map.get("settlementType"))) {//黄包
            bumen.createCell(0).setCellValue("核对人:");
            bumen.createCell(8).setCellValue("核对人:");
        } else {//蓝包
            bumen.createCell(0).setCellValue("确认人:");
            bumen.createCell(8).setCellValue("确认人:");
        }
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 7));
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 8, 9));

        //盖章日期
        rowNum += 2;
        Row riqi = sheet2.createRow(rowNum);
        riqi.setHeight((short) 400);
        riqi.createCell(0).setCellValue("盖章日期:");
        riqi.createCell(8).setCellValue("盖章日期:");
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 7));
        sheet2.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 8, 9));

    }

}
