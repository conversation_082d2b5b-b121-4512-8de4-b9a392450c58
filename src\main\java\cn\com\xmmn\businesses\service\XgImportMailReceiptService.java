package cn.com.xmmn.businesses.service;

import cn.com.xmmn.businesses.domain.XgImportMailCargoDO;
import cn.com.xmmn.businesses.domain.XgImportMailReceiptDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-06 10:55:50
 */
public interface XgImportMailReceiptService  extends IService<XgImportMailReceiptDO> {
	void save(List<XgImportMailReceiptDO> xgImportMailReceiptDOList);
}
