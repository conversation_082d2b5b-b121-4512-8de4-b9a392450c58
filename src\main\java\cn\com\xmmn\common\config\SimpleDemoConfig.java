package cn.com.xmmn.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix="config")
public class SimpleDemoConfig {
	//上传路径
	private String uploadPath;

	private String username;

	private String password;

	//TMS加解密key
	private String tmsKey;

	public String getUploadPath() {
		return uploadPath;
	}

	public void setUploadPath(String uploadPath) {
		this.uploadPath = uploadPath;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getTmsKey() {	return tmsKey; }

	public void setTmsKey(String tmsKey) { this.tmsKey = tmsKey; }
}
