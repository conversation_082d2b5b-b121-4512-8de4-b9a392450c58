package cn.com.xmmn.businesses.controller;

import cn.com.xmmn.businesses.domain.CarrierDealerRecivDO;
import cn.com.xmmn.businesses.dt.CarrierDealerRecivDetailDT;
import cn.com.xmmn.businesses.service.CarrierDealerD2AService;
import cn.com.xmmn.businesses.service.CarrierDealerTransitTimeService;
import cn.com.xmmn.businesses.vo.*;
import cn.com.xmmn.common.controller.BaseController;
import cn.com.xmmn.common.utils.PageUtils;
import cn.com.xmmn.common.utils.Query;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 承运商全程时限情况
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024年6月14日15:15:15
 */
@Slf4j
@Controller
@RequestMapping("/carrierDealerTransitTime/monitor")
public class CarrierDealerTransitTimeController extends BaseController {

    @Autowired
    private CarrierDealerTransitTimeService carrierDealerTransitTimeService;

    @GetMapping()
    @RequiresPermissions("carrierDealerTransitTime:carrierDealerTransitTime")
    String carrierDealerTransitTime() {
        return "carrierDealerTransitTime/list";
    }



    /**
     * 分组数据
     * @param params
     * @return
     */
    @ResponseBody
    @GetMapping("/group")
    @RequiresPermissions("carrierDealerTransitTime:carrierDealerTransitTime")
    public PageUtils group(@RequestParam Map<String, Object> params) {
        log.info("承运商全程时限情况参数:{}" , params);
        //查询列表数据
        Query query = new Query(params);
        List<CarrierDealerTransitTimeListVO> carrierDealerTransitTimeListVOList = carrierDealerTransitTimeService.group(query);
        List<CarrierDealerTransitTimeListVO> carrierDealerTransitTimeCountListVOList = carrierDealerTransitTimeService.groupCount(query);
        Integer total = carrierDealerTransitTimeCountListVOList.get(0).getTotal();
        PageUtils pageUtils = new PageUtils(carrierDealerTransitTimeListVOList, total);
        pageUtils.setReceiveNum(carrierDealerTransitTimeCountListVOList.get(0).getReceiveNum());
        pageUtils.setReceiverTotalWeight(carrierDealerTransitTimeCountListVOList.get(0).getReceiverTotalWeight());
        pageUtils.setAvgReceiveToPostTime(carrierDealerTransitTimeCountListVOList.get(0).getAvgReceiveToPostTime());
        pageUtils.setOnTimeNum(carrierDealerTransitTimeCountListVOList.get(0).getOnTimeNum());
        pageUtils.setOverTimeNum(carrierDealerTransitTimeCountListVOList.get(0).getOverTimeNum());
        pageUtils.setExceptionBagNum(carrierDealerTransitTimeCountListVOList.get(0).getExceptionBagNum());
        pageUtils.setTimeLimitRate(carrierDealerTransitTimeCountListVOList.get(0).getTimeLimitRate());


        return pageUtils;
    }

    /**
     * 跳转详情页列表
     * @return
     */
    @GetMapping("/detailPage")
    @RequiresPermissions("carrierDealerTransitTime:carrierDealerTransitTime")
    public String detailPage(HttpServletRequest request, Model model) {
        //点击的是哪个明细。
        String queryParameter = request.getParameter("queryParameter");
        model.addAttribute("queryParameter", queryParameter);

        String orgCode = request.getParameter("orgCode");
        String orgCodeStr = request.getParameter("orgCodeStr");
        String carrierCode = request.getParameter("carrierCode");
        String productCode = request.getParameter("productCode");
        String receiverCountryCode = request.getParameter("receiverCountryCode");
        String receiverCountry = request.getParameter("receiverCountry");

        //分组搜索的字段
        String opTime538Start = request.getParameter("opTime538Start");
        String opTime538End = request.getParameter("opTime538End");

        String provinceCodeStr = request.getParameter("provinceCodeStr");
        String oeDest = request.getParameter("oeDest");

        model.addAttribute("provinceCodeStr", provinceCodeStr);
        model.addAttribute("orgCodeStr", orgCodeStr);
        model.addAttribute("opTime538Start", opTime538Start);
        model.addAttribute("opTime538End", opTime538End);

        model.addAttribute("orgCode", orgCode);
        model.addAttribute("carrierCode", carrierCode);
        model.addAttribute("productCode", productCode);
        model.addAttribute("receiverCountryCode", receiverCountryCode);
        model.addAttribute("receiverCountry", receiverCountry);
        model.addAttribute("oeDest", oeDest);
        return "carrierDealerTransitTime/details";
    }

    @ResponseBody
    @GetMapping("/detailTable")
    @RequiresPermissions("carrierDealerTransitTime:carrierDealerTransitTime")
    public PageUtils detailTable(CarrierDealerRecivDetailDT dealerReceiveDetailDT) {
        log.info("承运商全程时限情况详情参数:{}" , dealerReceiveDetailDT);
        List<CarrierDealerRecivDO> carrierDealerRecivList = carrierDealerTransitTimeService.detail(dealerReceiveDetailDT);
        Integer total = carrierDealerTransitTimeService.detailCount(dealerReceiveDetailDT);
        PageUtils pageUtils = new PageUtils(carrierDealerRecivList, total);
        return pageUtils;

    }

    /**
     * 导出数据
     *
     * @param map
     * @param response 入参
     * @return void
     **/
    @RequestMapping(value = "/exportExcel", method = RequestMethod.GET)
    public void exportExcel(@RequestParam Map<String, Object> map, HttpServletResponse response) throws IOException {

        List<CarrierDealerTransitTimeListVO> carrierDealerTransitTimeListVOList = carrierDealerTransitTimeService.group(map);
        List<CarrierDealerTransitTimeListVO> carrierDealerTransitTimeCountListVOList = carrierDealerTransitTimeService.groupCount(map);

        //导出
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");
        // 文件名
        String fileName = map.get("dateStart") + "承运商接收情况统计表" + ".xlsx";
        try {
            fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
        try {
            CarrierDealerTransitTimeListVO newRecord = new CarrierDealerTransitTimeListVO();
            newRecord.setCarrierDealer("合计");

            newRecord.setReceiveNum(carrierDealerTransitTimeCountListVOList.get(0).getReceiveNum());
            newRecord.setReceiverTotalWeight(carrierDealerTransitTimeCountListVOList.get(0).getReceiverTotalWeight());
            newRecord.setAvgReceiveToPostTime(carrierDealerTransitTimeCountListVOList.get(0).getAvgReceiveToPostTime());
            newRecord.setOnTimeNum(carrierDealerTransitTimeCountListVOList.get(0).getOnTimeNum());
            newRecord.setOverTimeNum(carrierDealerTransitTimeCountListVOList.get(0).getOverTimeNum());
            newRecord.setExceptionBagNum(carrierDealerTransitTimeCountListVOList.get(0).getExceptionBagNum());
            newRecord.setTimeLimitRate(carrierDealerTransitTimeCountListVOList.get(0).getTimeLimitRate());

            carrierDealerTransitTimeListVOList.add(newRecord);

            //导出
            EasyExcel.write(response.getOutputStream(), CarrierDealerTransitTimeListVO.class).sheet("承运商接收情况统计表").doWrite(carrierDealerTransitTimeListVOList);
        } catch (Exception e) {
            log.info("excel文档导出错误-异常信息：" + e);
            e.printStackTrace();
        }

    }

    @RequestMapping(value = "/exportDetailExcel",method = RequestMethod.GET)
    public void exportDetailExcel(CarrierDealerRecivDetailDT dealerReceiveDetailDT, HttpServletResponse response) throws IOException {
        List<CarrierDealerRecivDO> portbatchList = carrierDealerTransitTimeService.detail(dealerReceiveDetailDT);


        //导出
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");
        // 文件名
        String fileName =   "全程时限明细" + ".xlsx";
        try {
            fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");


        try {
            List<CarrierDealerTransitTimeDetailListVo> exportList = new ArrayList<>();
            for (CarrierDealerRecivDO portbatch : portbatchList) {
                CarrierDealerTransitTimeDetailListVo vo = new CarrierDealerTransitTimeDetailListVo();
                BeanUtils.copyProperties(portbatch, vo);
                exportList.add(vo);
            }
            //导出
            EasyExcel.write(response.getOutputStream(), CarrierDealerTransitTimeDetailListVo.class)
                    //.registerWriteHandler(customHeaderHandler)
                    .sheet("全程时限明细")
                    .doWrite(exportList);
        } catch (Exception e) {
            log.info("excel文档导出错误-异常信息：" + e);
            e.printStackTrace();
        }
    }
}
