package cn.com.xmmn.businesses.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 投递订单表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-30 20:15:07
 */
@Data
public class OrderDO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 运单号
	 */
	private String shipperHawbcode;
	/**
	 * 客户代码
	 */
	private String customerCode;
	/**
	 * 客户简称
	 */
	private String customerShortname;
	/**
	 * 客户全称
	 */
	private String customerAllname;
	/**
	 * 销售产品名称
	 */
	private String productCnname;
	/**
	 * 目的国家(中文)
	 */
	private String countryCnname;
	/**
	 * 服务商中文名称
	 */
	private String serverAllname;
	/**
	 * 服务渠道名称
	 */
	private String serverChannelCnname;
	/**
	 * 到货时间
	 */
	private Date arrivalDate;
	/**
	 * 签入时间
	 */
	private Date checkinDate;
	/**
	 * 签出时间
	 */
	private Date checkoutDate;
	/**
	 * 申报品名(中文汇总)
	 */
	private String allInvoiceCnname;
	/**
	 * 件数
	 */
	private Integer pieces;
	/**
	 * 收货实重
	 */
	private BigDecimal checkinGrossweight;
	/**
	 * 收货材积重
	 */
	private BigDecimal checkinVolumeweight;
	/**
	 * 客户重量
	 */
	private BigDecimal shipperWeight;
	/**
	 * 收货计费重
	 */
	private BigDecimal shipperChargeweight;
	/**
	 * 服务商重量
	 */
	private BigDecimal serverChargeweight;
	/**
	 * 运费金额(RMB)
	 */
	private BigDecimal rmbFee;
	/**
	 * 其他费用(RMB)
	 */
	private BigDecimal rmbOtherFee;
	/**
	 * 总应收金额(RMB)
	 */
	private BigDecimal rmbTotalReceivableFee;
}
