package cn.com.xmmn.businesses.dao;

import cn.com.xmmn.businesses.domain.OrderDeliveryDO;
import cn.com.xmmn.report.domain.AirMailBusinessBillsDO;
import cn.com.xmmn.report.domain.DellMailBillsDO;
import cn.com.xmmn.report.domain.LandMailBillsDo;
import cn.com.xmmn.report.domain.ValueAddServiceDO;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import java.util.Map;

/**
 * 投递订单表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-30 20:15:07
 */
@Mapper
public interface tableReportDao {

//	<!--以下是查询某一年1月到12月的数据量，没有数据也显示月份，数据量置为0的MySQL语句,t_air_mail_channel_bills_frist_detail-->
	List<Map<String,Object>> airList(Map<String,Object> map);

	//查询陆运邮件
	Map<String,Object> landList(Map<String,Object> map);

	//查询增值服务数据
	Map<String,Object> addList(Map<String,Object> map);

	//查询戴尔物流数据
	Map<String,Object> dellList(Map<String,Object> map);

	//查询商业空运数据
	Map<String,Object> airBusinessList(Map<String,Object> map);

	//陆运邮件表保存
	int landSave(LandMailBillsDo landMailBills);

	//戴尔表保存
	int dellSave(DellMailBillsDO dellMailBills);

	//商业邮件空运表保存
	int airBusinessSave(AirMailBusinessBillsDO airMailBusinessBills);

	//增值服务表保存
	int addSave(ValueAddServiceDO valueAddService);

//	<!--以下是查询某一年1月到12月的数据量，没有数据也显示月份，数据量置为0的MySQL语句,t_orders_import-->
	List<Map<String,Object>> importList(Map<String,Object> map);

//<!--以下是查询某一年1月到12月的数据量，没有数据也显示月份，数据量置为0的MySQL语句,t_order_delivery-->
	List<Map<String,Object>> deliveryList(Map<String,Object> map);

//	<!--以下是查询某一年1月到12月的数据量，没有数据也显示月份，数据量置为0的MySQL语句,t_orders_tms-->
	List<Map<String,Object>> tmsList(Map<String,Object> map);
}
