package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.AirMailChannelBillsFristBillDao;
import cn.com.xmmn.businesses.domain.AirMailChannelBillsFristBillDO;
import cn.com.xmmn.businesses.service.AirMailChannelBillsFristBillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Service
public class AirMailChannelBillsFristBillServiceImpl implements AirMailChannelBillsFristBillService {
    @Autowired
    private AirMailChannelBillsFristBillDao airMailChannelBillsFristBillDao;

    @Override
    public AirMailChannelBillsFristBillDO get(Integer id) {
        return airMailChannelBillsFristBillDao.get(id);
    }

    @Override
    public List<AirMailChannelBillsFristBillDO> list(Map<String, Object> map) {
        return airMailChannelBillsFristBillDao.list(map);
    }

    @Override
    public int count(Map<String, Object> map) {
        return airMailChannelBillsFristBillDao.count(map);
    }

    @Override
    public int save(AirMailChannelBillsFristBillDO airMailChannelBillsFristBill) {
        return airMailChannelBillsFristBillDao.save(airMailChannelBillsFristBill);
    }

    @Override
    public int update(AirMailChannelBillsFristBillDO airMailChannelBillsFristBill) {
        return airMailChannelBillsFristBillDao.update(airMailChannelBillsFristBill);
    }

    @Override
    public int remove(Integer id) {
        return airMailChannelBillsFristBillDao.remove(id);
    }

    @Override
    public int batchRemove(Integer[] ids) {
        return airMailChannelBillsFristBillDao.batchRemove(ids);
    }

    @Override
    public List detail(AirMailChannelBillsFristBillDO airMailChannelBillsFristBill) {
        return airMailChannelBillsFristBillDao.detail(airMailChannelBillsFristBill);
    }

    @Override
    //查询费用数据
    public List<Map<String, Object>> feeList(Map<String, Object> map) {
        return airMailChannelBillsFristBillDao.feeList(map);
    }

    /*  @Override
      //查询hongkong
      public List<String> hongkongList(Map<String,Object> map){
          return airMailChannelBillsFristBillDao.hongkongList(map);
      }*/
    @Override
    //查询hongkong
    public List<Map<String, Object>> portList(Map<String, Object> map) {
        return airMailChannelBillsFristBillDao.portList(map);
    }

    @Override
    //查询口岸发运量占比-饼图
    public List<Map<String, Object>> portPercent(Map<String, Object> map) {
        return airMailChannelBillsFristBillDao.portPercent(map);
    }


    @Override
    //查询按路向统计接收邮件总重
    public List<Map<String, Object>> routeList(Map<String, Object> map) {
        return airMailChannelBillsFristBillDao.routeList(map);
    }

    @Override
    //查询按路向统计接收邮件总重右侧的环比
    public List<Map<String, Object>> huanbiList(Map<String, Object> map) {
        return airMailChannelBillsFristBillDao.huanbiList(map);
    }
}
