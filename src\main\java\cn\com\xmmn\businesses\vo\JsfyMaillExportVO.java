package cn.com.xmmn.businesses.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 导出邮件接收-发运信息
 */
@Data
@HeadRowHeight(value = 30) //头部行高
@ContentRowHeight(value = 25) //内容行高
@ColumnWidth(value = 20) //列宽
@HeadFontStyle(fontName = "宋体",fontHeightInPoints = 11)
public class JsfyMaillExportVO implements Serializable {

    @ExcelProperty("客户名称")
    private String custName;
    @ExcelProperty("邮件号码")
    private String itemId;

    @ExcelProperty("原寄局/地区")
    private String originOrgName;
    @ExcelProperty("业务种类")
    private String product;
    @ExcelProperty("交换站接收时间")
    private Date recvOrgTime;

    @ExcelProperty("交换站发运时间")
    private Date recvOrgSendTime;
    @ExcelProperty("互换局名称")
    private String chOrgName;
    @ExcelProperty("互换局机构号")
    private String chOrgCode;
    @ExcelProperty("互换局接收时间")
    private Date chOrgRecvTime;

    @ExcelProperty("互换局开拆的时间")
    private Date chOrgOpenTime;
    @ExcelProperty("交海关待验时间")
    private Date waitTestTime;
    @ExcelProperty("海关待验开拆时间")
    private Date waitOpenTime;
    @ExcelProperty("封报关行时间")
    private Date disBrokerTime;
    @ExcelProperty("报关行出库时间")
    private Date brokerOutTime;
    @ExcelProperty("互换局封发时间")
    private Date chOrgDisTime;
    @ExcelProperty("互换局发运时间")
    private Date chOrgSendTime;
    @ExcelProperty("处理中心接收时间")
    private Date processRecvTime;
    @ExcelProperty("投递时间")
    private Date deliverTime;

    @ExcelProperty("（接收-发运）时长(h)")
    private Double recvTimeReduceSendTime;
    @ExcelProperty("（发运 -投递）时长（h)")
    private Double sendTimeReduceDeliverTime;
}
