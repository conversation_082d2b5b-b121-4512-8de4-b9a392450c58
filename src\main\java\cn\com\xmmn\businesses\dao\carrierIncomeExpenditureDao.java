package cn.com.xmmn.businesses.dao;

import cn.com.xmmn.businesses.domain.carrierIncomeExpenditureDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 承运商账单明细
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-20 13:12:29
 */
@Mapper
public interface carrierIncomeExpenditureDao {

	carrierIncomeExpenditureDO get(Integer id);
	
	List<carrierIncomeExpenditureDO> list(Map<String,Object> map);
	
	int count(Map<String,Object> map);
	
	int save(carrierIncomeExpenditureDO carrierBillDetail);
	
	int update(carrierIncomeExpenditureDO carrierBillDetail);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);
}
