package cn.com.xmmn.businesses.controller;

import cn.com.xmmn.businesses.domain.CarrierDealerRecivDO;
import cn.com.xmmn.businesses.dt.CarrierDealerRecivDetailDT;
import cn.com.xmmn.businesses.service.CarrierDealerDepartService;
import cn.com.xmmn.businesses.vo.CarrierDealerDepartDetailListVo;
import cn.com.xmmn.businesses.vo.CarrierDealerDepartListVo;
import cn.com.xmmn.businesses.vo.CarrierDealerRecivDetailListVo;
import cn.com.xmmn.businesses.vo.CarrierDealerRecivListVo;
import cn.com.xmmn.common.annotation.Log;
import cn.com.xmmn.common.controller.BaseController;
import cn.com.xmmn.common.utils.PageUtils;
import cn.com.xmmn.common.utils.Query;
import cn.com.xmmn.common.utils.StringUtils;
import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 承运商邮袋启运环节统计
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-04 09:18:55
 */
@Controller
@RequestMapping("/carrierDealerDepart/monitor")
@Slf4j
public class CarrierDealerDepartController extends BaseController {

    @Autowired
    private CarrierDealerDepartService carrierDealerDepartService;

    @GetMapping()
    @RequiresPermissions("carrierDealerDepart:carrierDealerDepart")
    String carrierDealerDepart() {
        return "carrierDealerDepart/list";
    }



    /**
     * 分组数据
     * @param params
     * @return
     */
    @ResponseBody
    @GetMapping("/group")
    @RequiresPermissions("carrierDealerDepart:carrierDealerDepart")
    public PageUtils group(@RequestParam Map<String, Object> params) {
        log.info("承运商邮袋启运环节统计参数:{}" + params);
        //查询列表数据
        Query query = new Query(params);
        List<CarrierDealerDepartListVo> carrierDealerDepartListVos = carrierDealerDepartService.group(query);
        List<CarrierDealerDepartListVo> carrierDealerDepartCountListVos = carrierDealerDepartService.groupCount(query);
        Integer total = carrierDealerDepartCountListVos.get(0).getTotal();
        PageUtils pageUtils = new PageUtils(carrierDealerDepartListVos, total);
        pageUtils.setReceiveNum(carrierDealerDepartCountListVos.get(0).getReceiveNum());
        pageUtils.setDepartNum(carrierDealerDepartCountListVos.get(0).getDepartNum());
        pageUtils.setNormalDepartNum(carrierDealerDepartCountListVos.get(0).getNormalDepartNum());
        pageUtils.setOvertime12to24DepartNum(carrierDealerDepartCountListVos.get(0).getOvertime12to24DepartNum());
        pageUtils.setOvertime24PlusDepartNum(carrierDealerDepartCountListVos.get(0).getOvertime24PlusDepartNum());
        pageUtils.setNotDepartNum(carrierDealerDepartCountListVos.get(0).getNotDepartNum());
        pageUtils.setExceptionBagNum(carrierDealerDepartCountListVos.get(0).getExceptionBagNum());



        return pageUtils;

    }

    /**
     * 跳转详情页列表
     * @return
     */
    @GetMapping("/detailPage")
    @RequiresPermissions("carrierDealerDepart:carrierDealerDepart")
    public String detailPage(HttpServletRequest request, Model model) {
        //点击的是哪个明细。
        String queryParameter = request.getParameter("queryParameter");
        model.addAttribute("queryParameter", queryParameter);

        String orgCode = request.getParameter("orgCode");
        String orgCodeStr = request.getParameter("orgCodeStr");
        String carrierCode = request.getParameter("carrierCode");
        String productCode = request.getParameter("productCode");
        String oeDest = request.getParameter("oeDest");
        String receiverCountryCode = request.getParameter("receiverCountryCode");
        String opTime538Start = request.getParameter("opTime538Start");
        String opTime538End = request.getParameter("opTime538End");

        String provinceCodeStr = request.getParameter("provinceCodeStr");

        model.addAttribute("provinceCodeStr", provinceCodeStr);
        model.addAttribute("opTime538Start", opTime538Start);
        model.addAttribute("opTime538End", opTime538End);
        model.addAttribute("orgCode", orgCode);
        model.addAttribute("orgCodeStr", orgCodeStr);
        model.addAttribute("carrierCode", carrierCode);
        model.addAttribute("productCode", productCode);
        model.addAttribute("oeDest", oeDest);
        model.addAttribute("receiverCountryCode", receiverCountryCode);
        return "carrierDealerDepart/details";
    }

    @ResponseBody
    @GetMapping("/detailTable")
    @RequiresPermissions("carrierDealerDepart:carrierDealerDepart")
    public PageUtils detailTable(CarrierDealerRecivDetailDT dealerReceiveDetailDT) {
        log.info("承运商邮袋启运环节详情参数:" + dealerReceiveDetailDT);
        List<CarrierDealerRecivDO> carrierDealerRecivList = carrierDealerDepartService.detail(dealerReceiveDetailDT);
        Integer total = carrierDealerDepartService.detailCount(dealerReceiveDetailDT);
        PageUtils pageUtils = new PageUtils(carrierDealerRecivList, total);
        return pageUtils;

    }

    /**
     * 导出数据
     *
     * @param map
     * @param response 入参
     * @return void
     **/
    @RequestMapping(value = "/exportExcel", method = RequestMethod.GET)
    public void exportExcel(@RequestParam Map<String, Object> map, HttpServletResponse response) throws IOException {

        List<CarrierDealerDepartListVo> carrierDealerDepartListVos = carrierDealerDepartService.group(map);
        List<CarrierDealerDepartListVo> carrierDealerDepartCountListVos = carrierDealerDepartService.groupCount(map);

        //导出
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");
        // 文件名
        String fileName = map.get("dateStart") + "承运商启运环节统计表" + ".xlsx";
        try {
            fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");

        try {

            CarrierDealerDepartListVo newRecord = new CarrierDealerDepartListVo();

            newRecord.setCarrierDealer("合计");
            newRecord.setReceiveNum(carrierDealerDepartCountListVos.get(0).getReceiveNum());
            newRecord.setDepartNum(carrierDealerDepartCountListVos.get(0).getDepartNum());
            newRecord.setNormalDepartNum(carrierDealerDepartCountListVos.get(0).getNormalDepartNum());
            newRecord.setOvertime12to24DepartNum(carrierDealerDepartCountListVos.get(0).getOvertime12to24DepartNum());
            newRecord.setOvertime24PlusDepartNum(carrierDealerDepartCountListVos.get(0).getOvertime24PlusDepartNum());
            newRecord.setNotDepartNum(carrierDealerDepartCountListVos.get(0).getNotDepartNum());
            newRecord.setExceptionBagNum(carrierDealerDepartCountListVos.get(0).getExceptionBagNum());

            carrierDealerDepartListVos.add(newRecord);

            //导出
            EasyExcel.write(response.getOutputStream(), CarrierDealerDepartListVo.class)
                    .sheet("承运商启运环节统计表").
                    doWrite(carrierDealerDepartListVos);
        } catch (Exception e) {
            log.info("excel文档导出错误-异常信息：" + e);
            e.printStackTrace();
        }

    }



    @RequestMapping(value = "/exportDetailExcel",method = RequestMethod.GET)
    public void exportDetailExcel(CarrierDealerRecivDetailDT dealerReceiveDetailDT, HttpServletResponse response) throws IOException {
        List<CarrierDealerRecivDO> portbatchList = carrierDealerDepartService.detail(dealerReceiveDetailDT);


        //导出
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");
        // 文件名
        String fileName =   "承运商启运环节明细" + ".xlsx";
        try {
            fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");


        try {
            List<CarrierDealerDepartDetailListVo> exportList = new ArrayList<>();
            for (CarrierDealerRecivDO portbatch : portbatchList) {
                CarrierDealerDepartDetailListVo vo = new CarrierDealerDepartDetailListVo();
                BeanUtils.copyProperties(portbatch, vo);
                exportList.add(vo);
            }
            //导出
            EasyExcel.write(response.getOutputStream(), CarrierDealerDepartDetailListVo.class)
                    //.registerWriteHandler(customHeaderHandler)
                    .sheet("承运商启运环节明细")
                    .doWrite(exportList);
        } catch (Exception e) {
            log.info("excel文档导出错误-异常信息：" + e);
            e.printStackTrace();
        }
    }

}
