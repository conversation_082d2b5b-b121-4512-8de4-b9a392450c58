package cn.com.xmmn.common.push.postal.model;

import lombok.Builder;
import lombok.Data;

/**
 *  邮政新一代消息头
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024年10月9日
 */
@Data
@Builder
public class PostalRequest {


    /**
     * 发送方标识
     */
    private String sendID;

    /**
     * 数据生产的省公司代码
     */
    private String proviceNo;

    /**
     * 消息类别 接口编码（JDPT_RCVR_TRACE）
     */
    private String msgKind;

    /**
     * 消息唯一序列号
     */
    private String serialNo;

    /**
     * 消息发送日期时间(YYYYMMDDHHMMSS)
     */
    private String sendDate;

    /**
     *代表接收方标识 JDPT：航空系统（邮政提供）
     */
    private String receiveID;

    /**
     * 批次号000000000001
     */
    private String batchNo;

    /**
     * 数据类型:1-JSON
     * 2-XML
     * 3-压缩后的Byte[]
     */
    private String dataType;

    /**
     * 轨迹报文加密后的签名
     */
    private String dataDigest;

    /**
     * 轨迹报文
     */
    private String msgBody;
}
