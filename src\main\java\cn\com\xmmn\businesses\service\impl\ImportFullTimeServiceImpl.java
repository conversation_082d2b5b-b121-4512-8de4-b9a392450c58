package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.ImportMailDao;
import cn.com.xmmn.businesses.domain.ImportMailDO;
import cn.com.xmmn.businesses.service.ImportFullTimeService;
import cn.com.xmmn.businesses.vo.ImportFullTimeListVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class ImportFullTimeServiceImpl  extends ServiceImpl<ImportMailDao, ImportMailDO> implements ImportFullTimeService {



    private final ImportMailDao importMailDao;

    @Override
    public List<ImportFullTimeListVO> list(Map<String, Object> params) {



        return importMailDao.fullTimeList(params);
    }

    @Override
    public Integer count(Map<String, Object> params) {
        return importMailDao.fullTimeCount(params);
    }
}
