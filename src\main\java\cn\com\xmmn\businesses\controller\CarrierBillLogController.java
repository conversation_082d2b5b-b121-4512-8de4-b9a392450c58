package cn.com.xmmn.businesses.controller;

import cn.com.xmmn.businesses.domain.CarrierBillLogDO;
import cn.com.xmmn.businesses.service.CarrierBillLogService;
import cn.com.xmmn.common.utils.PageUtils;
import cn.com.xmmn.common.utils.Query;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;

/**
 * 承运商账单操作日志
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-27 17:15:37
 */
 
@Controller
@RequestMapping("/check/carrierBillLog")
public class CarrierBillLogController {
	@Autowired
	private CarrierBillLogService carrierBillLogService;
	
	@GetMapping()
	@RequiresPermissions("carrierBillLog:carrierBillLog")
	String carrierBillLog(){
	    return "check/carrierBillLog/list";
	}
	
	@ResponseBody
	@GetMapping("/list")
	@RequiresPermissions("carrierBillLog:carrierBillLog")
	public PageUtils list(@RequestParam Map<String, Object> params){
		//查询列表数据
        Query query = new Query(params);
		Page<CarrierBillLogDO> pageResult=carrierBillLogService.page(query);
		PageUtils pageUtils = new PageUtils(pageResult.getRecords(),(int)pageResult.getTotal());
		return pageUtils;
	}
	

	
}
