package cn.com.xmmn.businesses.dao;

import cn.com.xmmn.businesses.domain.CarrierDealerRecivDO;
import cn.com.xmmn.businesses.dt.CarrierDealerRecivDetailDT;
import cn.com.xmmn.businesses.vo.CarrierDealerRecivListVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 承运商邮袋接收
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-04 09:18:55
 */
@Mapper
public interface CarrierDealerRecivDao extends BaseMapper<CarrierDealerRecivDO> {


    /**
     * 分组获取承运商邮袋
     * @param query 查询参数
     * @return
     */
    List<CarrierDealerRecivListVo> group(Map<String, Object> query);


    /**
     * 分组获取承运商邮袋总数
     * @param query 查询参数
     * @return
     */
    List<CarrierDealerRecivListVo>  groupCount(Map<String, Object> query);

    /**
     * 获取承运商邮袋详情
     * @param dealerRecivDetailDT
     * @return
     */
    List<CarrierDealerRecivDO> detail(CarrierDealerRecivDetailDT dealerRecivDetailDT);

    /**
     * 获取承运商邮袋详情的总数
     * @param dealerRecivDetailDT
     * @return
     */
    Integer detailCount(CarrierDealerRecivDetailDT dealerRecivDetailDT);


}
