package cn.com.xmmn.businesses.domain;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 进口通关效率占比信息
 * <AUTHOR>
 * @Date 2024/5/27 11:51
 */
@Data
public class ImportClearanceEfficiencyProportionDO implements Serializable {



    //总邮件数
    private int totalCount;

    //放行数
    private int releaseNum;

    //放行占比
    private String releasedPercentage;

    //征税数
    private int taxedCount;

    //征税占比
    private String taxedPercentage;

    //查验数
    private int checkedCount;

    //查验占比
    private String checkedPercentage;

    //补充申报数
    private int supplementDeclaredCount;

    //补充申报数
    private String supplementDeclaredPercentage;

    //人工审单数
    private int manualReviewedCount;
    //人工审单占比
    private String manualReviewedPercentage;

    //报关数
    private int customsDeclaredCount;
    //报关占比
    private String customsDeclaredPercentage;
}
