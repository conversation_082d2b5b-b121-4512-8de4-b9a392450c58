package cn.com.xmmn.businesses.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description 承运商邮袋启运列表
 * <AUTHOR>
 * @Date 2024/6/4 16:47
 */
@Data
@HeadRowHeight(value = 30) // 头部行高
@ContentRowHeight(value = 25) // 内容行高
@ColumnWidth(value = 20) // 列宽
@HeadFontStyle(fontName = "宋体", fontHeightInPoints = 11) // 头部字体样式
public class CarrierDealerArriveToPostListVo implements Serializable {
    // 交换站机构名称
    @ExcelIgnore
    private String orgName;

    // 交换站机构code
    @ExcelIgnore
    private String orgCode;



    // 承运商
    @ExcelProperty("承运商")
    private String carrierDealer;

    // 承运商code
    @ExcelIgnore
    private String carrierCode;

    // 业务种类
    @ExcelProperty("业务种类")
    private String product;

    // 业务种类code
    @ExcelIgnore
    private String productCode;

    // 寄达国家/地区
    @ExcelProperty("寄达国家/地区")
    private String receiverCountryName;

    // 寄达国家/地区Code
    @ExcelIgnore
    private String receiverCountryCode;


    // 寄达互换局代码
    //@ExcelProperty("路向")
    @ExcelIgnore
    private String oeDest;

    // 寄达互换局名称
    @ExcelProperty("寄达互换局名称")
    private String oeDestName;



    // 承运商已启运袋数
    @ExcelProperty("承运商已启运袋数")
    private Integer departNum;

    // 承运商运抵袋数
    @ExcelProperty("承运商运抵袋数")
    private Integer arriveNum;

    // 交邮袋数
    @ExcelProperty("交邮袋数")
    private Integer postNum;

    // 正常交邮邮袋（12小时内，含12小时）
    @ExcelProperty("正常交邮邮袋（12小时内，含12小时）")
    private Integer normalPostNum;

    // 超时交邮邮袋（12至24小时）
    @ExcelProperty("超时交邮邮袋（12至24小时）")
    private Integer overPostNum;

    // 承运商未启运袋数
    @ExcelProperty("承运商未启运袋数")
    private Integer notPostNum;

    // 异常袋数
    @ExcelProperty("异常袋数")
    private Integer exceptionBagNum;
    // 寄达国家/地区Code
    @ExcelIgnore
    private String receiverCountry;

    @ExcelIgnore
    private Integer total;

}
