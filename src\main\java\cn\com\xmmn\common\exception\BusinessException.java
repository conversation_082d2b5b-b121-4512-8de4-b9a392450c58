package cn.com.xmmn.common.exception;


/**
 * 自定义异常类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024年7月25日09:44:35
 */
public class BusinessException extends RuntimeException{


    private final int code;

    public BusinessException(int code, String message) {
        super(message);
        this.code = code;
    }
    public BusinessException( String message) {
        super(message);
        this.code = 500;
    }
    public int getCode() {
        return code;
    }
}
