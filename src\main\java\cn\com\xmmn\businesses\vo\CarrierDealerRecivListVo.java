package cn.com.xmmn.businesses.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description 承运商邮袋接收列表
 * <AUTHOR>
 * @Date 2024/6/4 16:47
 */
@Data
@HeadRowHeight(value = 30) //头部行高
@ContentRowHeight(value = 25) //内容行高
@ColumnWidth(value = 20) //列宽
@HeadFontStyle(fontName = "宋体",fontHeightInPoints = 11)
public class CarrierDealerRecivListVo  implements Serializable {


    @ExcelProperty(value = "交换站", index = 0)
    private String orgName;

    @ExcelIgnore
    private String orgCode;

    @ExcelProperty(value = "承运商", index = 1)
    private String carrierDealer;
    @ExcelIgnore
    private String carrierCode;

    @ExcelProperty(value = "业务种类", index = 2)
    private String product;
    @ExcelIgnore
    private String productCode;

    @ExcelProperty(value = "寄达国家/地区", index = 3)
    private String receiverCountryName;
    @ExcelIgnore
    private String receiverCountryCode;

    @ExcelIgnore
    private String oeDest;

    @ExcelProperty(value = "寄达互换局名称", index = 4)
    private String oeDestName;


    @ExcelProperty(value = "已交航袋数", index = 5)
    private Integer devNum;

    @ExcelProperty(value = "交航总重量(kg)", index = 6)
    private BigDecimal totalWeight;

    @ExcelProperty(value = "承运商已接收袋数", index = 7)
    private Integer receiveNum;

    @ExcelProperty(value = "承运商接收总重量（kg）", index = 8)
    private BigDecimal receiverTotalWeight;

    @ExcelProperty(value = "平均接收处理时长（天)", index = 9)
    private BigDecimal avgReceiverTime;

    @ExcelProperty(value = "承运商未接收袋数", index = 10)
    private Integer notReceiverNum;

    @ExcelProperty(value = "承运商未接收总重量（kg）", index = 11)
    private BigDecimal notReceiverTotalWeight;

    @ExcelProperty(value = "异常袋数", index = 12)
    private Integer exceptionBagNum;

    @ExcelIgnore
    private Integer total;
}
