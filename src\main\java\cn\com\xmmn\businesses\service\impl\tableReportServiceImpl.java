package cn.com.xmmn.businesses.service.impl;


import cn.com.xmmn.businesses.dao.tableReportDao;
import cn.com.xmmn.businesses.service.tableReportService;
import cn.com.xmmn.report.domain.AirMailBusinessBillsDO;
import cn.com.xmmn.report.domain.DellMailBillsDO;
import cn.com.xmmn.report.domain.LandMailBillsDo;
import cn.com.xmmn.report.domain.ValueAddServiceDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Service
public class tableReportServiceImpl implements tableReportService {
    @Autowired
    private tableReportDao tableReportDao;


    //	<!--以下是查询某一年1月到12月的数据量，没有数据也显示月份，数据量置为0的MySQL语句,t_air_mail_channel_bills_frist_detail-->
    @Override
    public List<Map<String, Object>> airList(Map<String, Object> map) {
        return tableReportDao.airList(map);
    }

    //查询陆运邮件
    @Override
    public Map<String, Object> landList(Map<String, Object> map) {
        return tableReportDao.landList(map);
    }

    //查询增值服务数据
    @Override
    public Map<String, Object> addList(Map<String, Object> map) {
        return tableReportDao.addList(map);
    }

    //查询戴尔物流数据
    @Override
    public Map<String, Object> dellList(Map<String, Object> map) {
        return tableReportDao.dellList(map);
    }

    //查询商业空运数据
    @Override
    public Map<String, Object> airBusinessList(Map<String, Object> map) {
        return tableReportDao.airBusinessList(map);
    }

    //陆运邮件表保存
    @Override
    public int landSave(LandMailBillsDo landMailBills){
        return tableReportDao.landSave(landMailBills);
    }

    //戴尔表保存
    @Override
    public int dellSave(DellMailBillsDO dellMailBills){
        return tableReportDao.dellSave(dellMailBills);
    };

    //商业邮件空运表保存
    @Override
    public int airBusinessSave(AirMailBusinessBillsDO airMailBusinessBills){
        return tableReportDao.airBusinessSave(airMailBusinessBills);
    };

    //增值服务表保存
    @Override
    public int addSave(ValueAddServiceDO valueAddService){
        return tableReportDao.addSave(valueAddService);
    };


    //	<!--以下是查询某一年1月到12月的数据量，没有数据也显示月份，数据量置为0的MySQL语句,t_orders_import-->
    @Override
    public List<Map<String, Object>> importList(Map<String, Object> map) {
        return tableReportDao.importList(map);
    }

    //	<!--以下是查询某一年1月到12月的数据量，没有数据也显示月份，数据量置为0的MySQL语句,t_order_delivery-->
    @Override
    public List<Map<String, Object>> deliveryList(Map<String, Object> map) {
        return tableReportDao.deliveryList(map);
    }

    //	<!--以下是查询某一年1月到12月的数据量，没有数据也显示月份，数据量置为0的MySQL语句,t_orders_tms-->
    @Override
    public List<Map<String, Object>> tmsList(Map<String, Object> map) {
        return tableReportDao.tmsList(map);
    }
}
