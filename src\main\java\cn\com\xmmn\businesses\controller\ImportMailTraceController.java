package cn.com.xmmn.businesses.controller;

import java.util.List;
import java.util.Map;

import cn.com.xmmn.businesses.domain.ImportMailTraceDO;
import cn.com.xmmn.businesses.service.ImportMailTraceService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import cn.com.xmmn.common.utils.PageUtils;
import cn.com.xmmn.common.utils.Query;


/**
 * 进口轨迹表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-04 10:28:53
 */
 
@Controller
@RequestMapping("/importMailTrace/importMailTrace")
public class ImportMailTraceController {
	@Autowired
	private ImportMailTraceService importMailTraceService;
	
	@GetMapping()
	@RequiresPermissions("importMailTrace:importMailTrace:importMailTrace")
	String ImportMailTrace(){
	    return "importMailTrace/importMailTrace/importMailTrace";
	}
	
	@ResponseBody
	@GetMapping("/list")
	@RequiresPermissions("importMailTrace:importMailTrace:importMailTrace")
	public PageUtils list(@RequestParam Map<String, Object> params){
		//查询列表数据
        Query query = new Query(params);
		List<ImportMailTraceDO> importMailTraceList = importMailTraceService.list(query);
		int total = importMailTraceService.count(query);
		PageUtils pageUtils = new PageUtils(importMailTraceList, total);
		return pageUtils;
	}
	
/*	@GetMapping("/add")
	@RequiresPermissions("importMailTrace:importMailTrace:add")
	String add(){
	    return "importMailTrace/importMailTrace/add";
	}

	@GetMapping("/edit/{id}")
	@RequiresPermissions("importMailTrace:importMailTrace:edit")
	String edit(@PathVariable("id") Integer id,Model model){
		ImportMailTraceDO importMailTrace = importMailTraceService.get(id);
		model.addAttribute("importMailTrace", importMailTrace);
	    return "importMailTrace/importMailTrace/edit";
	}*/
	
	/**
	 * 保存
	 */
	/*@ResponseBody
	@PostMapping("/save")
	@RequiresPermissions("system:importMailTrace:add")
	public R save( ImportMailTraceDO importMailTrace){
		if(importMailTraceService.save(importMailTrace)>0){
			return R.ok();
		}
		return R.error();
	}*/
	/**
	 * 修改
	 */
	/*@ResponseBody
	@RequestMapping("/update")
	@RequiresPermissions("system:importMailTrace:edit")
	public R update( ImportMailTraceDO importMailTrace){
		importMailTraceService.update(importMailTrace);
		return R.ok();
	}
	*/
	/**
	 * 删除
	 */
/*	@PostMapping( "/remove")
	@ResponseBody
	@RequiresPermissions("system:importMailTrace:remove")
	public R remove( Integer id){
		if(importMailTraceService.remove(id)>0){
		return R.ok();
		}
		return R.error();
	}*/
	
	/**
	 * 删除
	 */
	/*@PostMapping( "/batchRemove")
	@ResponseBody
	@RequiresPermissions("system:importMailTrace:batchRemove")
	public R remove(@RequestParam("ids[]") Integer[] ids){
		importMailTraceService.batchRemove(ids);
		return R.ok();
	}*/
	
}
