package cn.com.xmmn;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.servlet.resource.ResourceUrlEncodingFilter;


@EnableAutoConfiguration(exclude = {
        org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration.class
})
@EnableTransactionManagement
@ServletComponentScan
@MapperScan("cn.com.xmmn.*.dao")
@SpringBootApplication
@EnableCaching
@EnableScheduling
public class SerivceApplication {
    public static void main(String[] args) {
        SpringApplication.run(SerivceApplication.class, args);
        System.out.println("ヾ(◍°∇°◍)ﾉﾞ    服务启动成功      ヾ(◍°∇°◍)ﾉﾞ");
    }


    /**
     *  添加静态资源URL编码过滤器，用于处理静态资源URL中的版本控制。
     * @return ResourceUrlEncodingFilter
     **/
    @Bean
    public ResourceUrlEncodingFilter resourceUrlEncodingFilter(){
        return new ResourceUrlEncodingFilter();
    }
}
