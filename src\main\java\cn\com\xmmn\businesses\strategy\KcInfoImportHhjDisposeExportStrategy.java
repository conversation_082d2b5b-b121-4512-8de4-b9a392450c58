package cn.com.xmmn.businesses.strategy;

import cn.com.xmmn.businesses.service.ImportMailService;
import cn.com.xmmn.businesses.vo.ImportMailShouldListVO;
import cn.com.xmmn.businesses.vo.KcInfoMaillExportVO;
import cn.com.xmmn.businesses.vo.YclMaillExportVO;
import cn.com.xmmn.common.enums.ImportHhjDisposeExportNameEnum;
import cn.hutool.core.convert.Convert;
import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * (邮件)接收-开拆 开拆量导出
 *
 */
@Component
@Slf4j
public class KcInfoImportHhjDisposeExportStrategy extends AbstractImportHhjDisposeExportStrategy{
    @Autowired
    private ImportMailService importMailService;

    /**
     * 导出
     *
     * @param map 查询条件
     */
    @Override
    public void exportExcel(Map<String, Object> map,HttpServletResponse response) {

        List<ImportMailShouldListVO> list = importMailService.getShouldList(map);
        // 转换为导出的 VO 类型
        List<KcInfoMaillExportVO> mailExportVOS = convertToExportVO(list);
        // 设置导出响应
        setupResponseHeaders(response, map, mailExportVOS);
        try {
            // 导出
            EasyExcel.write(response.getOutputStream(), getExportVOClass())
                    .sheet(getSheetName(map))
                    .doWrite(mailExportVOS);
        } catch (Exception e) {
            log.error("Excel导出失败，异常信息：", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);  // 返回500错误
        }
    }
    /**
     * 将原始数据转换为特定的 ExportVO
     */
    @Override
    protected List<KcInfoMaillExportVO> convertToExportVO(List<ImportMailShouldListVO> list) {
        List<KcInfoMaillExportVO> exportVOS = new ArrayList<>();
        for (ImportMailShouldListVO mailShouldListVO : list) {
            KcInfoMaillExportVO exportVO = new KcInfoMaillExportVO();
            // 使用 BeanUtils 复制属性
            BeanUtils.copyProperties(mailShouldListVO, exportVO);
            exportVOS.add(exportVO);
        }
        return exportVOS;
    }

    /**
     * 获取导出的 VO 类型
     */
    @Override
    protected Class<KcInfoMaillExportVO> getExportVOClass() {
        return KcInfoMaillExportVO.class;
    }

    //获取 sheet 名称
    @Override
    protected String getSheetName(Map<String, Object> map) {
        String pageHandled = Convert.toStr(map.get("pageHandled"));
        return ImportHhjDisposeExportNameEnum.getName(pageHandled);
    }
}
