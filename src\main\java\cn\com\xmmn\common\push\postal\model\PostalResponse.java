package cn.com.xmmn.common.push.postal.model;

import lombok.Data;

import java.util.List;

/**
 *  邮政新一代响应
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024年10月9日
 */
@Data
public class PostalResponse {


    /**
     * 接收方标识
     */
    private String receiveID;

    /**
     * 执行结果 true或false
     */
    private boolean responseState;
    /**
     * 错误描述信息
     */
    private String errorDesc;

    /**
     * 执行结果
     */
    private List<ResponseItem> responseItems;

    @Data
    public class ResponseItem  {
        /**
         * 总包条码
         */
        private String traceNo;
        private String mailNo;

        /**
         * 是否成功
         */
        private boolean success;

        /**
         * 失败原因
         */
        private String reason;
    }

}
