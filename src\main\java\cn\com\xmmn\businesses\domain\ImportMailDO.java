package cn.com.xmmn.businesses.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;



/**
 * 进口邮件表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-23 17:32:54
 */
@Data
@TableName("t_import_mail")
public class ImportMailDO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//ID;批次号
	private Integer id;
	//邮件号码
	private String itemId;
	//申报企业
	private String declareComp;
	//收寄日期
	private Date opTime;
	//申报类别
	private String custDeclareClassCode;
	//发运海关时间
	private Date cusTime;
	//原寄局
	private String originOrgName;
	//寄达局
	private String destOrgName;
	//清关口岸
	private String custCode;
	//寄件人
	private String senderName;
	//寄件人电话
	private String senderPhone;
	//寄件人地址
	private String senderAddr;
	//收件人
	private String recName;
	//收件人电话
	private String recipientAddress;
	//收件人地址
	private String recAddress;
	//重量
	private BigDecimal totalWeight;
	//邮费
	private BigDecimal postage;
	//内件总价
	private BigDecimal declareTotalValue;
	//税费
	private BigDecimal taxTotalValue;
	//备注
	private String remarkTxt;
	//回执时间
	private Date replyTime;
	//回执说明
	private String replyNotes;
	//回执状态
	private String replyState;
	//业务时间
	private Date pt;
	//形成总包时间
	private Date contractTime;
	//放行时间
	private Date releaseTime;
	//征税时间
	private Date taxTime;
	//查验时间
	private Date checkTime;
	//补充申报时间
	private Date declarationTime;
	//货物报关时间
	private Date customsTime;
	//人工审单时间
	private Date reviewTime;
	//乐观锁;数据版本号
	private Integer revision;
	//创建人id
	private Integer createUserId;
	//创建部门id
	private Integer createDeptId;
	//创建人
	private String createUserName;
	//创建部门
	private String createDeptName;
	//创建时间
	private Date createTime;
	//修改人id
	private Integer updateUserId;
	//修改人
	private String updateUserName;
	//修改部门id
	private Integer updateDeptId;
	//修改部门
	private String updateDeptName;
	//修改时间
	private Date updateTime;


	//客户名称
	private String custName;
	//业务种类代码
	private String productCode;
	//业务种类名称
	private String product;

    //总包条码
	private String barCode;

	//互换局接收时间
	private Date chOrgRecvTime;

	//交换站发运时间
	private Date recvOrgSendTime;

	//接收交換站代码
	private String recvOrgCode;
	//接收交換站名称
	private String recvOrgName;
    //交换站接收时间
	private  Date recvOrgTime;
    //接收互换站代码
	private String chOrgCode;
    //接收互换站名称
	private String chOrgName;

    //经转互换站代码
	private String pChOrgCode;
	//经转互换站
	private String pChOrgName;
	//经转互换站发运时间
	private Date pChOrgSendTime;
    //发运时间
	private Date chOrgSendTime;

    //互换站开拆时间
	private Date chOrgOpenTime;

	//封发时间
    private Date chOrgDisTime;
	//封发内部格口时间
	private Date waitTestTime;
	private Date brokerTime;
	private Date custSubmitTime;
	private Date waitOpenTime;
	private Date disBrokerTime;
	private Date brokerOutTime;
	private Date processRecvTime;
	private Date deliverTime;

    //报关行
    private String brokerName;
	private String brokerCode;
	private String ifCustTax;
	private Date custTaxTime;


	//是否多次开拆接收
    private String ifOpenTime;

	private Date deliverRecvTime;

}
