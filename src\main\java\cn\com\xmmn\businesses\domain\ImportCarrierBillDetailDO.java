package cn.com.xmmn.businesses.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;



/**
 * 承运商账单明细
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-27 15:37:47
 */
@Data
public class ImportCarrierBillDetailDO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//ID
	private Integer id;
	//乐观锁;数据版本号
	private Integer revision;
	//创建人id
	private Integer createUserId;
	//创建人
	private String createUserName;
	//创建部门id
	private Integer createDeptId;
	//创建部门
	private String createDeptName;
	//创建时间
	private Date createTime;
	//修改人id
	private Integer updateUserId;
	//修改人
	private String updateUserName;
	//修改部门id
	private Integer updateDeptId;
	//修改部门
	private String updateDeptName;
	//修改时间
	private Date updateTime;
	//邮件种类
	private String mailType;
	//承运人名称
	private String carrierName;
	//Barcode
	private String barcode;
	//CN38时间
	private Date cn38time;
	private String cn38timeStr;
	//接收扫描时间
	private Date scanTime;
	private String scanTimeStr;
	//启运地点
	private String startPlace;
	//启运时间
	private Date startTime;
	private String startTimeStr;
	//中转到达地点
	private String transArrivePlace;
	//中转到达时间
	private Date transArriveTime;
	private String transArriveTimeStr;
	//中转启运时间
	private Date transStartTime;
	private String transStartTimeStr;
	//到达地点
	private String arrivePlace;
	//目的地到达时间
	private Date arriveTime;
	private String arriveTimeStr;
	//目的地交邮时间
	private Date holdTime;
	private String holdTimeStr;
	//收费路由
	private String tollRoute;
	//航班
	private String flightNum;
	//重量
	private BigDecimal weight;
	//费率
	private BigDecimal feeRate;
	//金额
	private BigDecimal amount;
	//币种
	private String currency;
	//账务时期
	private String accountPeriod;
	//账单编号
	private String billNo;
	//备注
	private String remark;
	//运能编码
	private String capacityCode;
	//箱板类型
	private String caseType;
	//集装器号（板号）
	private String packNum;
	//批次id
	private Integer batchId;

	private String importContent;
	private String importState;


}
