package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.CarrierDealerArriveToPostDao;
import cn.com.xmmn.businesses.dao.CarrierDealerDepartDao;
import cn.com.xmmn.businesses.dao.CarrierDealerRecivDao;
import cn.com.xmmn.businesses.domain.CarrierDealerRecivDO;
import cn.com.xmmn.businesses.dt.CarrierDealerRecivDetailDT;
import cn.com.xmmn.businesses.service.CarrierDealerArriveToPostService;
import cn.com.xmmn.businesses.service.CarrierDealerDepartService;
import cn.com.xmmn.businesses.vo.CarrierDealerArriveToPostListVo;
import cn.com.xmmn.businesses.vo.CarrierDealerDepartListVo;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Service
public class CarrierDealerArriveToPostServiceImpl extends ServiceImpl<CarrierDealerRecivDao,CarrierDealerRecivDO> implements CarrierDealerArriveToPostService {

    @Autowired
    private CarrierDealerArriveToPostDao carrierDealerArriveToPostDao;

    @Override
    public List<CarrierDealerArriveToPostListVo> group(Map<String,Object> query) {
        return carrierDealerArriveToPostDao.group(query);
    }

    @Override
    public List<CarrierDealerArriveToPostListVo>  groupCount(Map<String,Object> query) {
        return carrierDealerArriveToPostDao.groupCount(query);
    }


    @Override
    public List<CarrierDealerRecivDO> detail(CarrierDealerRecivDetailDT dealerRecivDetailDT) {
        return carrierDealerArriveToPostDao.detail(dealerRecivDetailDT);
    }

    @Override
    public Integer detailCount(CarrierDealerRecivDetailDT dealerRecivDetailDT) {
        return carrierDealerArriveToPostDao.detailCount(dealerRecivDetailDT);
    }
}
