package cn.com.xmmn.businesses.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/*
* 商业出口手工
* */

@Data
public class CommercialDO implements Serializable {
    private static final long serialVersionUID = 1L;
    //批次id
    private Integer id;
    //乐观锁;数据版本号
    private Integer revision;
    //创建人id
    private Integer createUserId;
    //创建人
    private String createUserName;
    //创建部门id
    private Integer createDeptId;
    //创建部门
    private String createDeptName;
    //创建时间
    private Date createTime;
    //修改人id
    private Integer updateUserId;
    //修改人
    private String updataUserName;
    //修改部门id
    private Integer updataDeptId;
    //修改部门
    private String updataDeptName;
    //修改时间
    private Date updateTime;
    //业务产品
    private String businessProduct;
    //大客户号
    private String customerCode;
    //大宗客户
    private String customerName;
    //邮件号
    private String itemNo;
    //寄达国
    private  String receiveCountry;
    //寄件人
    private  String sender;
    //重量
    private double weight;
    //金额
    private double amount;
    //收寄时间
    private Date postingDatetime;
    //批次id
    private Integer batchId;


}
