package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.CustomerOrderDeclareDao;
import cn.com.xmmn.businesses.domain.CustomerOrderDeclareDO;
import cn.com.xmmn.businesses.service.CustomerOrderDeclareService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;



@Service
public class CustomerOrderDeclareServiceImpl implements CustomerOrderDeclareService {
	@Autowired
	private CustomerOrderDeclareDao customerOrderDeclareDao;
	
	@Override
	public CustomerOrderDeclareDO get(Integer id){
		return customerOrderDeclareDao.get(id);
	}
	
	@Override
	public List<CustomerOrderDeclareDO> list(Map<String, Object> map){
		return customerOrderDeclareDao.list(map);
	}
	
	@Override
	public int count(Map<String, Object> map){
		return customerOrderDeclareDao.count(map);
	}
	
	@Override
	public int save(CustomerOrderDeclareDO customerOrderDeclare){
		return customerOrderDeclareDao.save(customerOrderDeclare);
	}
	
	@Override
	public int update(CustomerOrderDeclareDO customerOrderDeclare){
		return customerOrderDeclareDao.update(customerOrderDeclare);
	}
	
	@Override
	public int remove(Integer id){
		return customerOrderDeclareDao.remove(id);
	}
	
	@Override
	public int batchRemove(Integer[] ids){
		return customerOrderDeclareDao.batchRemove(ids);
	}
	
}
