package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.CarrierDealerDepartDao;
import cn.com.xmmn.businesses.dao.CarrierDealerRecivDao;
import cn.com.xmmn.businesses.domain.CarrierDealerRecivDO;
import cn.com.xmmn.businesses.dt.CarrierDealerRecivDetailDT;
import cn.com.xmmn.businesses.service.CarrierDealerDepartService;
import cn.com.xmmn.businesses.service.CarrierDealerRecivService;
import cn.com.xmmn.businesses.vo.CarrierDealerDepartListVo;
import cn.com.xmmn.businesses.vo.CarrierDealerRecivListVo;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Service
public class CarrierDealerDepartServiceImpl  extends ServiceImpl<CarrierDealerRecivDao,CarrierDealerRecivDO> implements CarrierDealerDepartService {

    @Autowired
    private CarrierDealerDepartDao carrierDealerDepartDao;

    @Override
    public List<CarrierDealerDepartListVo> group(Map<String,Object> query) {


        return carrierDealerDepartDao.group(query);
    }

    @Override
    public List<CarrierDealerDepartListVo>  groupCount(Map<String,Object> query) {
        return carrierDealerDepartDao.groupCount(query);
    }


    @Override
    public List<CarrierDealerRecivDO> detail(CarrierDealerRecivDetailDT dealerRecivDetailDT) {
        return carrierDealerDepartDao.detail(dealerRecivDetailDT);
    }

    @Override
    public Integer detailCount(CarrierDealerRecivDetailDT dealerRecivDetailDT) {
        return carrierDealerDepartDao.detailCount(dealerRecivDetailDT);
    }
}
