package cn.com.xmmn.common.utils;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.security.Security;
import java.util.Arrays;
import java.util.Base64;

/**
 * @author: zxh
 * @since: 2022-11-24 18:21
 * @description:
 */
public class EncryptUtils {

    private static final String SECRET = "AES";
    private static final String CIPHER_ALGORITHM = "AES/ECB/PKCS7Padding";

    private static Cipher encryptCipher;
    private static Cipher decryptCipher;

    private static String encryptKey = "chinacourierhk";

    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    /**
     * 自动代码补齐位数
     *
     * @param key String
     * @return String
     * <AUTHOR>
     * @since 2022-11-27 19:43
     */
    public static String paddingKeys(String key) {
        // 如果密钥不足16位，那么就补足.  这个if 中的内容很重要
        byte[] keyBytes = key.getBytes();
        int base = 16;
        if (keyBytes.length % base != 0) {
            int groups = keyBytes.length / base + 1;
            byte[] temp = new byte[groups * base];
            Arrays.fill(temp, (byte) 0);
            System.arraycopy(keyBytes, 0, temp, 0, keyBytes.length);
            keyBytes = temp;
        }
        return new String(keyBytes);
    }

    /**
     * AES加密ECB模式PKCS7Padding填充方式
     *
     * @param str String
     * @param key String
     * @return String
     * <AUTHOR>
     * @since 2022-11-27 19:44
     */
    public static String aes128ECBPkcs7PaddingEncrypt(String str, String key) throws Exception {
        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
        byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
        cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(keyBytes, SECRET));
        byte[] doFinal = cipher.doFinal(str.getBytes(StandardCharsets.UTF_8));
        return new String(Base64.getEncoder().encode(doFinal));
    }

    /**
     * AES解密ECB模式PKCS7Padding填充方式
     *
     * @param str String
     * @param key String
     * @return String
     * <AUTHOR>
     * @since 2022-11-27 19:45
     */
    public static String aes128ECBPkcs7PaddingDecrypt(String str, String key) throws Exception {
        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
        byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
        cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(keyBytes, SECRET));
        byte[] doFinal = cipher.doFinal(Base64.getDecoder().decode(str));
        return new String(doFinal);
    }

    /**
     * AES加密
     */
    public static String aesSha1prngEncrypt(String content) throws Exception {
        if (encryptCipher == null) {
            KeyGenerator kgen = KeyGenerator.getInstance("AES");
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            secureRandom.setSeed(encryptKey.getBytes());
            kgen.init(128, secureRandom);
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(kgen.generateKey().getEncoded(), "AES"));
            encryptCipher = cipher;
        }
        byte[] bytes = encryptCipher.doFinal(content.getBytes("utf-8"));

        return base64Encode(bytes);
    }

    /**
     * AES解密
     */
    public static String aesSha1prngDecrypt(String encryptString) throws Exception {
        byte[] encryptBytes = base64Decode(encryptString);

        if (decryptCipher == null) {
            KeyGenerator kgen = KeyGenerator.getInstance("AES");
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            secureRandom.setSeed(encryptKey.getBytes());
            kgen.init(128, secureRandom);
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(kgen.generateKey().getEncoded(), "AES"));
            decryptCipher = cipher;
        }
        byte[] decryptBytes = decryptCipher.doFinal(encryptBytes);
        return new String(decryptBytes);
    }

    public static String base64Encode(byte[] bytes) {
        return new BASE64Encoder().encode(bytes);
    }

    public static byte[] base64Decode(String base64Code) throws Exception {
        return StringUtils.isBlank(base64Code) ? null : new BASE64Decoder().decodeBuffer(base64Code);
    }
}
