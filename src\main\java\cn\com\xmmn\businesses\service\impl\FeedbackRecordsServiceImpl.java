package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.FeedbackRecordsDao;
import cn.com.xmmn.businesses.dao.FeedbackRecordsPushLogDao;
import cn.com.xmmn.businesses.domain.CarrierBillLogDO;
import cn.com.xmmn.businesses.domain.FeedbackRecordsDO;
import cn.com.xmmn.businesses.domain.FeedbackRecordsPushLogDO;
import cn.com.xmmn.businesses.service.FeedbackRecordsService;
import cn.com.xmmn.common.enums.FeedbackRecordsPushTypeEnum;
import cn.com.xmmn.common.enums.PostalTraceOpCodeEnum;
import cn.com.xmmn.common.enums.PushStatusEnum;
import cn.com.xmmn.common.push.postal.PostalPushService;
import cn.com.xmmn.common.push.postal.model.PostalResponse;
import cn.com.xmmn.common.push.postal.model.PostalTraceMessageBody;
import cn.com.xmmn.common.push.postal.model.PostalTraces;
import cn.hutool.Hutool;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


@Service
@RequiredArgsConstructor
public class FeedbackRecordsServiceImpl implements FeedbackRecordsService {

    private final FeedbackRecordsDao feedbackRecordsDao;

    private final PostalPushService postalPushService;

    private final FeedbackRecordsPushLogDao feedbackRecordsPushLogDao;

    @Override
    public FeedbackRecordsDO get(Integer id) {
        return feedbackRecordsDao.get(id);
    }

    @Override
    public List<FeedbackRecordsDO> list(Map<String, Object> map) {
        return feedbackRecordsDao.list(map);
    }

    @Override
    public int count(Map<String, Object> map) {
        return feedbackRecordsDao.count(map);
    }

    @Override
    public int save(FeedbackRecordsDO feedbackRecords) {
        return feedbackRecordsDao.save(feedbackRecords);
    }

    @Override
    public int update(FeedbackRecordsDO feedbackRecords) {
        return feedbackRecordsDao.update(feedbackRecords);
    }

    @Override
    public int remove(Integer id) {
        return feedbackRecordsDao.remove(id);
    }

    @Override
    public int batchRemove(Integer[] ids) {
        return feedbackRecordsDao.batchRemove(ids);
    }

    @Async("asyncTaskPool")
    @Override
    public void batchPush(Integer[] ids, Integer pushType) {

        //根据推送类型获取查询条件
        LambdaQueryWrapper<FeedbackRecordsDO> queryWrapper = buildQueryWrapper(pushType);
        queryWrapper.in(FeedbackRecordsDO::getId, ids);

        //查询出需要推送反馈信息列表
        List<FeedbackRecordsDO> feedbackRecordsDOS = feedbackRecordsDao.selectList(queryWrapper);
        //推送
        push(pushType, feedbackRecordsDOS);

    }



    @Override
    public List<FeedbackRecordsPushLogDO> pushLogList(Map<String, Object> params) {
        int limit = params.getOrDefault("limit", 10) instanceof Number ? ((Number) params.get("limit")).intValue() : 10;
        int page = params.getOrDefault("page", 1) instanceof Number ? ((Number) params.get("page")).intValue() : 1;
        String bagBarcode = Convert.toStr(params.get("bagBarCode"));

        Page<FeedbackRecordsPushLogDO> pageRequest = new Page(page, limit);
        LambdaUpdateWrapper<FeedbackRecordsPushLogDO> queryWrapper = new LambdaUpdateWrapper();
        queryWrapper.eq(FeedbackRecordsPushLogDO::getBagBarCode, bagBarcode);
        queryWrapper.orderByAsc(FeedbackRecordsPushLogDO::getPushType);
        queryWrapper.orderByDesc(FeedbackRecordsPushLogDO::getId);
        Page<FeedbackRecordsPushLogDO> result = feedbackRecordsPushLogDao.selectPage(pageRequest, queryWrapper);
        return result.getRecords();
    }

    @Override
    public int pushLogCount(Map<String, Object> params) {
        String bagBarcode = Convert.toStr(params.get("bagBarCode"));
        LambdaUpdateWrapper<FeedbackRecordsPushLogDO> queryWrapper = new LambdaUpdateWrapper();
        queryWrapper.eq(FeedbackRecordsPushLogDO::getBagBarCode, bagBarcode);

        return Convert.toInt(feedbackRecordsPushLogDao.selectCount(queryWrapper));
    }

    @Override
    public void pushReceiveStatus() {

        LambdaQueryWrapper<FeedbackRecordsDO> queryWrapper = buildQueryWrapper(FeedbackRecordsPushTypeEnum.RECEIC.getValue());

        //添加限制创建时间不能小于20241206
        queryWrapper.ge(FeedbackRecordsDO::getCreateTime,"2024-12-06");
        queryWrapper.orderByAsc(FeedbackRecordsDO::getReceiceDatetime);
        //查询出推送接收状态未推送的记录
        List<FeedbackRecordsDO> feedbackRecordsDOS = feedbackRecordsDao.selectPage(new Page<>(1,200),queryWrapper).getRecords();
        push(FeedbackRecordsPushTypeEnum.RECEIC.getValue(),feedbackRecordsDOS);
    }

    @Override
    public void pushDepartureStatus() {
        LambdaQueryWrapper<FeedbackRecordsDO> queryWrapper = buildQueryWrapper(FeedbackRecordsPushTypeEnum.DEPARTURE.getValue());
        //添加限制创建时间不能小于20241206
        queryWrapper.ge(FeedbackRecordsDO::getCreateTime,"2024-12-06");

        queryWrapper.orderByAsc(FeedbackRecordsDO::getDepartureTime);
        List<FeedbackRecordsDO> feedbackRecordsDOS = feedbackRecordsDao.selectPage(new Page<>(1,200),queryWrapper).getRecords();
        push(FeedbackRecordsPushTypeEnum.DEPARTURE.getValue(),feedbackRecordsDOS);
    }

    @Override
    public void pushDeliveryStatus() {
        LambdaQueryWrapper<FeedbackRecordsDO> queryWrapper = buildQueryWrapper(FeedbackRecordsPushTypeEnum.DELIVERY.getValue());
        //添加限制创建时间不能小于20241206
        queryWrapper.ge(FeedbackRecordsDO::getCreateTime,"2024-12-06");
        queryWrapper.orderByAsc(FeedbackRecordsDO::getDeliveryTime);
        List<FeedbackRecordsDO> feedbackRecordsDOS = feedbackRecordsDao.selectPage(new Page<>(1,200),queryWrapper).getRecords();

        push(FeedbackRecordsPushTypeEnum.DELIVERY.getValue(),feedbackRecordsDOS);
    }

    @Override
    public void pushPostStatus() {
        LambdaQueryWrapper<FeedbackRecordsDO> queryWrapper = buildQueryWrapper(FeedbackRecordsPushTypeEnum.POST.getValue());
        //添加限制创建时间不能小于20241206   --定时查询这个月
        queryWrapper.ge(FeedbackRecordsDO::getCreateTime,"2024-12-06");
        queryWrapper.orderByAsc(FeedbackRecordsDO::getPostTime);
        List<FeedbackRecordsDO> feedbackRecordsDOS = feedbackRecordsDao.selectPage(new Page<>(1,200),queryWrapper).getRecords();
        push(FeedbackRecordsPushTypeEnum.POST.getValue(),feedbackRecordsDOS);
    }


    /**
     * 推送信息
     * @param pushType 推送类型：FeedbackRecordsPushTypeEnum.RECEIC.getValue()
     * @param feedbackRecordsDOS 反馈信息列表
     */
    private void push(Integer pushType, List<FeedbackRecordsDO> feedbackRecordsDOS) {
        //每次分批推送20条
        int batchSize = 20;
        //总数量
        int totalSize = feedbackRecordsDOS.size();
        //分多少次
        int batchCount = (int) Math.ceil((double) totalSize / batchSize);

        for (int i = 0; i < batchCount; i++) {
            //计算起始位置
            int index = i * batchSize;

            //计算结束：起始位置+每次分批推送20条
            int toIndex = Math.min(index + batchSize, totalSize);

            //获取当前批次
            List<FeedbackRecordsDO> batchFR = feedbackRecordsDOS.subList(index, toIndex);

            List<PostalTraceMessageBody> traces = new ArrayList<>();

            //遍历
            batchFR.forEach(feedbackRecord -> {
                //创建单个邮政跟踪消息体
                PostalTraceMessageBody postalTraceMessageBody = createPostalTraceMessageBody(feedbackRecord, pushType);
                traces.add(postalTraceMessageBody);
            });

            //构建好的邮政跟踪对象
            PostalTraces postalTraces = PostalTraces.builder()
                    .traces(traces)
                    .build();

            //推送
            PostalResponse postalResponse = postalPushService.pushMailsTrack(postalTraces);

            //处理回执信息
            handlePushResponse(postalResponse, pushType);

        }
    }

    //处理回执信息
    private void handlePushResponse(PostalResponse postalResponse, int pushType) {
        if (postalResponse != null) {

            // 遍历响应项并更新反馈记录状态和添加日志
            postalResponse.getResponseItems().forEach(item -> updateFeedbackRecordStatus(item, pushType));
        }
    }


    // 更新反馈记录状态和添加日志
    private void updateFeedbackRecordStatus(PostalResponse.ResponseItem item, Integer pushType) {
        PushStatusEnum status;
        try {

            FeedbackRecordsDO recordsDO = new FeedbackRecordsDO();
            recordsDO.setBagBarCode(item.getMailNo());
            // 根据推送类型设置状态
            status = item.isSuccess() ? PushStatusEnum.PUSH_SUCCESS : PushStatusEnum.PUSH_FAILED;
            switch (FeedbackRecordsPushTypeEnum.fromValue(pushType)) {
                case RECEIC:
                    recordsDO.setIsPushReceice(status.getValue());
                    break;
                case DEPARTURE:
                    recordsDO.setIsPushDeparture(status.getValue());
                    break;
                case DELIVERY:
                    recordsDO.setIsPushDelivery(status.getValue());
                    break;
                case POST:
                    recordsDO.setIsPushPost(status.getValue());
                    break;
            }
            // 更新反馈记录
            feedbackRecordsDao.update(recordsDO);
        } catch (Exception e) {
           e.printStackTrace();
            status =PushStatusEnum.PUSH_FAILED;
        }
        //添加推送日志
        FeedbackRecordsPushLogDO feedbackRecordsPushLogDo = new FeedbackRecordsPushLogDO();
        feedbackRecordsPushLogDo.setCreateTime(new Date());
        feedbackRecordsPushLogDo.setBagBarCode(item.getMailNo());
        feedbackRecordsPushLogDo.setErrMsg(item.getReason());
        feedbackRecordsPushLogDo.setPushType(pushType);
        feedbackRecordsPushLogDo.setIsOk(status.getValue());
        feedbackRecordsPushLogDao.insert(feedbackRecordsPushLogDo);
    }

    // 创建单个邮政跟踪消息体
    private PostalTraceMessageBody createPostalTraceMessageBody(FeedbackRecordsDO record, Integer pushType) {
        String opTime = "";  // 操作时间
        String opCode = "";  // 操作代码
        String opPortCode="";// 操作口岸三位操作码
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 根据推送类型设置操作时间和操作代码
        switch (FeedbackRecordsPushTypeEnum.fromValue(pushType)) {
            case RECEIC:
                opTime = sdf.format(record.getReceiceDatetime());
                opCode = PostalTraceOpCodeEnum.AIRLINE_RECEIVED.getValue();
                opPortCode=record.getOpPortCode();
                break;
            case DEPARTURE:
                opTime = sdf.format(record.getDepartureTime());
                opCode = PostalTraceOpCodeEnum.AIRLINE_DEPARTED.getValue();
                opPortCode=record.getOpPortCode();
                break;
            case DELIVERY:
                opTime = sdf.format(record.getDeliveryTime());
                opCode = PostalTraceOpCodeEnum.AIRCRAFT_ARRIVED.getValue();
                //操作码为卸运站代码
                opPortCode=record.getOffloadPort();
                break;
            case POST:
                opTime = sdf.format(record.getPostTime());
                opCode = PostalTraceOpCodeEnum.DELIVERY_TO_FOREIGN.getValue();
                opPortCode=record.getOpPortCode();
                break;
        }

        // 创建并返回邮政跟踪消息体
        return PostalTraceMessageBody.builder()
                .traceNo(record.getBagBarCode())
                .opTime(opTime)
                .opOrgCode(record.getOpOrgCode())
                .opOrgName(record.getOpOrgName())
                .opCode(opCode)
                .traceType(record.getTraceType())
                .vehicleCode(record.getVehicleCode())
                .operatorNo(record.getOperatorNo())
                .operatorName(record.getOperatorName())
                .containerType(record.getContainerType())
                .containerNo(record.getContainerNumbers())
                .deviceType(record.getDeviceType())
                .departureDatetime(sdf.format(record.getDepartureDatetime()))
                .transClass(record.getTransClass())
                .opPortCode(opPortCode) //如果发飞机达到进港的事件时，操作码改成取卸运站，其他的则取操作口岸
                .departPort(record.getDepartPort())
                .offloadPort(record.getOffloadPort())
                .dispBarCode(record.getDispBarCode())
                .executionMode(record.getExecutionMode())
                .isHybrid(record.getIsHybrid())
                .airlinebillNo(record.getAirlinebillNo())
                .deviceType(record.getTraceType())
                .build();
    }

    /**
     * 根据推送类型构建查询条件
     *
     * @param pushType
     * @return
     */
    private LambdaQueryWrapper<FeedbackRecordsDO> buildQueryWrapper(Integer pushType) {

        LambdaQueryWrapper<FeedbackRecordsDO> queryWrapper = new LambdaQueryWrapper();

        if (FeedbackRecordsPushTypeEnum.RECEIC.getValue() == pushType) {
            //接收

            //未推送接收或失败
            queryWrapper.in(FeedbackRecordsDO::getIsPushReceice, PushStatusEnum.UNPUSHED.getValue(), PushStatusEnum.PUSH_FAILED.getValue());
            queryWrapper.isNotNull(FeedbackRecordsDO::getReceiceDatetime);
        } else if (FeedbackRecordsPushTypeEnum.DEPARTURE.getValue() == pushType) {
            //启运

            //已推送接收
//            queryWrapper.eq(FeedbackRecordsDO::getIsPushReceice, PushStatusEnum.PUSH_SUCCESS.getValue());

            //未推送启运或失败
            queryWrapper.in(FeedbackRecordsDO::getIsPushDeparture, PushStatusEnum.UNPUSHED.getValue(), PushStatusEnum.PUSH_FAILED.getValue());

            //启运时间不为空
            queryWrapper.isNotNull(FeedbackRecordsDO::getDepartureTime);
        } else if (FeedbackRecordsPushTypeEnum.DELIVERY.getValue() == pushType) {
            //运抵

//            //已推送接收
//            queryWrapper.eq(FeedbackRecordsDO::getIsPushReceice, PushStatusEnum.PUSH_SUCCESS.getValue());
//            //已推送启运
//            queryWrapper.eq(FeedbackRecordsDO::getIsPushDeparture, PushStatusEnum.PUSH_SUCCESS.getValue());

            //未推送运抵或失败
            queryWrapper.in(FeedbackRecordsDO::getIsPushDelivery, PushStatusEnum.UNPUSHED.getValue(), PushStatusEnum.PUSH_FAILED.getValue());
            //运抵时间不为空
            queryWrapper.isNotNull(FeedbackRecordsDO::getDeliveryTime);
        } else if (FeedbackRecordsPushTypeEnum.POST.getValue() == pushType) {
            //交邮

//            //已推送接收
//            queryWrapper.eq(FeedbackRecordsDO::getIsPushReceice, PushStatusEnum.PUSH_SUCCESS.getValue());
//            //已推送启运
//            queryWrapper.eq(FeedbackRecordsDO::getIsPushDeparture, PushStatusEnum.PUSH_SUCCESS.getValue());
//            //已推送启运
//            queryWrapper.eq(FeedbackRecordsDO::getIsPushDelivery, PushStatusEnum.PUSH_SUCCESS.getValue());

            //未推送交邮或失败
            queryWrapper.in(FeedbackRecordsDO::getIsPushPost, PushStatusEnum.UNPUSHED.getValue(), PushStatusEnum.PUSH_FAILED.getValue());

            //交邮时间不为空
            queryWrapper.isNotNull(FeedbackRecordsDO::getPostTime);
        }
        return queryWrapper;
    }
}
