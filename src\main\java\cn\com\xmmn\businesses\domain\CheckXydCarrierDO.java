package cn.com.xmmn.businesses.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 核对表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-20 13:12:29
 */
@Data
public class CheckXydCarrierDO implements Serializable {

	private static final long serialVersionUID = 1L;
	//Carrier
	//对方单位
	private  String danWei="中国邮政速递物流有限公司";
    //袋子数量
	private Integer daiShu;
	//ID
	private Integer id;
	//乐观锁;数据版本号
	private Integer revision;
	//创建人id
	private Integer createUserId;
	//创建人
	private String createUserName;
	//创建部门id
	private Integer createDeptId;
	//创建部门
	private String createDeptName;
	//创建时间
	private Date createTime;
	//修改人id
	private Integer updateUserId;
	//修改人
	private String updateUserName;
	//修改部门id
	private Integer updateDeptId;
	//修改部门
	private String updateDeptName;
	//修改时间
	private Date updateTime;
	//邮件种类
	private String mailType;
	//承运人名称
	private String carrierName;
	//Barcode
	private String barcode;
	//CN38时间
	private Date cn38time;
	//接收扫描时间
	private Date scanTime;
	//启运地点
	private String startPlace;
	//启运时间
	private Date startTime;
	//中转到达地点
	private String transArrivePlace;
	//中转到达时间
	private Date transArriveTime;
	//中转启运时间
	private Date transStartTime;
	//到达地点
	private String arrivePlace;
	//目的地到达时间
	private Date arriveTime;
	//目的地交邮时间
	private Date holdTime;
	//收费路由
	private String tollRoute;
	//航班
	private String flightNum;
	//重量
	private BigDecimal weight;
	//费率
	private BigDecimal feeRate;
	//金额
	private BigDecimal amount;
	//币种
	private String currency;
	//账务时期
	private String accountPeriod;
	//账单编号
	private String billNo;
	//备注
	private String remark;
	//运能编码
	private String capacityCode;
	//箱板类型
	private String caseType;
	//集装器号（板号）
	private String packNum;
	//是否产生安检退回
	private String isReturnFlag;
	//是否有改航信息
	private String isChangeFlightFlag;
	//改航航班
	private String changeFlight;
	//封发时间
	private Date dispatchTime;
	//批次id
	private Integer batchId;
	//核对时间
	private Date checkTime;
	//核对人id
	private String checkPersonId;
	//核对人姓名
	private String checkPersonName;
	//xyd
	private Integer	xydRevision;
	private Integer	xydCreateUserId;
	private String	xydCreateUserName;
	private Integer	xydCreateDeptId;
	private String 	xydCreateDeptName;
	private Date 	xydCreateTime;
	private Integer	xydUpdateUserId;
	private String 	xydUpdateUserName;
	private Integer	xydUpdateDeptId;
	private String 	xydUpdateDeptName;
	private Date 	xydUpdateTime;
	private String 	xydMailType;
	private String 	xydCarrierName;
	private String 	xydBarcode;
	private Date 	xydCn38time;
	private Date 	xydScanTime;
	private String 	xydStartPlace;
	private Date 	xydStartTime;
	private String 	xydTransArrivePlace;
	private Date 	xydTransArriveTime;
	private Date 	xydTransStartTime;
	private String	xydArrivePlace;
	private Date 	xydArriveTime;
	private Date 	xydHoldTime;
	private String	xydTollRoute;
	private String	xydFlightNum;
	private BigDecimal 	xydWeight;
	private BigDecimal 	xydFeeRate;
	private BigDecimal 	xydAmount;
	private String 	xydCurrency;
	private String 	xydAccountPeriod;
	private String 	xydBillNo;
	private String 	xydRemark;
	private String 	xydCapacityCode;
	private String 	xydCaseType;
	private String 	xydPackNum;
	private String 	xydIsReturnFlag;
	private String 	xydIsChangeFlightFlag;
	private String 	xydChangeFlight;
	private Date 	xydDispatchTime;
	private Integer	xydBatchId;
	private String  matchState;
	private String  arriveState;

}
