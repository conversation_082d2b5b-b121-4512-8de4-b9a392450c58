package cn.com.xmmn.businesses.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;



/**
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-06 10:36:35
 */
@Data
@ApiModel(value = "香港出口表",description = "")
@TableName(value = "tb_xg_out_mail")
public class XgOutMailDO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//
	private String id;
	//
	private String itemId;
	//
	private String cltOrgCode;
	//
	private Date cltDatetime;
	//
	private String opOrgName;
	//
	private Date opTime;
	//
	private Date oeBillStartTime;
	//
	private String orgName;
	//
	private Date unloadOpTime;
	//
	private String oeBagBarcode;
	//
	private String flightNumber;
	//
	private String newFlightNo;
	//
	private String receiverCountryCode;
	//
	private String oeDest;
	//
	private String oeDestName;
	//
	private Date gmtModified;
	//
	private Date pt;
	//
	private String revision;
	//
	private String createUserId;
	//
	private String createDeptId;
	//
	private String createUserName;
	//
	private String createDeptName;
	//
	private Date createTime;
	//
	private String updateUserId;
	//
	private String updateUserName;
	//
	private String updateDeptId;
	//
	private String updateDeptName;
	//
	private String cltProvinceCode;
	//
	private String cltProvinceName;
	//
	private String cltCityCode;
	//
	private String cltCityName;
	//
	private String cyssxId;
	//
	private String carrier;
	//
	private String carrierName;
	//
	private String receiverCountryName;
	//
	private String receiverCountry;
	//
	private String orgCode;
	//
	private String provinceCode;
	//
	private String provinceName;
	//
	private String opOrgCode;


}
