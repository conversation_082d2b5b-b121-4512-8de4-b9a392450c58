package cn.com.xmmn.businesses.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 邮件空运渠道账单第一邮通账单
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-22 17:37:38
 */
public class AirMailChannelBillsFristBillDO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//ID
	private Integer id;
	//乐观锁;数据版本号
	private Integer revision;
	//创建人id
	private Integer createUserId;
	//创建人
	private String createUserName;
	//创建部门id
	private Integer createDeptId;
	//创建部门
	private String createDeptName;
	//创建时间
	private Date createTime;
	//修改人id
	private Integer updateUserId;
	//修改人
	private String updateUserName;
	//修改部门id
	private Integer updateDeptId;
	//修改部门
	private String updateDeptName;
	//修改时间
	private Date updateTime;
	//序号
	private Integer serialNumber;
	//发运日期
	private Date dateOfShipment;
	//出发地
	private String startPlace;
	//航线;日期+时间
	private String airRoute;
	//总包号
	private String packageNo;
	//袋数
	private Integer bags;
	//重量
	private BigDecimal weight;
	//邮件类型
	private String emailType;
	//结算单价
	private BigDecimal settlementPrice;
	//结算金额
	private BigDecimal settlementAmount;
	//批次id
	private Integer batchId;
	//口岸id
	private String portId;
	//口岸名称
	private String portName;
	//渠道商id
	private Integer channelId;
	//渠道商名称
	private String channelName;
	//航班号
	private String flightNumber;


	/**
	 * 设置：ID
	 */
	public void setId(Integer id) {
		this.id = id;
	}
	/**
	 * 获取：ID
	 */
	public Integer getId() {
		return id;
	}
	/**
	 * 设置：乐观锁;数据版本号
	 */
	public void setRevision(Integer revision) {
		this.revision = revision;
	}
	/**
	 * 获取：乐观锁;数据版本号
	 */
	public Integer getRevision() {
		return revision;
	}
	/**
	 * 设置：创建人id
	 */
	public void setCreateUserId(Integer createUserId) {
		this.createUserId = createUserId;
	}
	/**
	 * 获取：创建人id
	 */
	public Integer getCreateUserId() {
		return createUserId;
	}
	/**
	 * 设置：创建人
	 */
	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}
	/**
	 * 获取：创建人
	 */
	public String getCreateUserName() {
		return createUserName;
	}
	/**
	 * 设置：创建部门id
	 */
	public void setCreateDeptId(Integer createDeptId) {
		this.createDeptId = createDeptId;
	}
	/**
	 * 获取：创建部门id
	 */
	public Integer getCreateDeptId() {
		return createDeptId;
	}
	/**
	 * 设置：创建部门
	 */
	public void setCreateDeptName(String createDeptName) {
		this.createDeptName = createDeptName;
	}
	/**
	 * 获取：创建部门
	 */
	public String getCreateDeptName() {
		return createDeptName;
	}
	/**
	 * 设置：创建时间
	 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	/**
	 * 获取：创建时间
	 */
	public Date getCreateTime() {
		return createTime;
	}
	/**
	 * 设置：修改人id
	 */
	public void setUpdateUserId(Integer updateUserId) {
		this.updateUserId = updateUserId;
	}
	/**
	 * 获取：修改人id
	 */
	public Integer getUpdateUserId() {
		return updateUserId;
	}
	/**
	 * 设置：修改人
	 */
	public void setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
	}
	/**
	 * 获取：修改人
	 */
	public String getUpdateUserName() {
		return updateUserName;
	}
	/**
	 * 设置：修改部门id
	 */
	public void setUpdateDeptId(Integer updateDeptId) {
		this.updateDeptId = updateDeptId;
	}
	/**
	 * 获取：修改部门id
	 */
	public Integer getUpdateDeptId() {
		return updateDeptId;
	}
	/**
	 * 设置：修改部门
	 */
	public void setUpdateDeptName(String updateDeptName) {
		this.updateDeptName = updateDeptName;
	}
	/**
	 * 获取：修改部门
	 */
	public String getUpdateDeptName() {
		return updateDeptName;
	}
	/**
	 * 设置：修改时间
	 */
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	/**
	 * 获取：修改时间
	 */
	public Date getUpdateTime() {
		return updateTime;
	}
	/**
	 * 设置：序号
	 */
	public void setSerialNumber(Integer serialNumber) {
		this.serialNumber = serialNumber;
	}
	/**
	 * 获取：序号
	 */
	public Integer getSerialNumber() {
		return serialNumber;
	}
	/**
	 * 设置：发运日期
	 */
	public void setDateOfShipment(Date dateOfShipment) {
		this.dateOfShipment = dateOfShipment;
	}
	/**
	 * 获取：发运日期
	 */
	public Date getDateOfShipment() {
		return dateOfShipment;
	}
	/**
	 * 设置：出发地
	 */
	public void setStartPlace(String startPlace) {
		this.startPlace = startPlace;
	}
	/**
	 * 获取：出发地
	 */
	public String getStartPlace() {
		return startPlace;
	}
	/**
	 * 设置：航线;日期+时间
	 */
	public void setAirRoute(String airRoute) {
		this.airRoute = airRoute;
	}
	/**
	 * 获取：航线;日期+时间
	 */
	public String getAirRoute() {
		return airRoute;
	}
	/**
	 * 设置：总包号
	 */
	public void setPackageNo(String packageNo) {
		this.packageNo = packageNo;
	}
	/**
	 * 获取：总包号
	 */
	public String getPackageNo() {
		return packageNo;
	}
	/**
	 * 设置：袋数
	 */
	public void setBags(Integer bags) {
		this.bags = bags;
	}
	/**
	 * 获取：袋数
	 */
	public Integer getBags() {
		return bags;
	}
	/**
	 * 设置：重量
	 */
	public void setWeight(BigDecimal weight) {
		this.weight = weight;
	}
	/**
	 * 获取：重量
	 */
	public BigDecimal getWeight() {
		return weight;
	}
	/**
	 * 设置：邮件类型
	 */
	public void setEmailType(String emailType) {
		this.emailType = emailType;
	}
	/**
	 * 获取：邮件类型
	 */
	public String getEmailType() {
		return emailType;
	}
	/**
	 * 设置：结算单价
	 */
	public void setSettlementPrice(BigDecimal settlementPrice) {
		this.settlementPrice = settlementPrice;
	}
	/**
	 * 获取：结算单价
	 */
	public BigDecimal getSettlementPrice() {
		return settlementPrice;
	}
	/**
	 * 设置：结算金额
	 */
	public void setSettlementAmount(BigDecimal settlementAmount) {
		this.settlementAmount = settlementAmount;
	}
	/**
	 * 获取：结算金额
	 */
	public BigDecimal getSettlementAmount() {
		return settlementAmount;
	}
	/**
	 * 设置：批次id
	 */
	public void setBatchId(Integer batchId) {
		this.batchId = batchId;
	}
	/**
	 * 获取：批次id
	 */
	public Integer getBatchId() {
		return batchId;
	}
	/**
	 * 设置：口岸id
	 */
	public void setPortId(String portId) {
		this.portId = portId;
	}
	/**
	 * 获取：口岸id
	 */
	public String getPortId() {
		return portId;
	}
	/**
	 * 设置：口岸名称
	 */
	public void setPortName(String portName) {
		this.portName = portName;
	}
	/**
	 * 获取：口岸名称
	 */
	public String getPortName() {
		return portName;
	}
	/**
	 * 设置：渠道商id
	 */
	public void setChannelId(Integer channelId) {
		this.channelId = channelId;
	}
	/**
	 * 获取：渠道商id
	 */
	public Integer getChannelId() {
		return channelId;
	}
	/**
	 * 设置：渠道商名称
	 */
	public void setChannelName(String channelName) {
		this.channelName = channelName;
	}
	/**
	 * 获取：渠道商名称
	 */
	public String getChannelName() {
		return channelName;
	}
	/**
	 * 设置：航班号
	 */
	public void setFlightNumber(String flightNumber) {
		this.flightNumber = flightNumber;
	}
	/**
	 * 获取：航班号
	 */
	public String getFlightNumber() {
		return flightNumber;
	}

}
