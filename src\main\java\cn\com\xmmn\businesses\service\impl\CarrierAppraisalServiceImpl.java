package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.CarrierAppraisalDao;
import cn.com.xmmn.businesses.domain.CarrierAppraisalDO;
import cn.com.xmmn.businesses.domain.ImportClearanceEfficiencyDO;
import cn.com.xmmn.businesses.service.CarrierAppraisalService;
import cn.com.xmmn.common.utils.Convert;
import cn.com.xmmn.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;


@Service
public class CarrierAppraisalServiceImpl extends ServiceImpl<CarrierAppraisalDao, CarrierAppraisalDO> implements CarrierAppraisalService {
	@Autowired
	private CarrierAppraisalDao carrierAppraisalDao;




	@Override
	public Page<CarrierAppraisalDO> page(Map<String, Object> params) {
		// 默认分页参数
		final int defaultLimit = 10;
		final int defaultPage = 1;

		// 获取分页参数
		int limit = params.getOrDefault("limit", defaultLimit) instanceof Number ? ((Number) params.get("limit")).intValue() : defaultLimit;
		int page = params.getOrDefault("page", defaultPage) instanceof Number ? ((Number) params.get("page")).intValue() : defaultPage;
		// 检查分页参数的合理性
		if (limit <= 0) {
			limit = defaultLimit;
		}
		if (page <= 0) {
			page = defaultPage;
		}

		QueryWrapper queryWrapper = getQueryWrapper(params);

		Page page1=new Page(page,limit);
		page1.addOrder(OrderItem.asc("id"));

		Page<CarrierAppraisalDO> pageImport = carrierAppraisalDao.selectPage(page1, queryWrapper);

		return pageImport;
	}
	private QueryWrapper getQueryWrapper(Map<String, Object> map) {
		QueryWrapper<CarrierAppraisalDO> queryWrapper = new QueryWrapper<CarrierAppraisalDO>();
		//月份
		String appraisalMonStart = Convert.toStr(map.get("appraisalMonStart"));
		String appraisalMonEnd = Convert.toStr(map.get("appraisalMonEnd"));

		if (StringUtils.isNotEmpty(appraisalMonStart) && StringUtils.isNotEmpty(appraisalMonEnd)){
            queryWrapper.between("appraisal_mon", appraisalMonStart, appraisalMonEnd);
		}
		String productCode = Convert.toStr(map.get("productCode"));
        if(StringUtils.isNotEmpty(productCode)){
			queryWrapper.eq("product_code",productCode);
		}
		String carrierCode = Convert.toStr(map.get("carrierCode"));
		if(StringUtils.isNotEmpty(carrierCode)){
			queryWrapper.eq("carrier_code",carrierCode);
		}
		return queryWrapper;
	}

}
