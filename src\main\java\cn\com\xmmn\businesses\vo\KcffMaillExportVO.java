package cn.com.xmmn.businesses.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 导出邮件开拆-封发，封发信息
 */
@Data
@HeadRowHeight(value = 30) //头部行高
@ContentRowHeight(value = 25) //内容行高
@ColumnWidth(value = 20) //列宽
@HeadFontStyle(fontName = "宋体",fontHeightInPoints = 11)
public class KcffMaillExportVO implements Serializable {


    @ExcelProperty("客户名称")
    private String custName;
    @ExcelProperty("邮件号码")
    private String itemId;
    @ExcelProperty("业务种类")
    private String product;

    @ExcelProperty("互换局")
    private String chOrgName;
    @ExcelProperty("互换局机构")
    private String chOrgCode;


    @ExcelProperty("互换局开拆的时间")
    private Date chOrgOpenTime;
    @ExcelProperty("互换局封发时间")
    private Date chOrgDisTime;
    @ExcelProperty("封发总包条码")
    private String barCode;

    @ExcelProperty("寄达局")
    private String destOrgName;

    @ExcelProperty("互换局开拆-互换局封发 （h）")
    private Double openTimeMinusDisTime;
}
