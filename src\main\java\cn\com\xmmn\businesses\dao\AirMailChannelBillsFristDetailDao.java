package cn.com.xmmn.businesses.dao;


import cn.com.xmmn.businesses.domain.AirMailChannelBillsFristBillDO;
import cn.com.xmmn.businesses.domain.AirMailChannelBillsFristDetailDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 邮件空运渠道账单第一邮通明细
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-22 17:44:40
 */
@Mapper
public interface AirMailChannelBillsFristDetailDao {

	AirMailChannelBillsFristDetailDO get(Integer id);
	
	List<AirMailChannelBillsFristDetailDO> list(Map<String,Object> map);
	
	int count(Map<String,Object> map);
	
	int save(AirMailChannelBillsFristDetailDO airMailChannelBillsFristDetail);
	
	int update(AirMailChannelBillsFristDetailDO airMailChannelBillsFristDetail);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);

	List detail(AirMailChannelBillsFristDetailDO airMailChannelBillsFristDetail);

	List detailOfBill(AirMailChannelBillsFristDetailDO airMailChannelBillsFristDetail);

	List<AirMailChannelBillsFristDetailDO> detailList(Map<String,Object> map);


	//查询航线分组
	List<Map<String,Object>> airList(Map<String,Object> map);
}
