package cn.com.xmmn.businesses.dao;

import cn.com.xmmn.businesses.domain.OrdersImportDO;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

/**
 * 进口订单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-30 12:26:11
 */
@Mapper
public interface OrdersImportDao {

    OrdersImportDO get(Integer id);

    List<OrdersImportDO> list(Map<String, Object> map);

    Map<String, Object> countList(Map<String, Object> map);

    int count(Map<String, Object> map);

    int save(OrdersImportDO ordersImport);

    int update(OrdersImportDO ordersImport);

    int remove(Integer id);

    int batchRemove(Integer[] ids);

    //折柱一体图 商业渠道
    List<Map<String, Object>> countShipway(Map<String, Object> map);

    //环比 商业渠道
    List<Map<String, Object>> huanbiShipway(Map<String, Object> map);

    //饼图 商业渠道
    List<Map<String, Object>> oneShipway(Map<String, Object> map);

    //折柱一体图 客户
    List<Map<String, Object>> countCustomer(Map<String, Object> map);

    //环比 商业渠道
    List<Map<String, Object>> huanbiCustomer(Map<String, Object> map);

    //饼图 客户
    List<Map<String, Object>> oneCustomer(Map<String, Object> map);


}
