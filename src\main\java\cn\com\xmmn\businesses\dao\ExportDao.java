package cn.com.xmmn.businesses.dao;

import cn.com.xmmn.businesses.domain.AirMailChannelBillsFristBillDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 口岸导入批次表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-22 01:30:58
 */
@Mapper
public interface ExportDao {

	AirMailChannelBillsFristBillDO get(Integer id);
	
	List<AirMailChannelBillsFristBillDO> list(Map<String,Object> map);
	
	int count(Map<String,Object> map);
	
	int save(AirMailChannelBillsFristBillDO airMailChannelBillsFristBill);
	
	int update(AirMailChannelBillsFristBillDO airMailChannelBillsFristBill);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);
}
