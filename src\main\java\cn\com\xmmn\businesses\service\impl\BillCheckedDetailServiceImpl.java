package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.BillCheckedDetailDao;
import cn.com.xmmn.businesses.domain.BillCheckedDetailDO;
import cn.com.xmmn.businesses.domain.CheckXydCarrierDO;
import cn.com.xmmn.businesses.service.BillCheckedDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;



@Service
public class BillCheckedDetailServiceImpl implements BillCheckedDetailService {
	@Autowired
	private BillCheckedDetailDao billCheckedDetailDao;
	
	@Override
	public BillCheckedDetailDO get(Integer id){
		return billCheckedDetailDao.get(id);
	}
	
	@Override
	public List<BillCheckedDetailDO> list(Map<String, Object> map){
		return billCheckedDetailDao.list(map);
	}
	
	@Override
	public int count(Map<String, Object> map){
		return billCheckedDetailDao.count(map);
	}
	
	@Override
	public int save(CheckXydCarrierDO billCheckedDetail){
		return billCheckedDetailDao.save(billCheckedDetail);
	}
	
	@Override
	public int update(BillCheckedDetailDO billCheckedDetail){
		return billCheckedDetailDao.update(billCheckedDetail);
	}
	
	@Override
	public int remove(Integer id){
		return billCheckedDetailDao.remove(id);
	}
	
	@Override
	public int batchRemove(Integer[] ids){
		return billCheckedDetailDao.batchRemove(ids);
	}
	
}
