package cn.com.xmmn.businesses.service;


import cn.com.xmmn.businesses.domain.AirMailChannelBillsFristDetailDO;

import java.util.List;
import java.util.Map;

/**
 * 邮件空运渠道账单第一邮通明细
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-22 17:44:40
 */
public interface AirMailChannelBillsFristDetailService {
	
	AirMailChannelBillsFristDetailDO get(Integer id);
	
	List<AirMailChannelBillsFristDetailDO> list(Map<String, Object> map);
	
	int count(Map<String, Object> map);
	
	int save(AirMailChannelBillsFristDetailDO airMailChannelBillsFristDetail);
	
	int update(AirMailChannelBillsFristDetailDO airMailChannelBillsFristDetail);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);

	List detail(AirMailChannelBillsFristDetailDO airMailChannelBillsFristDetail);

	List detailOfBill(AirMailChannelBillsFristDetailDO airMailChannelBillsFristDetail);
}
