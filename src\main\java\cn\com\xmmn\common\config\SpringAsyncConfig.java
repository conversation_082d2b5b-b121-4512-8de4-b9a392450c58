package cn.com.xmmn.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.concurrent.ThreadPoolExecutor;


/**
 * 描述：自定义线程池配置类
 * <AUTHOR>
 * @version 1.0
 * @data 2024年12月5日10:04:35
 */
@ConfigurationProperties(prefix = "async-task.pool")
@Component
@Configuration
@EnableAsync
@Data
public class SpringAsyncConfig {


    private Integer coreSize;
    private Integer maxSize;
    private Integer keepAliveSeconds;
    private Integer queueCapacity;
    private Boolean allowCoreThreadTimeOut;
    private Integer awaitTerminationSeconds;

    @Bean("asyncTaskPool")
    public AsyncTaskExecutor asyncTaskPool() {

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //线程池名称前缀
        executor.setThreadNamePrefix("asyncTaskPool-");
        //核心线程池数量
        executor.setCorePoolSize(coreSize);
        //最大线程池数量
        executor.setMaxPoolSize(maxSize);
        //线程池空闲存活时间，单位秒
        executor.setKeepAliveSeconds(keepAliveSeconds);
        //拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        //线程池任务容量
        executor.setQueueCapacity(queueCapacity);
        //是否允许超时
        executor.setAllowCoreThreadTimeOut(allowCoreThreadTimeOut);
        //超时时间，单位秒
        executor.setAwaitTerminationSeconds(awaitTerminationSeconds);
        //初始化线程池
        executor.initialize();
        return executor;
    }
}