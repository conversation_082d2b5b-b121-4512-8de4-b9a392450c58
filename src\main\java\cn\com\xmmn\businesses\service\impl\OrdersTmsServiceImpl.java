package cn.com.xmmn.businesses.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import cn.com.xmmn.businesses.dao.OrdersTmsDao;
import cn.com.xmmn.businesses.domain.OrdersTmsDO;
import cn.com.xmmn.businesses.service.OrdersTmsService;



@Service
public class OrdersTmsServiceImpl implements OrdersTmsService {
	@Autowired
	private OrdersTmsDao ordersTmsDao;
	
	@Override
	public OrdersTmsDO get(Integer id){
		return ordersTmsDao.get(id);
	}
	
	@Override
	public List<OrdersTmsDO> list(Map<String, Object> map){
		return ordersTmsDao.list(map);
	}

	@Override
	public Map<String, Object> countList(Map<String, Object> map) {
		return ordersTmsDao.countList(map);
	}

	@Override
	public int count(Map<String, Object> map){
		return ordersTmsDao.count(map);
	}
	
	@Override
	public int save(OrdersTmsDO ordersTms){
		return ordersTmsDao.save(ordersTms);
	}
	
	@Override
	public int update(OrdersTmsDO ordersTms){
		return ordersTmsDao.update(ordersTms);
	}
	
	@Override
	public int remove(Integer id){
		return ordersTmsDao.remove(id);
	}
	
	@Override
	public int batchRemove(Integer[] ids){
		return ordersTmsDao.batchRemove(ids);
	}

	//<!--统计分别按渠道、客户商业出口占比-->
	public List<Map<String,Object>> percent(Map<String,Object> map){
		return ordersTmsDao.percent(map);
	};

	//<!--按月统计件数-->
	public List<Map<String,Object>> totalCount(Map<String,Object> map){
		return ordersTmsDao.totalCount(map);
	};

	//<!--按月统计重量-->
	public List<Map<String,Object>> totalWeight(Map<String,Object> map){
		return ordersTmsDao.totalWeight(map);
	};

	//<!--按月统计收入-->
	public List<Map<String,Object>> totalAmount(Map<String,Object> map){
		return ordersTmsDao.totalAmount(map);
	};

	//查询环比升降
	public List<Map<String,Object>> huanbiList(Map<String,Object> map){return ordersTmsDao.huanbiList(map);};
	
}
