package cn.com.xmmn.businesses.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (邮件)接收-开拆 开拆量邮件数据导出
 */
@Data
@HeadRowHeight(value = 30) //头部行高
@ContentRowHeight(value = 25) //内容行高
@ColumnWidth(value = 20) //列宽
@HeadFontStyle(fontName = "宋体",fontHeightInPoints = 11)
public class KcInfoMaillExportVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ExcelProperty("客户名称")
    private String custName;


    @ExcelProperty("邮件号码")
    private String itemId;
    @ExcelProperty("原寄局")
    private String originOrgName;
    @ExcelProperty("接收互换局")
    private String chOrgName;
    @ExcelProperty("互换局接收时间")
    private Date chOrgRecvTime;
    @ExcelProperty("互换局开拆时间")
    private Date chOrgOpenTime;
    @ExcelProperty("封发内部格口时间")
    private Date waitTestTime;
    @ExcelProperty("交换站接收时间")
    private  Date recvOrgTime;

    @ExcelProperty("互换局接收-开拆时长（h）")
    private Double recvAndOpenTime;
    @ExcelProperty("内部格口封发-开拆时长（h）")
    private Double sendAndOpenTime;
}
