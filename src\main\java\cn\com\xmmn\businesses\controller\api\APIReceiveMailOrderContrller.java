package cn.com.xmmn.businesses.controller.api;

import cn.com.xmmn.businesses.dt.api.DateRequest;
import cn.com.xmmn.businesses.service.CustomerOrderService;
import cn.com.xmmn.common.utils.ApiResponseData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 接收客户订单信息和邮件信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024年7月25日09:44:35
 */
@Slf4j
@RestController
@RequestMapping("/api/receive/customer/order")
@RequiredArgsConstructor
public class APIReceiveMailOrderContrller {


    private final CustomerOrderService customerOrderService;


    /**
     * 接收客户订单信息
     * @param request
     * @return
     */
    @PostMapping("")
    public ApiResponseData receiveCustomerOrder(@RequestBody DateRequest request) {
        String encryptedData = request.getEncryptedData();
        customerOrderService.receiveCustomerOrder(encryptedData);
        return ApiResponseData.ok();
    }
}
