package cn.com.xmmn.businesses.dao;



import java.util.List;
import java.util.Map;

import cn.com.xmmn.businesses.domain.BillCheckedDetailDO;
import cn.com.xmmn.businesses.domain.CheckXydCarrierDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 账单核对结果表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-08 21:58:47
 */
@Mapper
public interface BillCheckedDetailDao {

	BillCheckedDetailDO get(Integer id);
	
	List<BillCheckedDetailDO> list(Map<String,Object> map);
	
	int count(Map<String,Object> map);
	
	int save(CheckXydCarrierDO billCheckedDetail);
	
	int update(BillCheckedDetailDO billCheckedDetail);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);
}
