package cn.com.xmmn.businesses.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 邮件空运渠道账单第一邮通明细
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-22 17:44:40
 */
@Data
public class AirMailChannelBillsFristDetailDO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//ID
	private Integer id;
	//乐观锁;数据版本号
	private Integer revision;
	//创建人id
	private Integer createUserId;
	//创建人
	private String createUserName;
	//创建部门id
	private Integer createDeptId;
	//创建部门
	private String createDeptName;
	//创建时间
	private Date createTime;
	//修改人id
	private Integer updateUserId;
	//修改人
	private String updateUserName;
	//修改部门id
	private Integer updateDeptId;
	//修改部门
	private String updateDeptName;
	//修改时间
	private Date updateTime;
	//序号
	private Integer serialNumber;
	//总包;日期+时间
	private String packageNo;
	//袋号
	private String bagsNo;
	//原寄局
	private String sendOrg;
	//原寄局
	private String arriveOrg;
	//日期
	private Date dateOfShipment;
	//路由
	private String airRoute;
	//重量
	private BigDecimal weight;
	//邮件类型
	private String emailType;
	//条码
	private String barcode;
	//账单id
	private Integer billsId;
	//航班号
	private String flightNumber;
	//航班号
	private String startPlace;

	/**
	 * 设置：ID
	 */
	public void setId(Integer id) {
		this.id = id;
	}
	/**
	 * 获取：ID
	 */
	public Integer getId() {
		return id;
	}
	/**
	 * 设置：乐观锁;数据版本号
	 */
	public void setRevision(Integer revision) {
		this.revision = revision;
	}
	/**
	 * 获取：乐观锁;数据版本号
	 */
	public Integer getRevision() {
		return revision;
	}
	/**
	 * 设置：创建人id
	 */
	public void setCreateUserId(Integer createUserId) {
		this.createUserId = createUserId;
	}
	/**
	 * 获取：创建人id
	 */
	public Integer getCreateUserId() {
		return createUserId;
	}
	/**
	 * 设置：创建人
	 */
	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}
	/**
	 * 获取：创建人
	 */
	public String getCreateUserName() {
		return createUserName;
	}
	/**
	 * 设置：创建部门id
	 */
	public void setCreateDeptId(Integer createDeptId) {
		this.createDeptId = createDeptId;
	}
	/**
	 * 获取：创建部门id
	 */
	public Integer getCreateDeptId() {
		return createDeptId;
	}
	/**
	 * 设置：创建部门
	 */
	public void setCreateDeptName(String createDeptName) {
		this.createDeptName = createDeptName;
	}
	/**
	 * 获取：创建部门
	 */
	public String getCreateDeptName() {
		return createDeptName;
	}
	/**
	 * 设置：创建时间
	 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	/**
	 * 获取：创建时间
	 */
	public Date getCreateTime() {
		return createTime;
	}
	/**
	 * 设置：修改人id
	 */
	public void setUpdateUserId(Integer updateUserId) {
		this.updateUserId = updateUserId;
	}
	/**
	 * 获取：修改人id
	 */
	public Integer getUpdateUserId() {
		return updateUserId;
	}
	/**
	 * 设置：修改人
	 */
	public void setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
	}
	/**
	 * 获取：修改人
	 */
	public String getUpdateUserName() {
		return updateUserName;
	}
	/**
	 * 设置：修改部门id
	 */
	public void setUpdateDeptId(Integer updateDeptId) {
		this.updateDeptId = updateDeptId;
	}
	/**
	 * 获取：修改部门id
	 */
	public Integer getUpdateDeptId() {
		return updateDeptId;
	}
	/**
	 * 设置：修改部门
	 */
	public void setUpdateDeptName(String updateDeptName) {
		this.updateDeptName = updateDeptName;
	}
	/**
	 * 获取：修改部门
	 */
	public String getUpdateDeptName() {
		return updateDeptName;
	}
	/**
	 * 设置：修改时间
	 */
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	/**
	 * 获取：修改时间
	 */
	public Date getUpdateTime() {
		return updateTime;
	}
	/**
	 * 设置：序号
	 */
	public void setSerialNumber(Integer serialNumber) {
		this.serialNumber = serialNumber;
	}
	/**
	 * 获取：序号
	 */
	public Integer getSerialNumber() {
		return serialNumber;
	}
	/**
	 * 设置：总包;日期+时间
	 */
	public void setPackageNo(String packageNo) {
		this.packageNo = packageNo;
	}
	/**
	 * 获取：总包;日期+时间
	 */
	public String getPackageNo() {
		return packageNo;
	}
	/**
	 * 设置：袋号
	 */
	public void setBagsNo(String bagsNo) {
		this.bagsNo = bagsNo;
	}
	/**
	 * 获取：袋号
	 */
	public String getBagsNo() {
		return bagsNo;
	}
	/**
	 * 设置：原寄局
	 */
	public void setSendOrg(String sendOrg) {
		this.sendOrg = sendOrg;
	}
	/**
	 * 获取：原寄局
	 */
	public String getSendOrg() {
		return sendOrg;
	}
	/**
	 * 设置：日期
	 */
	public void setDateOfShipment(Date dateOfShipment) {
		this.dateOfShipment = dateOfShipment;
	}
	/**
	 * 获取：日期
	 */
	public Date getDateOfShipment() {
		return dateOfShipment;
	}
	/**
	 * 设置：路由
	 */
	public void setAirRoute(String airRoute) {
		this.airRoute = airRoute;
	}
	/**
	 * 获取：路由
	 */
	public String getAirRoute() {
		return airRoute;
	}
	/**
	 * 设置：重量
	 */
	public void setWeight(BigDecimal weight) {
		this.weight = weight;
	}
	/**
	 * 获取：重量
	 */
	public BigDecimal getWeight() {
		return weight;
	}
	/**
	 * 设置：邮件类型
	 */
	public void setEmailType(String emailType) {
		this.emailType = emailType;
	}
	/**
	 * 获取：邮件类型
	 */
	public String getEmailType() {
		return emailType;
	}
	/**
	 * 设置：条码
	 */
	public void setBarcode(String barcode) {
		this.barcode = barcode;
	}
	/**
	 * 获取：条码
	 */
	public String getBarcode() {
		return barcode;
	}
	/**
	 * 设置：账单id
	 */
	public void setBillsId(Integer billsId) {
		this.billsId = billsId;
	}
	/**
	 * 获取：账单id
	 */
	public Integer getBillsId() {
		return billsId;
	}
	/**
	 * 设置：航班号
	 */
	public void setFlightNumber(String flightNumber) {
		this.flightNumber = flightNumber;
	}
	/**
	 * 获取：航班号
	 */
	public String getFlightNumber() {
		return flightNumber;
	}
	/**
	 * 设置：出发地
	 */
	public void setStartPlace(String startPlace) {
		this.startPlace = startPlace;
	}
	/**
	 * 获取：出发地
	 */
	public String getStartPlace() {
		return startPlace;
	}
}
