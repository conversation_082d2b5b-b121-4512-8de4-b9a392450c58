package cn.com.xmmn.businesses.service;

import cn.com.xmmn.businesses.domain.AirMailChannelBillsFristBillDO;
import cn.com.xmmn.businesses.domain.AirMailChannelBillsFristDetailDO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 口岸导入批次表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-22 01:30:58
 */
public interface ExportService {
	
	AirMailChannelBillsFristBillDO get(Integer id);
	
	List<AirMailChannelBillsFristBillDO> list(Map<String, Object> map);

	List<AirMailChannelBillsFristDetailDO> detailList(Map<String, Object> map);

	//查询航线分组
	List<Map<String,Object>> airList(Map<String,Object> map);
	
	int count(Map<String, Object> map);
	
	int save(AirMailChannelBillsFristBillDO airMailChannelBillsFristBillDO);
	
	int update(AirMailChannelBillsFristBillDO airMailChannelBillsFristBillDO);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);

	void exportExcel(Map<String, Object> map,List<AirMailChannelBillsFristBillDO> list,List<AirMailChannelBillsFristDetailDO> detailList, List<Map<String, Object>> airList,HttpServletResponse response);
}
