package cn.com.xmmn.businesses.domain;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;



/**
 * 进口时限统计表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-20 15:02:00
 */
@Data
@TableName(value = "t_import_hhj_dispose")
public class ImportHhjDisposeDO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//ID;批次号
	@ExcelIgnore
	private Integer id;

	//互换局代码
	@ExcelProperty("机构代码")
	private String orgNo;
	//互换局名称
	@ExcelProperty("互换局")
	private String orgName;


	//交换站省份代码
	@ExcelIgnore
	private String provinceCode;
	//交换站省份名称
	@ExcelProperty("省份")
	private String provinceName;

	//接收量
	@ExcelProperty("接收量")
	private BigDecimal jsSum=BigDecimal.ZERO;
	//未接收量
	@ExcelProperty("未接收量")
	private BigDecimal wjsSum=BigDecimal.ZERO;
	//应处理量
	@ExcelProperty("应处理量")
	private BigDecimal yclSum=BigDecimal.ZERO;
	//开拆量
	@ExcelProperty("开拆量")
	private BigDecimal kcSum=BigDecimal.ZERO;
	//开拆率
	@ExcelProperty("开拆率")
	private BigDecimal kcRate=BigDecimal.ZERO;
	//逾期开拆率
	@ExcelIgnore
	private BigDecimal rqKcRate=BigDecimal.ZERO;

	@ExcelProperty("逾期开拆量")
	private BigDecimal rqKcSum=BigDecimal.ZERO;

	//开拆平均时长
	@ExcelProperty("平均时长(h)")
	private BigDecimal kcPjsc=BigDecimal.ZERO;
	//未开拆量
	@ExcelProperty("未开拆量")
	private BigDecimal wkcSum=BigDecimal.ZERO;

	//通关量
	@ExcelProperty("通关量")
	private BigDecimal tgSum=BigDecimal.ZERO;
	//通关率
	@ExcelProperty("通关率")
	private BigDecimal tgRate=BigDecimal.ZERO;
	//通关平均时长
	@ExcelProperty("通关平均时长")
	private BigDecimal tgPjsc=BigDecimal.ZERO;
	//待通关量
	@ExcelProperty("待通关量")
	private BigDecimal dtgSum=BigDecimal.ZERO;
	//未缴税量
	@ExcelProperty("未缴税量")
	private BigDecimal wjnsSum=BigDecimal.ZERO;
	//补充申报量
	@ExcelProperty("补充申报量")
	private BigDecimal bcsbSum=BigDecimal.ZERO;
	//未申报量
	@ExcelProperty("未申报量")
	private BigDecimal wsbSum=BigDecimal.ZERO;
	//已封发量
	@ExcelProperty("已封发量")
	private BigDecimal yffSum=BigDecimal.ZERO;
	//封发率
	@ExcelProperty("封发率")
	private BigDecimal ffRate=BigDecimal.ZERO;
	//逾期封发量
	@ExcelProperty("逾期封发量")
	private BigDecimal yqFfSum=BigDecimal.ZERO;
	//封发平均时长
	@ExcelProperty("平均时长(h)")
	private BigDecimal ffPjsc=BigDecimal.ZERO;
	//未封发量
	@ExcelProperty("未封发量")
	private BigDecimal wffSum=BigDecimal.ZERO;
	//已发运量
	@ExcelProperty("已发运量")
	private BigDecimal fySum=BigDecimal.ZERO;
	//发运率
	@ExcelProperty("发运率")
	private BigDecimal fyRate=BigDecimal.ZERO;
	//逾期发运量
	@ExcelProperty("逾期发运量")
	private BigDecimal rqFySum=BigDecimal.ZERO;
	//发运平均时长
	@ExcelProperty("平均时长(h)")
	private BigDecimal fyPjsc=BigDecimal.ZERO;
	//未发运量
	@ExcelProperty("未发运量")
	private BigDecimal wfySum=BigDecimal.ZERO;
	//时限内
	@ExcelProperty("时限内")
	private BigDecimal sxnSum=BigDecimal.ZERO;
	//时限内发运率
	@ExcelProperty("及时率")
	private BigDecimal sxnRate=BigDecimal.ZERO;
	//逾期时限内量
	@ExcelProperty("逾期邮件量")
	private BigDecimal rqSxnSum=BigDecimal.ZERO;
	//时限内平均时长
	@ExcelProperty("平均时长(h)")
	private BigDecimal sxnPjsc=BigDecimal.ZERO;
	//封发量
	@ExcelProperty("封发量")
	private BigDecimal ttffSum=BigDecimal.ZERO;

	//发运量
	@ExcelProperty("发运量")
	private BigDecimal ttfySum=BigDecimal.ZERO;
	//发运率
	@ExcelProperty("发运率")
	private BigDecimal ttfyRate=BigDecimal.ZERO;
	//未发运量
	@ExcelProperty("未发运量")
	private BigDecimal wttfySum=BigDecimal.ZERO;
	//下一环节接收量
	@ExcelProperty("下一环节接收量")
	private BigDecimal nextSum=BigDecimal.ZERO;
	//下一环节未接收量
	@ExcelProperty("下一环节未接收量")
	private BigDecimal nextNoSum=BigDecimal.ZERO;
	//已投递量
	@ExcelProperty("已投递量")
	private BigDecimal ttSum=BigDecimal.ZERO;
	//未投递量
	@ExcelProperty("未投递量")
	private BigDecimal wttSum=BigDecimal.ZERO;
	//投递率
	@ExcelProperty("投递率")
	private BigDecimal ttRate=BigDecimal.ZERO;
	//统计日期
	@ExcelIgnore
	private Date ptTime;

}
