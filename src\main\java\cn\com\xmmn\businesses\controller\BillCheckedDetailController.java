package cn.com.xmmn.businesses.controller;

import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

import cn.com.xmmn.businesses.domain.BillCheckedDetailDO;
import cn.com.xmmn.businesses.domain.CarrierBillDetailDO;
import cn.com.xmmn.businesses.service.BillCheckedDetailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;


import cn.com.xmmn.common.utils.PageUtils;
import cn.com.xmmn.common.utils.Query;
import cn.com.xmmn.common.utils.R;

import javax.servlet.http.HttpServletResponse;

/**
 * 账单核对结果表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-08 21:58:47
 */
@Slf4j
@Controller
@RequestMapping("/check/billCheckedDetail")
public class BillCheckedDetailController {
	@Autowired
	private BillCheckedDetailService billCheckedDetailService;
	
	@GetMapping()
	@RequiresPermissions("check:billCheckedDetail:billCheckedDetail")
	String BillCheckedDetail(){
	    return "check/billCheckedDetail/billCheckedDetail";
	}
	
	@ResponseBody
	@GetMapping("/list")
	@RequiresPermissions("check:billCheckedDetail:billCheckedDetail")
	public PageUtils list(@RequestParam Map<String, Object> params){
		//查询列表数据
        Query query = new Query(params);
		List<BillCheckedDetailDO> billCheckedDetailList = billCheckedDetailService.list(query);
		int total = billCheckedDetailService.count(query);
		PageUtils pageUtils = new PageUtils(billCheckedDetailList, total);
		return pageUtils;
	}
	@RequestMapping(value = "/exportExcel",method = RequestMethod.GET)
	public void exportExcel(@RequestParam Map<String, Object> map, HttpServletResponse response) throws IOException {
		List<BillCheckedDetailDO> portbatchList = billCheckedDetailService.list(map);//账单
		//创建HSSFWorkbook对象,  excel的文档对象
		HSSFWorkbook wb = new HSSFWorkbook();
		// 创建sheet
		Sheet sheet = wb.createSheet("账单核对汇总表");
		//表头字体
		Font headerFont = wb.createFont();
		headerFont.setFontName("宋体");
		headerFont.setFontHeightInPoints((short) 18);
		headerFont.setBold(true);
		headerFont.setColor(Font.COLOR_NORMAL);
		//正文字体
		Font contextFont = wb.createFont();
		contextFont.setFontName("宋体");
		contextFont.setFontHeightInPoints((short) 12);
		headerFont.setBold(true);
		//表头样式，左右上下居中
		CellStyle headerStyle = wb.createCellStyle();
		headerStyle.setFont(headerFont);
		// 左右居中
		headerStyle.setAlignment(HorizontalAlignment.CENTER);
		// 上下居中
		headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		headerStyle.setLocked(true);
		// 自动换行
		headerStyle.setWrapText(false);
		//单元格样式，右对齐
		CellStyle alignRightStyle = wb.createCellStyle();
		alignRightStyle.setFont(contextFont);
		alignRightStyle.setLocked(true);
		// 自动换行
		alignRightStyle.setWrapText(false);
		alignRightStyle.setAlignment(HorizontalAlignment.RIGHT);
		//单元格样式，左右上下居中 边框
		CellStyle commonStyle = wb.createCellStyle();
		commonStyle.setFont(contextFont);
		// 左右居中
		commonStyle.setAlignment(HorizontalAlignment.CENTER);
		// 上下居中
		commonStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		commonStyle.setLocked(true);
		// 自动换行
		commonStyle.setWrapText(false);
		// 行号
		int rowNum = 0;
		//设置列宽
		for (int i = 0; i < 5; i++) {
			sheet.setColumnWidth(i, 6000);
		}
		//第一行中国快递服务有限公司核账单
		Row r0 = sheet.createRow(rowNum++);
		r0.setHeight((short) 1000);
		Cell c0 = r0.createCell(0);
		c0.setCellValue("中国快递服务有限公司\n" +
				"账单核对汇总表");
		c0.setCellStyle(headerStyle);
		//合并单元格
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 26));
		//第二行 结算周期
		Row r1 = sheet.createRow(rowNum++);
		r1.setHeight((short) 500);
		Cell c1 = r1.createCell(0);
		c1.setCellValue("财务时期：" + map.get("dateStart"));
		c1.setCellStyle(alignRightStyle);
		//合并单元格
		sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 26));
		Row r2 = sheet.createRow(rowNum++);
		r2.setHeight((short) 500);
		String[] rowSecond = { "邮件种类", "承运人名称", "Barcode", "CN38时间", "接收扫描时间", "启运地点", "启运时间", "中转到达地点", "中转到达时间", "中转启运时间", "到达地点", "目的地到达时间", "目的地交邮时间", "收费路由", "航班", "重量", "费率", "金额", "币种", "账务时期", "账单编号", "备注", "运能编码", "箱板类型", "集装器号（板号）",  "核对时间", "核对人姓名"};

		for (int i = 0; i < rowSecond.length; i++) {
			Cell tempCell = r2.createCell(i);
			tempCell.setCellValue(rowSecond[i]);
			tempCell.setCellStyle(commonStyle);
			tempCell.setCellStyle(commonStyle);
		}
		for (int i = 0; i < portbatchList.size(); i++) {
			BillCheckedDetailDO vo= portbatchList.get(i);
			Row tempRow = sheet.createRow(rowNum++);
			tempRow.setRowStyle(commonStyle);
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
			tempRow.createCell(0).setCellValue(vo.getMailType()!= null ? String.valueOf(vo.getMailType()) : "");//邮件种类
			tempRow.createCell(1).setCellValue(vo.getCarrierName()!= null ? String.valueOf(vo.getCarrierName()) : "");//承运人名称
			tempRow.createCell(2).setCellValue(vo.getBarcode()!= null ? String.valueOf(vo.getBarcode()) : "");//Barcode
			tempRow.createCell(3).setCellValue(vo.getCn38time()!= null ? String.valueOf(sdf.format(vo.getCn38time())) : "");//CN38时间
			tempRow.createCell(4).setCellValue(vo.getScanTime()!= null ? String.valueOf(sdf.format(vo.getScanTime())) : "");//接收扫描时间
			tempRow.createCell(5).setCellValue(vo.getStartPlace()!= null ? String.valueOf(vo.getStartPlace()) : "");//启运地点
			tempRow.createCell(6).setCellValue(vo.getStartTime()!= null ? String.valueOf(sdf.format(vo.getStartTime())) : "");//启运时间
			tempRow.createCell(7).setCellValue(vo.getTransArrivePlace()!= null ? String.valueOf(vo.getTransArrivePlace()) : "");//中转到达地点
			tempRow.createCell(8).setCellValue(vo.getTransArriveTime()!= null ? String.valueOf(sdf.format(vo.getTransArriveTime())) : "");//中转到达时间
			tempRow.createCell(9).setCellValue(vo.getTransStartTime()!= null ? String.valueOf(vo.getTransStartTime()) : "");//中转启运时间
			tempRow.createCell(10).setCellValue(vo.getArrivePlace()!= null ? String.valueOf(vo.getArrivePlace()) : "");//到达地点
			tempRow.createCell(11).setCellValue(vo.getArriveTime()!= null ? String.valueOf(sdf.format(vo.getArriveTime())) : "");//目的地到达时间
			tempRow.createCell(12).setCellValue(vo.getHoldTime()!= null ? String.valueOf(sdf.format(vo.getHoldTime())) : "");//目的地交邮时间
			tempRow.createCell(13).setCellValue(vo.getTollRoute()!= null ? String.valueOf(vo.getTollRoute()) : "");//收费路由
			tempRow.createCell(14).setCellValue(vo.getFlightNum()!= null ? String.valueOf(vo.getFlightNum()) : "");//航班
			tempRow.createCell(15).setCellValue(String.valueOf(vo.getWeight())!= null ? String.valueOf(vo.getWeight()) : "");//重量
			tempRow.createCell(16).setCellValue(String.valueOf(vo.getFeeRate())!= null ? String.valueOf(vo.getFeeRate()) : "");//费率
			tempRow.createCell(17).setCellValue(String.valueOf(vo.getAmount())!= null ? String.valueOf(vo.getAmount()) : "");//金额
			tempRow.createCell(18).setCellValue(vo.getCurrency()!= null ? String.valueOf(vo.getCurrency()) : "");//币种
			tempRow.createCell(19).setCellValue(vo.getAccountPeriod()!= null ? String.valueOf(vo.getAccountPeriod()) : "");//账务时期
			tempRow.createCell(20).setCellValue(vo.getBillNo()!= null ? String.valueOf(vo.getBillNo()) : "");//账单编号
			tempRow.createCell(21).setCellValue(vo.getRemark()!= null ? String.valueOf(vo.getRemark()) : "");//备注
			tempRow.createCell(22).setCellValue(vo.getCapacityCode()!= null ? String.valueOf(vo.getCapacityCode()) : "");//运能编码
			tempRow.createCell(23).setCellValue(vo.getCaseType()!= null ? String.valueOf(vo.getCaseType()) : "");//箱板类型
			tempRow.createCell(24).setCellValue(vo.getPackNum()!= null ? String.valueOf(vo.getPackNum()) : "");//集装器号（板号）
			tempRow.createCell(25).setCellValue(vo.getCheckTime()!= null ? String.valueOf(sdf.format(vo.getCheckTime())) : "");//核对时间
			tempRow.createCell(26).setCellValue(vo.getCheckPersonName()!= null ? String.valueOf(vo.getCheckPersonName()) : "");//核对人姓名
		}
		//导出
		response.setContentType("application/vnd.ms-excel");
		response.setCharacterEncoding("UTF-8");
		// 文件名
		String fileName =  map.get("dateStart")  + "中国快递服务有限公司账单核对汇总表" + ".xlsx";
		try {
			fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
		OutputStream stream = null;
		try {
			stream = response.getOutputStream();
		} catch (IOException e) {
			e.printStackTrace();
		}
		try {
			if (null != wb && null != stream) {
				wb.write(stream);
				stream.close();
			}
		} catch (Exception e) {
			log.info("excel文档导出错误-异常信息：" + e);
		}
	}
	/*@GetMapping("/add")
	@RequiresPermissions("system:billCheckedDetail:add")
	String add(){
	    return "system/billCheckedDetail/add";
	}

	@GetMapping("/edit/{id}")
	@RequiresPermissions("system:billCheckedDetail:edit")
	String edit(@PathVariable("id") Integer id,Model model){
		BillCheckedDetailDO billCheckedDetail = billCheckedDetailService.get(id);
		model.addAttribute("billCheckedDetail", billCheckedDetail);
	    return "system/billCheckedDetail/edit";
	}
	
	*//**
	 * 保存
	 *//*
	@ResponseBody
	@PostMapping("/save")
	@RequiresPermissions("system:billCheckedDetail:add")
	public R save( BillCheckedDetailDO billCheckedDetail){
		if(billCheckedDetailService.save(billCheckedDetail)>0){
			return R.ok();
		}
		return R.error();
	}
	*//**
	 * 修改
	 *//*
	@ResponseBody
	@RequestMapping("/update")
	@RequiresPermissions("system:billCheckedDetail:edit")
	public R update( BillCheckedDetailDO billCheckedDetail){
		billCheckedDetailService.update(billCheckedDetail);
		return R.ok();
	}
	
	*//**
	 * 删除
	 *//*
	@PostMapping( "/remove")
	@ResponseBody
	@RequiresPermissions("system:billCheckedDetail:remove")
	public R remove( Integer id){
		if(billCheckedDetailService.remove(id)>0){
		return R.ok();
		}
		return R.error();
	}
	
	*//**
	 * 删除
	 *//*
	@PostMapping( "/batchRemove")
	@ResponseBody
	@RequiresPermissions("system:billCheckedDetail:batchRemove")
	public R remove(@RequestParam("ids[]") Integer[] ids){
		billCheckedDetailService.batchRemove(ids);
		return R.ok();
	}*/
	
}
