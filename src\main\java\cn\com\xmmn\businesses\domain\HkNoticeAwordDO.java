package cn.com.xmmn.businesses.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 香港成交通知单接收表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-31
 */
@Data
public class HkNoticeAwordDO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    // ID
    private Integer id;
    // 运能编码
    private String capacityCode;
    // 特快价格
    private BigDecimal emsPrice;
    // 邮宝价格
    private BigDecimal epacketPrice;
    // 航空函件价格
    private BigDecimal airLettersPrice;
    // 航空包裹价格
    private BigDecimal airParcelsPrice;
    // 空运水路邮件价格
    private BigDecimal salPrice;
    // 生效日期
    private Date effectiveDate;
    // 失效日期
    private Date expiryDate;
    // 币种
    private String currency;
    // 创建时间
    private Date createTime;
    // 更新时间
    private Date updateTime;
}
