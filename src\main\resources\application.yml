title: 经营分析数据管理系统
server:
  tomcat:
    max-http-form-post-size: -1
  session-timeout: 14400
  port: 8090
  connection-timeout: 120
spring:
  thymeleaf:
    mode: LEGACYHTML5
    cache: false
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  profiles: 
    active: pro
  servlet:
    multipart:
      enabled: true
      max-file-size: 30Mb
      max-request-size: 30Mb
  devtools:
    restart:
      enabled: true
  cache:
    type: ehcache
    ehcache:
      config: classpath:config/ehcache.xml

# 香港邮政成交通知单配置
hk-notice:
  api-url: https://api.hongkongpost.hk
  secret-key: your-secret-key-here
  timeout: 30000
  ssl-enabled: true
mybatis:
  configuration:
    map-underscore-to-camel-case: true
  mapper-locations: mybatis/**/*Mapper.xml
  typeAliasesPackage: cn.com.xmmn.**.domain
#[弃用]配置缓存和session存储方式，默认ehcache,可选redis,[弃用]调整至 spring cache type【shiro.用户，权限，session，spring.cache通用】
#[弃用]cacheType: ehcache

#mybatis-plus配置
mybatis-plus:
  mapper-locations: classpath:mybatis/**/*Mapper.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: cn.com.xmmn.**.pojo
  global-config:
    # 数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID",ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: INPUT
      #字段策略 IGNORED:"忽略判断",NOT_NULL:"非 NULL 判断"),NOT_EMPTY:"非空判断"
      field-strategy: not_empty
      #驼峰下划线转换
      column-underline: true
      #数据库大写下划线转换
      #capital-mode: true
      #逻辑删除配置
      logic-delete-value: 0
      logic-not-delete-value: 1
      db-type: oracle
    #刷新mapper 调试神器
    refresh: true
  # 原生配置
  configuration:
    map-underscore-to-camel-case: true
    jdbcTypeForNull: 'null' # 解决oracle更新数据为null时无法转换报错
    cache-enabled: false
  logging:
    level: warn
#springboot 异步线程池配置
asyncTask:
  pool:
    coreSize: 5 #核心线程池数量
    maxSize: 10 #最大线程池数量
    keepAliveSeconds: 60 #线程池空闲存活时间，单位秒
    queueCapacity: 1000 #线程池任务容量
    allowCoreThreadTimeOut: true #是否允许超时
    awaitTerminationSeconds: 60 #超时时间，单位秒

