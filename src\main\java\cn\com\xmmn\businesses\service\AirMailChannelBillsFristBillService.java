package cn.com.xmmn.businesses.service;


import cn.com.xmmn.businesses.domain.AirMailChannelBillsFristBillDO;

import java.util.List;
import java.util.Map;

/**
 * 邮件空运渠道账单第一邮通账单
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-22 17:37:38
 */
public interface AirMailChannelBillsFristBillService {
	
	AirMailChannelBillsFristBillDO get(Integer id);
	
	List<AirMailChannelBillsFristBillDO> list(Map<String, Object> map);
	
	int count(Map<String, Object> map);
	
	int save(AirMailChannelBillsFristBillDO airMailChannelBillsFristBill);
	
	int update(AirMailChannelBillsFristBillDO airMailChannelBillsFristBill);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);

	List detail(AirMailChannelBillsFristBillDO airMailChannelBillsFristBill);

	//查询费用数据
	List<Map<String,Object>> feeList(Map<String,Object> map);

	//查询hongkong
//	List<String> hongkongList(Map<String,Object> map);
	List<Map<String,Object>> portList(Map<String,Object> map);

	//查询口岸发运量占比-饼图
	List<Map<String,Object>> portPercent(Map<String,Object> map);

	//查询按路向统计接收邮件总重
	List<Map<String,Object>> routeList(Map<String,Object> map);

	//查询按路向统计接收邮件总重右侧的环比
	List<Map<String,Object>> huanbiList(Map<String,Object> map);
}
