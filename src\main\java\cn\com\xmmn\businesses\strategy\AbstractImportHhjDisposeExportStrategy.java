package cn.com.xmmn.businesses.strategy;

import cn.com.xmmn.businesses.service.ImportMailService;
import cn.com.xmmn.businesses.vo.ImportMailShouldListVO;
import cn.com.xmmn.common.enums.ImportHhjDisposeExportNameEnum;
import cn.com.xmmn.common.utils.Convert;
import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

/**
 * 通用导出的抽象类
 */
@Component
@Slf4j
public abstract class AbstractImportHhjDisposeExportStrategy implements ImportHhjDisposeExportStrategy{



    // 在子类中实现此方法来执行特定的导出
    public abstract void exportExcel(Map<String, Object> map,HttpServletResponse response);

    /**
     * 将原始数据转换为特定的 ExportVO
     */
    protected abstract List<?> convertToExportVO(List<ImportMailShouldListVO> list);

    /**
     * 获取导出的 VO 类型
     */
    protected abstract Class<?> getExportVOClass();

    /**
     * 获取 sheet 名称
     */
    protected abstract String getSheetName(Map<String, Object> map);

    /**
     * 设置响应头
     */
    protected void setupResponseHeaders(HttpServletResponse response, Map<String, Object> map, List<?> maillExportVOS) {
        // 设置响应内容类型
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");

        // 获取导出类型
        String pageHandled = Convert.toStr(map.get("pageHandled"));

        String ptTimeStart = Convert.toStr(map.get("ptTimeStart"));
        String ptTimeEnd = Convert.toStr(map.get("ptTimeEnd"));

        // 文件名
        String fileName =ptTimeStart+"至"+ptTimeEnd+ ImportHhjDisposeExportNameEnum.getName(pageHandled) + ".xlsx";
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error("文件名编码转换失败", e);
        }
        // 设置文件下载响应头
        response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
    }
}
