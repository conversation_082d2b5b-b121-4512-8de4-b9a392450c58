package cn.com.xmmn.businesses.controller;

import cn.com.xmmn.businesses.domain.FeedbackRecordsDO;
import cn.com.xmmn.businesses.domain.ImportClearanceEfficiencyDO;
import cn.com.xmmn.businesses.dt.ImportHhjDisposeExcelDT;
import cn.com.xmmn.businesses.service.ImportFullTimeService;
import cn.com.xmmn.businesses.vo.ImportFullTimeListExportVO;
import cn.com.xmmn.businesses.vo.ImportFullTimeListVO;
import cn.com.xmmn.common.controller.BaseController;
import cn.com.xmmn.common.strategy.ImportFullTimeExcelStrategy;
import cn.com.xmmn.common.strategy.ImportHhjDisposeExcelStrategy;
import cn.com.xmmn.common.utils.Convert;
import cn.com.xmmn.common.utils.PageUtils;
import cn.com.xmmn.common.utils.Query;
import com.alibaba.excel.EasyExcel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 进口全程时限监控统计控制器
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-20 15:02:00
 */
@Controller
@RequestMapping("/import/importFullTime")
@Slf4j
@RequiredArgsConstructor
public class ImportFullTimeController extends BaseController {

    private final ImportFullTimeService importFullTimeService;


    @GetMapping()
    @RequiresPermissions("importFullTime:importFullTime")
    String importFullTime() {
        return "importFullTime/list";
    }

    @ResponseBody
    @GetMapping("/list")
    @RequiresPermissions("importFullTime:importFullTime")
    public PageUtils list(@RequestParam Map<String, Object> params) {
        //查询列表数据
        Query query = new Query(params);
        List<ImportFullTimeListVO> importHhjDisposeList = importFullTimeService.list(query);
        int total = importFullTimeService.count(query);
        PageUtils pageUtils = new PageUtils(importHhjDisposeList, total);
        return pageUtils;
    }

    /**
     * 导出数据
     *
     * @param map
     * @param response 入参
     * @return void
     **/
    @RequestMapping(value = "/exportExcel", method = RequestMethod.GET)
    public void exportExcel(@RequestParam Map<String, Object> map, HttpServletResponse response) throws IOException {

        List<ImportFullTimeListVO> hkCarrierDealerList = importFullTimeService.list(map);

        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");
        // 文件名
        //String fileName = map.get("dateStart") + "进口全程时限监控统计表" + ".xlsx";
        String fileName ="进口全程时限监控统计表" + ".xlsx";
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8");
           // fileName = new String(fileName.getBytes("UTF-8"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");

        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("进口全程时限监控统计表.xlsx");

        // 设置表头
        ImportFullTimeExcelStrategy.createHeader(sheet);
       // 填充数据行
        ImportFullTimeExcelStrategy.fillDataRows(sheet, hkCarrierDealerList);

        // 将 Excel 文件写入响应输出流
        try (OutputStream out = response.getOutputStream()) {
            workbook.write(out);
        }
    }



}
