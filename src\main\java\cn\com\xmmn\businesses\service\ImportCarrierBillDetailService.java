package cn.com.xmmn.businesses.service;

import cn.com.xmmn.businesses.domain.ImportCarrierBillDetailDO;
import cn.com.xmmn.businesses.domain.NewDO;
import org.springframework.web.multipart.MultipartFile;


import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 承运商账单明细
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-27 15:37:47
 */
public interface ImportCarrierBillDetailService {

	ImportCarrierBillDetailDO get(Integer id);
	
	List<ImportCarrierBillDetailDO> list(Map<String, Object> map);
	
	int count(Map<String, Object> map);

	int countRow(String barcodes);

	int countHis(String barcodes);
	
	int save(ImportCarrierBillDetailDO ImportcarrierBillDetail);
	
	int update(ImportCarrierBillDetailDO ImportcarrierBillDetail);
	
	int remove(Integer id);
	
	int batchRemove(Map<String, Object> map);

	int exportNew (ImportCarrierBillDetailDO importCarrierBillDetailDO);


}
