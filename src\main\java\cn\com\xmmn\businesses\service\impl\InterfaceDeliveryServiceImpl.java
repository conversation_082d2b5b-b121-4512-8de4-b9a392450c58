package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.config.DecryptKeyConfig;
import cn.com.xmmn.businesses.domain.OrderDO;
import cn.com.xmmn.businesses.domain.OrderDeliveryDO;
import cn.com.xmmn.businesses.domain.OrdersImportDO;
import cn.com.xmmn.businesses.domain.OrdersTmsDO;
import cn.com.xmmn.businesses.service.InterfaceDeliveryService;
import cn.com.xmmn.businesses.service.OrderDeliveryService;
import cn.com.xmmn.businesses.service.OrdersImportService;
import cn.com.xmmn.businesses.service.OrdersTmsService;
import cn.com.xmmn.businesses.utils.HttpClientUtil;
import cn.com.xmmn.businesses.config.InterfaceConfig;
import cn.com.xmmn.common.utils.EncryptUtils;
import cn.com.xmmn.common.utils.UnicodeUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Slf4j
@Service
public class InterfaceDeliveryServiceImpl implements InterfaceDeliveryService {

    @Autowired
    private OrderDeliveryService orderDeliveryService;

    @Autowired
    private DecryptKeyConfig decryptKeyConfig;

    @Autowired
    private InterfaceConfig interfaceConfig;


    //调用外部接口获取token
    public String getToken(String url) {
        String result = null;
        try {
            result = HttpClientUtil.get(url);
            log.info("调用外部接口获取token结果：" + result);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return result;
    }

    /***********************获取投递订单信息接口开始*****************************/
    //获取投递订单信息
    @Override
    @Transactional
    public String getDeliverOrder(String start, String end) {
        String res = "";
//        String getTokenUrl = "http://of.elinkwms.com:6008/openApi/search/getToken";
        String getTokenUrl = interfaceConfig.getDeliveryPath()+"/openApi/search/getToken";
//        String url = "http://of.elinkwms.com:6008/openApi/search/listParcel";
        String url = interfaceConfig.getDeliveryPath()+"/openApi/search/listParcel";
//        String key = "jkl;POIU1234++==";//解密钥匙
        String key = decryptKeyConfig.getDeliveryKey();//解密钥匙
        Map<String, Object> req = new HashMap();
        Map<String, String> params = new HashMap();//参数map里面的map
        log.info("开始整理请求参数");
        params.put("searchType", "1");
        params.put("receiveTimeStart", start);
        params.put("receiveTimeEnd", end);
        String token = getToken(getTokenUrl);
        if ("invalid ip\r\n".equals(token)) {
            log.info("invalid ip");
            return token;
        }
        try {
            String content;
            content = HttpClientUtil.doPost(url, params, token);
            log.info("调用外部接口返回结果主体信息：" + content);
            //返回的字符串转Map
            Map<String, Object> jsonMap = JSON.parseObject(content, new TypeReference<HashMap<String, Object>>() {
            });
//            log.info("返回结果转为map后的信息：" + jsonMap.toString());
            String data = jsonMap.get("data").toString();//返回结果集data里的内容是个List<Map<String, Object>>
            Boolean status = (Boolean) jsonMap.get("status");//返回状态码
            if(!status){
                res = jsonMap.get("errorMsg").toString();
            }
//            log.info("返回结果的data主体内容==>：" + data);
//            String data = "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.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";
            String decrypted = EncryptUtils.aes128ECBPkcs7PaddingDecrypt(data, EncryptUtils.paddingKeys(key));
//            log.info("解密后数据: " + decrypted);
            //String 转 List<OrderDO>
            List<OrderDeliveryDO> list = JSONArray.parseArray(decrypted, OrderDeliveryDO.class);
//            log.info("数据转list后: " + list.toString());
            for (OrderDeliveryDO oderDo : list) {
//                OrderDO oderDo = JSONObject.parseObject(data.toString(), OrderDO.class);//String 转 ReportDO对象
                insertDelivery(oderDo);
//                log.info("开始落表orders_delivery==>");
            }
        } catch (Exception e) {
            log.error("调用外部接口获取投递订单信息失败,url:" + url + ",参数：" + req, e);
            log.error(e.getMessage());
            throw new RuntimeException("POST调用异常", e);
        }
        return res;
    }

    //落表orders_delivery
    public void insertDelivery(OrderDeliveryDO vo) {
        OrderDeliveryDO dto = new OrderDeliveryDO();
        dto.setCreateTime(new Date());
        dto.setTrackingNo(vo.getTrackingNo());//订单号
        dto.setExportPortCode(vo.getExportPortCode());//出口局编号
        dto.setExportPortCodeName(vo.getExportPortCodeName());//出口局名称【深圳，广州等等】
        dto.setAddressType(vo.getAddressType());//邮件类型【工商件 或者 住宅件】
        dto.setReceivedTime(vo.getReceivedTime());//抵港时间
        dto.setFeeWeight(vo.getFeeWeight());//计费重量
        dto.setDeliverySiteNo(vo.getDeliverySiteNo());//投递site编号
        dto.setSuccessDeliveryTime(vo.getSuccessDeliveryTime());//成功投递时间
        dto.setStatus(vo.getStatus());//状态
        orderDeliveryService.save(dto);
    }

    /***********************获取投递订单信息接口结束*****************************/


}
