package cn.com.xmmn.businesses.controller;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.*;

import cn.com.xmmn.businesses.domain.CarrierDealerRecivDO;
import cn.com.xmmn.businesses.domain.ImportCarrierBillDetailDO;
import cn.com.xmmn.businesses.service.ImportCarrierBillDetailService;
import cn.com.xmmn.businesses.service.impl.ImportCarrierBillDetailServiceImpl;
import cn.com.xmmn.businesses.vo.CarrierBillDetailListVo;
import cn.com.xmmn.businesses.vo.CarrierDealerArriveToPostDetailListVo;
import cn.com.xmmn.businesses.vo.CarrierDealerD2AListVo;
import cn.com.xmmn.common.utils.R;
import com.alibaba.excel.EasyExcel;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;


import cn.com.xmmn.common.utils.PageUtils;
import cn.com.xmmn.common.utils.Query;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;


/**
 * 承运商账单明细
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-27 15:37:47
 */
 
@Controller
@RequestMapping("/check/importCarrierBillDetail")
public class ImportCarrierBillDetailController {
	@Autowired
	private ImportCarrierBillDetailService importCarrierBillDetailService;
	@Autowired
	private ImportCarrierBillDetailServiceImpl importCarrierBillDetailServiceImpl;
	@GetMapping()
	@RequiresPermissions("check:importCarrierBillDetail:importCarrierBillDetail")
	String CarrierBillDetail(Model model){

		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.DATE, 1);// 设为当前月的1号
		calendar.add(Calendar.MONTH, 0);// 0表示当前月，-2就是当前月-2
		Date d = calendar.getTime();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
		String startDate = sdf.format(d);
		model.addAttribute("dateStart", startDate);
		return "check/importCarrierBillDetail/importCarrierBillDetail";
	}
	
	@ResponseBody
	@GetMapping("/list")
	@RequiresPermissions("check:importCarrierBillDetail:importCarrierBillDetail")
	public PageUtils list(@RequestParam Map<String, Object> params){
		//查询列表数据
        Query query = new Query(params);
		List<ImportCarrierBillDetailDO> importCarrierBillDetailList = importCarrierBillDetailService.list(query);
		int total = importCarrierBillDetailService.count(query);
		PageUtils pageUtils = new PageUtils(importCarrierBillDetailList, total);
		return pageUtils;
	}

	//导入
	/**
	 *   接受文件   解析  上传资料。
	 *   /admin/client/upload_excel
	 */
	@PostMapping("/uploadExcel")
	public String uploadExcel(MultipartFile file, String modelType, Model model) {
		List list = null;
		try {
			// 验证文件类型
			if (file == null || file.isEmpty()) {
				throw new Exception("上传文件不能为空");
			}
			
			String originalFilename = file.getOriginalFilename();
			if (originalFilename == null || originalFilename.trim().isEmpty()) {
				throw new Exception("文件名不能为空");
			}
			
			// 检查文件扩展名是否为CSV或Excel
			String extension = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase();
			if (!("csv".equals(extension) || "xlsx".equals(extension) || "xls".equals(extension))) {
				throw new Exception("仅支持CSV或Excel文件格式");
			}
			
			// 使用安全的导入方法
			if ("csv".equals(extension)) {
				list = importCarrierBillDetailServiceImpl.importCsv(file, modelType);
			} else {
				list = importCarrierBillDetailServiceImpl.NewExcel(file, modelType);
			}
		} catch (Exception e) {
			e.printStackTrace();
			String msg = "导入失败: " + e.getMessage(); 
			model.addAttribute("message", msg); // 错误信息
			return "check/importCarrierBillDetail/importCarrierBillDetail";
		}
		model.addAttribute("msg", list);

		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.DATE, 1);// 设为当前月的1号
		calendar.add(Calendar.MONTH, 0);// 0表示当前月，-2就是当前月-2
		Date d = calendar.getTime();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
		String startDate = sdf.format(d);
		model.addAttribute("dateStart", startDate);
		return "check/importCarrierBillDetail/importCarrierBillDetail";
	}


     /**
	 * 下载excel模板
	 */
	@GetMapping("/downloadExcel")
	@ResponseBody
	@RequiresPermissions("check:importCarrierBillDetail:downloadExcel")
	public void downloadExcel(HttpServletResponse response) throws UnsupportedEncodingException {
		importCarrierBillDetailServiceImpl.downloadCsv(response);
	}
	/**
	 * 删除
	 */
	@PostMapping( "/importRemove")
	@ResponseBody
	@RequiresPermissions("check:importCarrierBillDetail:importRemove")
	public R remove(@RequestParam Map<String, Object> params){
		importCarrierBillDetailServiceImpl.batchRemove(params);
		return R.ok();
	}

	/**
	 * 导出数据
	 *
	 * @param map
	 * @param response 入参
	 * @return void
	 **/
	@RequestMapping(value = "/exportExcel", method = RequestMethod.GET)
	@RequiresPermissions("check:importCarrierBillDetail:exportExcel")
	public void exportExcel(@RequestParam Map<String, Object> map, HttpServletResponse response) throws IOException {
		// 参数验证与安全处理
		String dateStart = (String) map.get("dateStart");
		if (dateStart != null) {
			// 验证日期格式是否正确，例如使用正则表达式验证是否为yyyyMM格式
			if (!dateStart.matches("^\\d{6}$")) {
				response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
				response.getWriter().write("日期格式无效");
				return;
			}
		}
		try {
			List<ImportCarrierBillDetailDO> portbatchList = importCarrierBillDetailService.list(map);

			// 设置响应头
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding("UTF-8");
			
			// 安全地构造文件名
			String fileName = (dateStart != null ? dateStart : "unknown") + "账单导入结果" + ".xlsx";
			response.setHeader("Content-disposition", "attachment;filename=\"" + 
				new String(fileName.getBytes("UTF-8"), "ISO8859-1") + "\"");
			
			// 导出数据
			List<CarrierBillDetailListVo> exportList = new ArrayList<>();
			for (ImportCarrierBillDetailDO portbatch : portbatchList) {
				CarrierBillDetailListVo vo = new CarrierBillDetailListVo();
				BeanUtils.copyProperties(portbatch, vo);
				exportList.add(vo);
			}
			
			// 导出
			EasyExcel.write(response.getOutputStream(), CarrierBillDetailListVo.class)
					.sheet("账单导入结果")
					.doWrite(exportList);
		} catch (Exception e) {
			// 记录异常但不暴露详细信息给客户端
			e.printStackTrace();
			response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
			response.getWriter().write("导出失败");
		}
	}

}
