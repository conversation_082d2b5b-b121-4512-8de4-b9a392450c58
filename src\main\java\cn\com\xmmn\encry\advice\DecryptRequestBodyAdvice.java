//package cn.com.xmmn.encry.advice;
//
//import cn.com.xmmn.businesses.utils.AESUtils;
//import com.alibaba.fastjson.JSON;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.core.MethodParameter;
//import org.springframework.http.HttpInputMessage;
//import org.springframework.http.converter.HttpMessageConverter;
//import org.springframework.lang.Nullable;
//import org.springframework.stereotype.Component;
//import org.springframework.web.bind.annotation.ControllerAdvice;
//import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdvice;
//
//import java.io.IOException;
//import java.lang.reflect.Type;
//import java.util.Map;
//
//
///**
// * 解密
// * <AUTHOR>
// * @email <EMAIL>
// * @date 2024年6月14日15:15:15
// */
//@Component
//@ControllerAdvice(basePackages = "cn.com.xmmn.businesses.controller.api")
//@Slf4j
//public class DecryptRequestBodyAdvice implements RequestBodyAdvice {
//
//    @Override
//    public boolean supports(MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
//        return false;
//    }
//
//    @Override
//    public HttpInputMessage beforeBodyRead(HttpInputMessage httpInputMessage, MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) throws IOException {
//        return null;
//    }
//
//    @Override
//    public Object afterBodyRead(Object body, HttpInputMessage httpInputMessage, MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
//        String dealData = null;
//        try {
//            //解密操作
//            Map<String,String> dataMap = (Map)body;
//            log.info("接收到原始请求数据={}", JSON.toJSONString(dataMap));
//            String srcData = dataMap.get("data");
//            dealData= AESUtils.decryptByBytes(srcData);
//            log.info("解密后数据={}",dealData);
//        } catch (Exception e) {
//            log.error("异常！", e);
//        }
//        return dealData;
//    }
//
//    @Override
//    public Object handleEmptyBody(Object o, HttpInputMessage httpInputMessage, MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
//        return null;
//    }
//
//    public static void main(String[] args) throws Exception {
//        String s="G5UML/uuONCukRIeGhtrVZ+YQ3hqaFg9lBl0CpkcyYg=";
//        System.out.println( AESUtils.decryptByBytes(s));
//    }
//}
