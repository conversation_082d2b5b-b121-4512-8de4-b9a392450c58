package cn.com.xmmn.businesses.domain;

import java.io.Serializable;
import java.util.Date;



/**
 * 进口轨迹表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-04 10:28:53
 */
public class ImportMailTraceDO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//ID;批次号
	private Integer id;
	//单号
	private String traceNo;
	//操作时间
	private Date opTime;
	//操作码
	private String opCode;
	//操作名
	private String opName;
	//操作网点编码
	private String opOrgCode;
	//操作网点名称
	private String opOrgName;
	//投递备注
	private String notes;
	//业务时间
	private String pt;

	/**
	 * 设置：ID;批次号
	 */
	public void setId(Integer id) {
		this.id = id;
	}
	/**
	 * 获取：ID;批次号
	 */
	public Integer getId() {
		return id;
	}
	/**
	 * 设置：单号
	 */
	public void setTraceNo(String traceNo) {
		this.traceNo = traceNo;
	}
	/**
	 * 获取：单号
	 */
	public String getTraceNo() {
		return traceNo;
	}
	/**
	 * 设置：操作时间
	 */
	public void setOpTime(Date opTime) {
		this.opTime = opTime;
	}
	/**
	 * 获取：操作时间
	 */
	public Date getOpTime() {
		return opTime;
	}
	/**
	 * 设置：操作码
	 */
	public void setOpCode(String opCode) {
		this.opCode = opCode;
	}
	/**
	 * 获取：操作码
	 */
	public String getOpCode() {
		return opCode;
	}
	/**
	 * 设置：操作名
	 */
	public void setOpName(String opName) {
		this.opName = opName;
	}
	/**
	 * 获取：操作名
	 */
	public String getOpName() {
		return opName;
	}
	/**
	 * 设置：操作网点编码
	 */
	public void setOpOrgCode(String opOrgCode) {
		this.opOrgCode = opOrgCode;
	}
	/**
	 * 获取：操作网点编码
	 */
	public String getOpOrgCode() {
		return opOrgCode;
	}
	/**
	 * 设置：操作网点名称
	 */
	public void setOpOrgName(String opOrgName) {
		this.opOrgName = opOrgName;
	}
	/**
	 * 获取：操作网点名称
	 */
	public String getOpOrgName() {
		return opOrgName;
	}
	/**
	 * 设置：投递备注
	 */
	public void setNotes(String notes) {
		this.notes = notes;
	}
	/**
	 * 获取：投递备注
	 */
	public String getNotes() {
		return notes;
	}
	/**
	 * 设置：业务时间
	 */
	public void setPt(String pt) {
		this.pt = pt;
	}
	/**
	 * 获取：业务时间
	 */
	public String getPt() {
		return pt;
	}
}
