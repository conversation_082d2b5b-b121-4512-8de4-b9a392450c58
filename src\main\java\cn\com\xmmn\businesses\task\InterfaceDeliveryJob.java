package cn.com.xmmn.businesses.task;

import cn.com.xmmn.businesses.config.InterfaceConfig;
import cn.com.xmmn.businesses.service.InterfaceDeliveryService;
import cn.com.xmmn.common.utils.GetTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @author: zxh
 * @since: 2023-01-29 14:41
 * @description:
 */
@Component
@Slf4j
public class InterfaceDeliveryJob implements Job {

    @Autowired
    InterfaceDeliveryService interfaceDeliveryService;

    /**
     * 单线程收发
     */
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.info("警告！执行线程！！");
    }

    @Scheduled(cron = "0 0 1 * * ?")//每天1:00
    private void getOrderTasks() {
        log.info("======投递系统订单获取接口执行开始时间" + new Date());
        //TODO:接口调用
        interfaceDeliveryService.getDeliverOrder(GetTimeUtils.getYesterdayStartTime(),GetTimeUtils.getYesterdayEndTime());
        log.info("======投递系统订单获取接口结束时间" + new Date());
    }
}
