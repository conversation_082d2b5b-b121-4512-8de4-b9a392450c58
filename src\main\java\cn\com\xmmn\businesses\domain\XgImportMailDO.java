package cn.com.xmmn.businesses.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;



/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-06 10:52:58
 */
@Data
@ApiModel(value = "香港进口邮件表",description = "")
@TableName(value = "tb_xg_import_mail")
public class XgImportMailDO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//
	private String id;
	//
	private String itemId;
	//
	private String declareComp;
	//
	private Date opTime;
	//
	private String custDeclareClassCode;
	//
	private Date cusTime;
	//
	private String originOrgName;
	//
	private String destOrgName;
	//
	private String custCode;
	//
	private String senderName;
	//
	private String senderPhone;
	//
	private String senderAddr;
	//
	private String recName;
	//
	private String recipientAddress;
	//
	private String recAddress;
	//
	private BigDecimal totalWeight;
	//
	private BigDecimal postage;
	//
	private BigDecimal declareTotalValue;
	//
	private BigDecimal taxTotalValue;
	//
	private String remarkTxt;
	//
	private Date replyTime;
	//
	private String replyNotes;
	//
	private String replyState;
	//
	private Date pt;
	//
	private Date contractTime;
	//
	private Date releaseTime;
	//
	private Date taxTime;
	//
	private Date checkTime;
	//
	private Date declarationTime;
	//
	private Date customsTime;
	//
	private Date reviewTime;
	//
	private String revision;
	//
	private String createUserId;
	//
	private String createDeptId;
	//
	private String createUserName;
	//
	private String createDeptName;
	//
	private Date createTime;
	//
	private String updateUserId;
	//
	private String updateUserName;
	//
	private String updateDeptId;
	//
	private String updateDeptName;
	//
	private Date updateTime;
	//
	private String brokerCode;
	//
	private Date chOrgDisTime;
	//
	private Date chOrgSendTime;
	//
	private Date processRecvTime;
	//
	private Date deliverRecvTime;
	//
	private Date chOrgOpenTime;
	//
	private Date waitTestTime;
	//
	private Date waitOpenTime;
	//
	private Date disBrokerTime;
	//
	private Date brokerTime;
	//
	private Date brokerOutTime;
	//
	private Date brokerName;
	//
	private Date custSubmitTime;
	//
	private Date custTaxTime;
	//
	private String ifCustTax;
	//
	private Date deliverTime;
	//
	private String processOrgCode;
	//
	private String processOrgName;
	//
	private String deliverOrgCode;
	//
	private String deliverOrgName;
	//
	private Date recvOrgSendTime;
	//
	private Date chOrgRecvTime;
	//
	private String recvOrgName;
	//
	private String chOrgName;
	//
	private String recvOrgCode;
	//
	private String chOrgCode;
	//
	private Date recvOrgTime;
	//总包条码
	private String barCode;


}
