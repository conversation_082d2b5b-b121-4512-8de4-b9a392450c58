package cn.com.xmmn.businesses.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;



/**
 * 承运商账单操作日志
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-27 17:15:37
 */
@Data
@TableName("t_carrier_bill_log")
public class CarrierBillLogDO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//ID
	private Integer id;
	//承运人名称
	private String carrierName;
	//账务时期
	private String accountPeriod;
	//操作动作
	private String operType;
	//操作人
	private String createdBy;
	//操作时间
	private Date createdTime;


}
