<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.xmmn.businesses.dao.HkCarrierDealerDao">

	<select id="get" resultType="cn.com.xmmn.businesses.domain.HkCarrierDealerDO">
		select `id`,`bag_barcode`,`deliver_bill_no`,`carrier_name`,`container_numbers`,`receice_datetime`,`departure_datetime`,`create_time`,return_Datetime from t_hk_carrier_dealer where id = #{value}
	</select>

	<select id="list" resultType="cn.com.xmmn.businesses.domain.HkCarrierDealerDO">
		select `id`,`bag_barcode`,`deliver_bill_no`,`carrier_name`,`container_numbers`,`receice_datetime`,`departure_datetime`,`create_time`,carrier_Dealer,return_Datetime from t_hk_carrier_dealer
        <where>
			<if test="bagBarcode != null and bagBarcode != ''">
				and bag_barcode like concat('%', #{bagBarcode} ,'%')
			</if>
			<if test="deliverBillNo != null and deliverBillNo != ''">
				and deliver_bill_no like concat('%', #{deliverBillNo}  ,'%')
			</if>
			<if test="carrierName != null and carrierName != ''">
				and carrier_name like concat('%', #{carrierName}  ,'%')
			</if>
			<if test="containerNumbers != null and containerNumbers != ''">
				and container_numbers like concat('%',#{containerNumbers} ,'%')
			</if>
			<if test="receiceDatetimeStart != '' and receiceDatetimeEnd != ''">
				AND receice_datetime >= CONCAT(#{receiceDatetimeStart},' 00:00:00') AND receice_datetime &lt;= CONCAT(#{receiceDatetimeEnd}, ' 23:59:59')
			</if>
			<if test="departureDatetimeStart != '' and departureDatetimeEnd != ''">
				AND departure_Datetime >= CONCAT(#{departureDatetimeStart},' 00:00:00') AND departure_Datetime &lt;= CONCAT(#{departureDatetimeEnd}, ' 23:59:59')
			</if>
			<if test="createTimeStart != '' and createTimeEnd != ''">
				AND create_time >= CONCAT(#{createTimeStart},' 00:00:00') AND create_time &lt;= CONCAT(#{createTimeEnd}, ' 23:59:59')
			</if>
			<choose>
				<when test="isReturnFlag != null and isReturnFlag == 1 ">
					AND return_datetime IS NOT NULL
				</when>
				<when test="isReturnFlag != null and isReturnFlag == 0 ">
					AND return_datetime IS NULL
				</when>
			</choose>
		</where>
		order by create_time,receice_datetime,departure_Datetime
		<if test="offset != null and limit != null">
			limit #{offset}, #{limit}
		</if>
	</select>
	
 	<select id="count" resultType="int">
		select count(*) from t_hk_carrier_dealer
		<where>
			<if test="bagBarcode != null and bagBarcode != ''">
				and bag_barcode like concat('%', #{bagBarcode} ,'%')
			</if>
			<if test="deliverBillNo != null and deliverBillNo != ''">
				and deliver_bill_no like concat('%', #{deliverBillNo}  ,'%')
			</if>
			<if test="carrierName != null and carrierName != ''">
				and carrier_name like concat('%', #{carrierName}  ,'%')
			</if>
			<if test="containerNumbers != null and containerNumbers != ''">
				and container_numbers like concat('%',#{containerNumbers} ,'%')
			</if>
			<if test="receiceDatetimeStart != '' and receiceDatetimeEnd != ''">
				AND receice_datetime >= CONCAT(#{receiceDatetimeStart},' 00:00:00') AND receice_datetime &lt;= CONCAT(#{receiceDatetimeEnd}, ' 23:59:59')
			</if>
			<if test="departureDatetimeStart != '' and departureDatetimeEnd != ''">
				AND departure_Datetime >= CONCAT(#{departureDatetimeStart},' 00:00:00') AND departure_Datetime &lt;= CONCAT(#{departureDatetimeEnd}, ' 23:59:59')
			</if>
			<if test="createTimeStart != '' and createTimeEnd != ''">
				AND create_time >= CONCAT(#{createTimeStart},' 00:00:00') AND create_time &lt;= CONCAT(#{createTimeEnd}, ' 23:59:59')
			</if>
			<choose>
				<when test="isReturnFlag != null and isReturnFlag == 1 ">
					AND return_datetime IS NOT NULL
				</when>
				<when test="isReturnFlag != null and isReturnFlag == 0 ">
					AND return_datetime IS NULL
				</when>
			</choose>
		</where>
	</select>
	 
	<insert id="save" parameterType="cn.com.xmmn.businesses.domain.HkCarrierDealerDO" useGeneratedKeys="true" keyProperty="id">
		insert into t_hk_carrier_dealer
		(
			`bag_barcode`, 
			`deliver_bill_no`, 
			`carrier_name`, 
			`container_numbers`, 
			`receice_datetime`, 
			`departure_datetime`, 
			`create_time`,
		    `return_Datetime`
		)
		values
		(
			#{bagBarcode}, 
			#{deliverBillNo}, 
			#{carrierName}, 
			#{containerNumbers}, 
			#{receiceDatetime}, 
			#{departureDatetime}, 
			#{createTime},
		    #{returnDatetime}
		)
	</insert>
	 
	<update id="update" parameterType="cn.com.xmmn.businesses.domain.HkCarrierDealerDO">
		update t_hk_carrier_dealer 
		<set>
			<if test="bagBarcode != null">`bag_barcode` = #{bagBarcode}, </if>
			<if test="deliverBillNo != null">`deliver_bill_no` = #{deliverBillNo}, </if>
			<if test="carrierName != null">`carrier_name` = #{carrierName}, </if>
			<if test="containerNumbers != null">`container_numbers` = #{containerNumbers}, </if>
			<if test="receiceDatetime != null">`receice_datetime` = #{receiceDatetime}, </if>
			<if test="departureDatetime != null">`departure_datetime` = #{departureDatetime}, </if>
			<if test="createTime != null">`create_time` = #{createTime}</if>
			<if test="returnDatetime != null">`return_Datetime` = #{returnDatetime}</if>
		</set>
		where id = #{id}
		<where>
			<if test="bagBarcode != null and bagBarcode != ''">
				and bag_barcode = #{bagBarcode}
			</if>
			<if test="id != null and id != ''">
				and id = #{id}
			</if>
		</where>
	</update>
	
	<delete id="remove">
		delete from t_hk_carrier_dealer where id = #{value}
	</delete>
	
	<delete id="batchRemove">
		delete from t_hk_carrier_dealer where id in 
		<foreach item="id" collection="array" open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>

</mapper>