<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.xmmn.businesses.dao.HkNoticeAwordDao">

	<select id="get" resultType="cn.com.xmmn.businesses.domain.HkNoticeAwordDO">
		select `id`,`capacity_code`,`ems_price`,`epacket_price`,`air_letters_price`,`air_parcels_price`,`sal_price`,`effective_date`,`expiry_date`,`currency`,`create_time`,`update_time` 
		from t_hk_notice_aword where id = #{value}
	</select>

	<select id="list" resultType="cn.com.xmmn.businesses.domain.HkNoticeAwordDO">
		select `id`,`capacity_code`,`ems_price`,`epacket_price`,`air_letters_price`,`air_parcels_price`,`sal_price`,`effective_date`,`expiry_date`,`currency`,`create_time`,`update_time` 
		from t_hk_notice_aword
        <where>
			<if test="capacityCode != null and capacityCode != ''">
				and capacity_code like concat('%', #{capacityCode} ,'%')
			</if>
			<if test="currency != null and currency != ''">
				and currency like concat('%', #{currency}  ,'%')
			</if>
			<if test="effectiveDateStart != '' and effectiveDateEnd != ''">
				AND effective_date >= CONCAT(#{effectiveDateStart},' 00:00:00') AND effective_date &lt;= CONCAT(#{effectiveDateEnd}, ' 23:59:59')
			</if>
			<if test="expiryDateStart != '' and expiryDateEnd != ''">
				AND expiry_date >= CONCAT(#{expiryDateStart},' 00:00:00') AND expiry_date &lt;= CONCAT(#{expiryDateEnd}, ' 23:59:59')
			</if>
			<if test="createTimeStart != '' and createTimeEnd != ''">
				AND create_time >= CONCAT(#{createTimeStart},' 00:00:00') AND create_time &lt;= CONCAT(#{createTimeEnd}, ' 23:59:59')
			</if>
		</where>
		order by create_time desc, effective_date desc
		<if test="offset != null and limit != null">
			limit #{offset}, #{limit}
		</if>
	</select>
	
 	<select id="count" resultType="int">
		select count(*) from t_hk_notice_aword
        <where>
			<if test="capacityCode != null and capacityCode != ''">
				and capacity_code like concat('%', #{capacityCode} ,'%')
			</if>
			<if test="currency != null and currency != ''">
				and currency like concat('%', #{currency}  ,'%')
			</if>
			<if test="effectiveDateStart != '' and effectiveDateEnd != ''">
				AND effective_date >= CONCAT(#{effectiveDateStart},' 00:00:00') AND effective_date &lt;= CONCAT(#{effectiveDateEnd}, ' 23:59:59')
			</if>
			<if test="expiryDateStart != '' and expiryDateEnd != ''">
				AND expiry_date >= CONCAT(#{expiryDateStart},' 00:00:00') AND expiry_date &lt;= CONCAT(#{expiryDateEnd}, ' 23:59:59')
			</if>
			<if test="createTimeStart != '' and createTimeEnd != ''">
				AND create_time >= CONCAT(#{createTimeStart},' 00:00:00') AND create_time &lt;= CONCAT(#{createTimeEnd}, ' 23:59:59')
			</if>
		</where>
	</select>
	 
	<insert id="save" parameterType="cn.com.xmmn.businesses.domain.HkNoticeAwordDO" useGeneratedKeys="true" keyProperty="id">
		insert into t_hk_notice_aword
		(
			`capacity_code`, 
			`ems_price`, 
			`epacket_price`, 
			`air_letters_price`, 
			`air_parcels_price`, 
			`sal_price`, 
			`effective_date`, 
			`expiry_date`, 
			`currency`, 
			`create_time`, 
			`update_time`
		)
		values
		(
			#{capacityCode}, 
			#{emsPrice}, 
			#{epacketPrice}, 
			#{airLettersPrice}, 
			#{airParcelsPrice}, 
			#{salPrice}, 
			#{effectiveDate}, 
			#{expiryDate}, 
			#{currency}, 
			#{createTime}, 
			#{updateTime}
		)
	</insert>
	 
	<update id="update" parameterType="cn.com.xmmn.businesses.domain.HkNoticeAwordDO">
		update t_hk_notice_aword 
		<set>
			<if test="capacityCode != null">`capacity_code` = #{capacityCode}, </if>
			<if test="emsPrice != null">`ems_price` = #{emsPrice}, </if>
			<if test="epacketPrice != null">`epacket_price` = #{epacketPrice}, </if>
			<if test="airLettersPrice != null">`air_letters_price` = #{airLettersPrice}, </if>
			<if test="airParcelsPrice != null">`air_parcels_price` = #{airParcelsPrice}, </if>
			<if test="salPrice != null">`sal_price` = #{salPrice}, </if>
			<if test="effectiveDate != null">`effective_date` = #{effectiveDate}, </if>
			<if test="expiryDate != null">`expiry_date` = #{expiryDate}, </if>
			<if test="currency != null">`currency` = #{currency}, </if>
			<if test="updateTime != null">`update_time` = #{updateTime}</if>
		</set>
		where id = #{id}
	</update>
	
	<delete id="remove">
		delete from t_hk_notice_aword where id = #{value}
	</delete>
	
	<delete id="batchRemove">
		delete from t_hk_notice_aword where id in 
		<foreach item="id" collection="array" open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>
	
	<select id="countByCapacityCodeAndDate" resultType="int">
		select count(*) from t_hk_notice_aword 
		where capacity_code = #{capacityCode} 
		and DATE(effective_date) = #{effectiveDate}
	</select>
	
	<insert id="batchSave" parameterType="java.util.List">
		insert into t_hk_notice_aword
		(
			`capacity_code`, 
			`ems_price`, 
			`epacket_price`, 
			`air_letters_price`, 
			`air_parcels_price`, 
			`sal_price`, 
			`effective_date`, 
			`expiry_date`, 
			`currency`, 
			`create_time`, 
			`update_time`
		)
		values
		<foreach collection="list" item="item" separator=",">
		(
			#{item.capacityCode}, 
			#{item.emsPrice}, 
			#{item.epacketPrice}, 
			#{item.airLettersPrice}, 
			#{item.airParcelsPrice}, 
			#{item.salPrice}, 
			#{item.effectiveDate}, 
			#{item.expiryDate}, 
			#{item.currency}, 
			#{item.createTime}, 
			#{item.updateTime}
		)
		</foreach>
	</insert>

</mapper>
