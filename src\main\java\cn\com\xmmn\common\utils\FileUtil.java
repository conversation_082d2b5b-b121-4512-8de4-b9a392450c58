package cn.com.xmmn.common.utils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;

public class FileUtil {

	/**
	 * 安全地上传文件，防止路径遍历攻击
	 * 
	 * @param file 文件字节数组
	 * @param filePath 文件保存路径
	 * @param fileName 文件名
	 * @throws Exception 上传失败时抛出异常
	 */
	public static void uploadFile(byte[] file, String filePath, String fileName) throws Exception {
		// 验证文件名不包含路径遍历字符
		if (fileName.contains("..") || fileName.contains("/") || fileName.contains("\\")) {
			throw new SecurityException("文件名不能包含路径字符");
		}
		
		// 创建目标目录
		File targetDir = new File(filePath);
		if (!targetDir.exists()) {
			if (!targetDir.mkdirs()) {
				throw new IOException("无法创建目标目录: " + filePath);
			}
		}
		
		// 规范化路径并验证安全性
		try {
			File targetFile = new File(filePath + fileName).getCanonicalFile();
			File uploadDir = new File(filePath).getCanonicalFile();
			
			// 确保文件路径在上传目录内
			if (!targetFile.getPath().startsWith(uploadDir.getPath())) {
				throw new SecurityException("检测到路径遍历攻击尝试");
			}
			
			// 安全地写入文件
			try (FileOutputStream out = new FileOutputStream(targetFile)) {
				out.write(file);
				out.flush();
			}
		} catch (IOException e) {
			throw new Exception("文件上传失败: " + e.getMessage(), e);
		}
	}

	public static boolean deleteFile(String fileName) {
		try {
			// 规范化路径
			File file = new File(fileName).getCanonicalFile();
			
			// 如果文件路径所对应的文件存在，并且是一个文件，则直接删除
			if (file.exists() && file.isFile()) {
				return file.delete();
			}
			return false;
		} catch (IOException e) {
			return false;
		}
	}

	/**
	 * 使用UUID重命名文件，保留原始扩展名
	 * 
	 * @param fileName 原始文件名
	 * @return 使用UUID重命名后的文件名
	 */
	public static String renameToUUID(String fileName) {
		int lastDotIndex = fileName.lastIndexOf(".");
		if (lastDotIndex < 0) {
			// 没有扩展名
			return UUID.randomUUID().toString();
		}
		return UUID.randomUUID() + "." + fileName.substring(lastDotIndex + 1);
	}
}
