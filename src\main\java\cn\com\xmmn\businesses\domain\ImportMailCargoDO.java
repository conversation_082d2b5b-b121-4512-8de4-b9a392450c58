package cn.com.xmmn.businesses.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;



/**
 * 进口邮件内件表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-26 13:45:12
 */
public class ImportMailCargoDO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//ID;批次号
	private Integer id;
	//邮件号码/邮件ID
	private String itemId;
	//物品名称
	private String bizName;
	//邮件税单号
	private String taxNo;
	//原产地（国别、地区）
	private String originNationCode;
	//产品数量
	private Integer goodsNum;
	//计量单位
	private String meterUnit;
	//申报总价
	private BigDecimal declareTotalVaule;
	//业务时间
	private Date pt;
	//乐观锁;数据版本号
	private Integer revision;
	//创建人id
	private Integer createUserId;
	//创建部门id
	private Integer createDeptId;
	//创建人
	private String createUserName;
	//创建部门
	private String createDeptName;
	//创建时间
	private Date createTime;
	//修改人id
	private Integer updateUserId;
	//修改人
	private String updateUserName;
	//修改部门id
	private Integer updateDeptId;
	//修改部门
	private String updateDeptName;
	//修改时间
	private Date updateTime;

	/**
	 * 设置：ID;批次号
	 */
	public void setId(Integer id) {
		this.id = id;
	}
	/**
	 * 获取：ID;批次号
	 */
	public Integer getId() {
		return id;
	}
	/**
	 * 设置：邮件号码/邮件ID
	 */
	public void setItemId(String itemId) {
		this.itemId = itemId;
	}
	/**
	 * 获取：邮件号码/邮件ID
	 */
	public String getItemId() {
		return itemId;
	}
	/**
	 * 设置：物品名称
	 */
	public void setBizName(String bizName) {
		this.bizName = bizName;
	}
	/**
	 * 获取：物品名称
	 */
	public String getBizName() {
		return bizName;
	}
	/**
	 * 设置：邮件税单号
	 */
	public void setTaxNo(String taxNo) {
		this.taxNo = taxNo;
	}
	/**
	 * 获取：邮件税单号
	 */
	public String getTaxNo() {
		return taxNo;
	}
	/**
	 * 设置：原产地（国别、地区）
	 */
	public void setOriginNationCode(String originNationCode) {
		this.originNationCode = originNationCode;
	}
	/**
	 * 获取：原产地（国别、地区）
	 */
	public String getOriginNationCode() {
		return originNationCode;
	}
	/**
	 * 设置：产品数量
	 */
	public void setGoodsNum(Integer goodsNum) {
		this.goodsNum = goodsNum;
	}
	/**
	 * 获取：产品数量
	 */
	public Integer getGoodsNum() {
		return goodsNum;
	}
	/**
	 * 设置：计量单位
	 */
	public void setMeterUnit(String meterUnit) {
		this.meterUnit = meterUnit;
	}
	/**
	 * 获取：计量单位
	 */
	public String getMeterUnit() {
		return meterUnit;
	}
	/**
	 * 设置：申报总价
	 */
	public void setDeclareTotalVaule(BigDecimal declareTotalVaule) {
		this.declareTotalVaule = declareTotalVaule;
	}
	/**
	 * 获取：申报总价
	 */
	public BigDecimal getDeclareTotalVaule() {
		return declareTotalVaule;
	}
	/**
	 * 设置：业务时间
	 */
	public void setPt(Date pt) {
		this.pt = pt;
	}
	/**
	 * 获取：业务时间
	 */
	public Date getPt() {
		return pt;
	}
	/**
	 * 设置：乐观锁;数据版本号
	 */
	public void setRevision(Integer revision) {
		this.revision = revision;
	}
	/**
	 * 获取：乐观锁;数据版本号
	 */
	public Integer getRevision() {
		return revision;
	}
	/**
	 * 设置：创建人id
	 */
	public void setCreateUserId(Integer createUserId) {
		this.createUserId = createUserId;
	}
	/**
	 * 获取：创建人id
	 */
	public Integer getCreateUserId() {
		return createUserId;
	}
	/**
	 * 设置：创建部门id
	 */
	public void setCreateDeptId(Integer createDeptId) {
		this.createDeptId = createDeptId;
	}
	/**
	 * 获取：创建部门id
	 */
	public Integer getCreateDeptId() {
		return createDeptId;
	}
	/**
	 * 设置：创建人
	 */
	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}
	/**
	 * 获取：创建人
	 */
	public String getCreateUserName() {
		return createUserName;
	}
	/**
	 * 设置：创建部门
	 */
	public void setCreateDeptName(String createDeptName) {
		this.createDeptName = createDeptName;
	}
	/**
	 * 获取：创建部门
	 */
	public String getCreateDeptName() {
		return createDeptName;
	}
	/**
	 * 设置：创建时间
	 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	/**
	 * 获取：创建时间
	 */
	public Date getCreateTime() {
		return createTime;
	}
	/**
	 * 设置：修改人id
	 */
	public void setUpdateUserId(Integer updateUserId) {
		this.updateUserId = updateUserId;
	}
	/**
	 * 获取：修改人id
	 */
	public Integer getUpdateUserId() {
		return updateUserId;
	}
	/**
	 * 设置：修改人
	 */
	public void setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
	}
	/**
	 * 获取：修改人
	 */
	public String getUpdateUserName() {
		return updateUserName;
	}
	/**
	 * 设置：修改部门id
	 */
	public void setUpdateDeptId(Integer updateDeptId) {
		this.updateDeptId = updateDeptId;
	}
	/**
	 * 获取：修改部门id
	 */
	public Integer getUpdateDeptId() {
		return updateDeptId;
	}
	/**
	 * 设置：修改部门
	 */
	public void setUpdateDeptName(String updateDeptName) {
		this.updateDeptName = updateDeptName;
	}
	/**
	 * 获取：修改部门
	 */
	public String getUpdateDeptName() {
		return updateDeptName;
	}
	/**
	 * 设置：修改时间
	 */
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	/**
	 * 获取：修改时间
	 */
	public Date getUpdateTime() {
		return updateTime;
	}
}
