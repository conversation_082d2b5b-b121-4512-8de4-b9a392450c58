{"name": "经营分析会", "describe": "经营分析会", "avatar": "", "version": "4.2.0", "createdTime": "2022-8-2 23:11:52", "updatedTime": "2022-11-30 18:50:07", "dbConns": [], "profile": {"default": {"db": "29D1CE08-4C35-4D2D-AAA9-23D93305B52E", "dbConn": "3A9A5AC0-35E3-45A1-B5E1-7D1D9AF926DC", "entityInitFields": [{"defKey": "id", "defName": "ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": true, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "refDict": "", "uiHint": "", "id": "ADB3AD14-6603-43E2-8261-114E32442B5B"}, {"defKey": "revision", "defName": "乐观锁", "comment": "数据版本号", "domain": "FB37AF1B-4C9D-42E5-B53E-9FDAE58F2C48", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "92BF430E-01FA-4AEF-944F-25A142632654"}, {"defKey": "create_user_id", "defName": "创建人id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "uiHint": "", "id": "C8BE2C7A-8251-4ADD-BB4F-411C5754DA62"}, {"defKey": "create_user_name", "defName": "创建人", "comment": "", "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "4E471FD6-3E73-4A90-B660-51598A482409"}, {"defKey": "create_dept_id", "defName": "创建部门id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "0DC24AA9-4CD0-45D8-95CF-FA546BE343AB"}, {"defKey": "create_dept_name", "defName": "创建部门", "comment": "", "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "09F64AC4-4DEE-428F-AF64-4C103884E1AC"}, {"defKey": "create_time", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "BF50B290-27EA-4C14-B6D0-9D7BCFEA9436"}, {"defKey": "update_user_id", "defName": "修改人id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "0FD59DB8-1488-46FA-9835-52DAE7984232"}, {"defKey": "update_user_name", "defName": "修改人", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "604B0959-49A3-4296-89F4-D281276B9E42"}, {"defKey": "update_dept_id", "defName": "修改部门id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "64E4E706-71A6-4E6D-AAFF-F614DB2453AF"}, {"defKey": "update_dept_name", "defName": "修改部门", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "id": "3F9C3407-3295-439F-A1F4-97B4C39FE150"}, {"defKey": "update_time", "defName": "修改时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "672655DC-B999-4FFD-9D21-679D6D4187CA"}], "entityInitProperties": {"partitioned by": "(date string)", "row format delimited": "", "fields terminated by ','": "", "collection items terminated by '-'": "", "map keys terminated by ':'": "", "store as textfile;": ""}}, "javaHome": "", "sql": {"delimiter": ""}, "dataTypeSupports": [{"defKey": "MYSQL", "id": "29D1CE08-4C35-4D2D-AAA9-23D93305B52E"}, {"defKey": "ORACLE", "id": "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542"}, {"defKey": "SQLServer", "id": "BFC87171-C74F-494A-B7C2-76B9C55FACC9"}, {"defKey": "PostgreSQL", "id": "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022"}, {"defKey": "DB2", "id": "89504F5D-94BF-4C9E-8B2E-44F37305FED5"}, {"defKey": "DM", "id": "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307"}, {"defKey": "GaussDB", "id": "592C7013-143D-4E7B-AF64-0D7BF1E28230"}, {"defKey": "Kingbase", "id": "77BD85E5-9D0D-4096-8427-CBA306FC9C6A"}, {"defKey": "MaxCompute", "id": "11D1FB71-A587-4217-89BA-611B8A1F83E0"}, {"defKey": "SQLite", "id": "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1"}, {"defKey": "JAVA", "id": "797A1496-D649-4261-89B4-544132EC3F36"}, {"defKey": "JavaMybatis", "id": "895CFD1D-4273-4D32-A2C4-CAC70200AB5B"}, {"defKey": "JavaMybatisPlus", "id": "A2EE7B4A-CE62-4290-B00C-B26C1BF18073"}, {"defKey": "C#", "id": "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30"}, {"defKey": "Hive", "id": "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2"}, {"defKey": "Golang", "id": "B91D99E0-9B7C-416C-8737-B760957DAF09"}], "codeTemplates": [{"type": "appCode", "applyFor": "797A1496-D649-4261-89B4-544132EC3F36", " JpaBean": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.entity;\n$blankline\nimport io.swagger.annotations.ApiModel;\nimport io.swagger.annotations.ApiModelProperty;\nimport javax.persistence.*;\nimport java.io.Serializable;\nimport java.util.Date;\n$blankline\n\n /**\n * {{=it.entity.defName}};{{=it.entity.comment}}\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@ApiModel(value = \"{{=it.entity.defName}}\",description = \"{{=it.entity.comment}}\")\n@Table(name=\"{{=it.entity.defKey}}\")\npublic class {{=beanClass}} implements Serializable,Cloneable{\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    {{? field.primaryKey }}\n    @Id\n    @GeneratedValue\n    {{?}}\n    @ApiModelProperty(name = \"{{=field.defName}}\",notes = \"{{=field.comment}}\")\n    private {{=field.type}} {{=it.func.camel(field.defKey,false)}} ;\n{{~}}\n$blankline\n\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public {{=field.type}} get{{=it.func.camel(field.defKey,true)}}(){\n        return this.{{=it.func.camel(field.defKey,false)}};\n    }\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public void set{{=it.func.camel(field.defKey,true)}}({{=field.type}} {{= it.func.camel(field.defKey,false) }}){\n        this.{{=it.func.camel(field.defKey,false)}}={{=it.func.camel(field.defKey,false)}};\n    }\n{{~}}\n}"}, {"type": "appCode", "applyFor": "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30", "Default": "using System;\nusing System.Collections.Generic;\n\n$blankline\n{{\n    var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n}}\n/*\n * <AUTHOR> http://www.chiner.com.cn\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n * @desc : {{=it.func.join(it.entity.defName,it.entity.comment,'-')}}\n */\nnamespace PDManer.Application\n{\n    public partial class {{=it.func.camel(it.entity.defKey,true) }}\n    {\n    \n        {{~it.entity.fields:field:index}}\n        /// <summary>\n        /// {{=it.func.join(field.defName,field.comment,';')}}\n        /// </summary>\n        public {{=field.type}} {{=it.func.camel(field.defKey,true)}} { get; set; }\n        $blankline\n        {{~}}\n        \n    }\n}", "SqlSugar": "using System;\nusing System.Collections.Generic;\nusing SqlSugar;\n\n$blankline\n{{\n    var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    var sqlSugartable='[SugarTable(\"{{=it.entity.defKey}}\", TableDescription = \"{{=it.func.join(it.entity.defName,it.entity.comment,';')}}\")]';\n}}\n/*\n * <AUTHOR> <EMAIL>\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n * @desc : {{=it.func.join(it.entity.defName,it.entity.comment,'-')}}\n */\nnamespace Model.DBModel\n{\n    /// <summary>\n    /// {{=it.func.join(it.entity.defName,it.entity.comment,';')}}\n    /// </summary>\n    {{=sqlSugartable}}\n    public class {{=it.entity.defKey}}\n    {\n        {{~it.entity.fields:field:index}}\n        /// <summary>\n        /// {{=it.func.join(field.defName,field.comment,';')}}\n        /// </summary>\n        {{? field.primaryKey }}\n        [SugarColumn(IsIdentity = true, IsPrimaryKey = true)]\n        {{?}}\n        public {{=field.type}} {{=it.func.camel(field.defKey,true)}}{ get; set; }\n        $blankline\n        {{~}}\n    }\n}"}, {"applyFor": "895CFD1D-4273-4D32-A2C4-CAC70200AB5B", "type": "appCode", "Controller": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.controller;\n$blankline\nimport io.swagger.annotations.Api;\nimport io.swagger.annotations.ApiOperation;\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.data.domain.Page;\nimport org.springframework.data.domain.PageRequest;\nimport org.springframework.http.ResponseEntity;\nimport org.springframework.web.bind.annotation.*;\nimport {{=pkgName}}.entity.{{=beanClass}};\nimport {{=pkgName}}.service.{{=serviceClass}};\n$blankline\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表控制层\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@Api(tags = \"{{=it.entity.defName}}对象功能接口\")\n@RestController\n@RequestMapping(\"/{{=it.func.camel(it.entity.defKey,false)}}\")\npublic class {{=beanClass}}Controller{\n    @Autowired\n    private {{=serviceClass}} {{=serviceVarName}};\n    $blankline\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    @ApiOperation(\"通过ID查询单条数据\")\n    @GetMapping(\"{{{=it.func.camel(pkVarName,false)}}}\")\n    public ResponseEntity<{{=beanClass}}> queryById({{=pkDataType}} {{=pkVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.queryById({{=pkVarName}}));\n    }\n    $blankline\n    /** \n     * 分页查询\n     *\n     * @param {{=beanVarName}} 筛选条件\n     * @param pageRequest 分页对象\n     * @return 查询结果\n     */\n    @ApiOperation(\"分页查询\")\n    @GetMapping\n    public ResponseEntity<Page<{{=beanClass}}>> paginQuery({{=beanClass}} {{=beanVarName}}, PageRequest pageRequest){\n        return ResponseEntity.ok({{=serviceVarName}}.paginQuery({{=beanVarName}}, pageRequest));\n    }\n    $blankline\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    @ApiOperation(\"新增数据\")\n    @PostMapping\n    public ResponseEntity<{{=beanClass}}> add({{=beanClass}} {{=beanVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.insert({{=beanVarName}}));\n    }\n    $blankline\n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    @ApiOperation(\"更新数据\")\n    @PutMapping\n    public ResponseEntity<{{=beanClass}}> edit({{=beanClass}} {{=beanVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.update({{=beanVarName}}));\n    }\n    $blankline\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 是否成功\n     */\n    @ApiOperation(\"通过主键删除数据\")\n    @DeleteMapping\n    public ResponseEntity<Boolean> deleteById({{=pkDataType}} {{=pkVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.deleteById({{=pkVarName}}));\n    }\n}", "Service": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.service;\n$blankline\nimport org.springframework.data.domain.Page;\nimport org.springframework.data.domain.PageRequest;\nimport {{=pkgName}}.entity.{{=beanClass}};\n$blankline\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表服务接口\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\npublic interface {{=serviceClass}}{\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    {{=beanClass}} queryById({{=pkDataType}} {{=pkVarName}});\n    \n    /** \n     * 分页查询\n     *\n     * @param {{=beanVarName}} 筛选条件\n     * @param pageRequest 分页对象\n     * @return 查询结果\n     */\n    Page<{{=beanClass}}> paginQuery({{=beanClass}} {{=beanVarName}}, PageRequest pageRequest);\n\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    {{=beanClass}} insert({{=beanClass}} {{=beanVarName}});\n\n    \n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    {{=beanClass}} update({{=beanClass}} {{=beanVarName}});\n\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 是否成功\n     */\n    boolean deleteById({{=pkDataType}} {{=pkVarName}});\n}", "ServiceImpl": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkVarNameU = \"UndefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkVarNameU = it.func.camel(field.defKey,true);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    var mapperName = beanVarName+'Mapper';\n    \n}}package {{=pkgName}}.service.impl;\n$blankline\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.stereotype.Service;\nimport org.springframework.data.domain.Page;\nimport org.springframework.data.domain.PageImpl;\nimport org.springframework.data.domain.PageRequest;\nimport {{=pkgName}}.entity.{{=beanClass}};\nimport {{=pkgName}}.mapper.{{=beanClass}}Mapper;\nimport {{=pkgName}}.service.{{=serviceClass}};\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表服务实现类\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@Service\npublic class {{=serviceClass}}Impl implements {{=serviceClass}}{\n    @Autowired\n    private {{=beanClass}}Mapper {{=mapperName}};\n    $blankline\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    public {{=beanClass}} queryById({{=pkDataType}} {{=pkVarName}}){\n        return {{=mapperName}}.queryById({{=pkVarName}});\n    }\n    $blankline\n    /** \n     * 分页查询\n     *\n     * @param {{=beanVarName}} 筛选条件\n     * @param pageRequest 分页对象\n     * @return 查询结果\n     */\n    public Page<{{=beanClass}}> paginQuery({{=beanClass}} {{=beanVarName}}, PageRequest pageRequest){\n        long total = {{=mapperName}}.count({{=beanVarName}});\n        return new PageImpl<>({{=mapperName}}.queryAllByLimit({{=beanVarName}}, pageRequest), pageRequest, total);\n    }\n    $blankline\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    public {{=beanClass}} insert({{=beanClass}} {{=beanVarName}}){\n        {{=mapperName}}.insert({{=beanVarName}});\n        return {{=beanVarName}};\n    }\n    $blankline\n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    public {{=beanClass}} update({{=beanClass}} {{=beanVarName}}){\n        {{=mapperName}}.update({{=beanVarName}});\n        return queryById({{=beanVarName}}.get{{=pkVarNameU}}());\n    }\n    $blankline\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 是否成功\n     */\n    public boolean deleteById({{=pkDataType}} {{=pkVarName}}){\n        int total = {{=mapperName}}.deleteById({{=pkVarName}});\n        return total > 0;\n    }\n}", "Mapper": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.mapper;\n$blankline\nimport java.util.List;\nimport org.apache.ibatis.annotations.Mapper;\nimport org.apache.ibatis.annotations.Param;\nimport org.springframework.data.domain.Pageable;\nimport {{=pkgName}}.entity.{{=beanClass}};\n$blankline\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表数据库访问层\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@Mapper\npublic interface {{=beanClass}}Mapper{\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    {{=beanClass}} queryById({{=pkDataType}} {{=pkVarName}});\n    \n    /** \n     * 分页查询指定行数据\n     *\n     * @param {{=beanVarName}} 查询条件\n     * @param pageable 分页对象\n     * @return 对象列表\n     */\n    List<{{=beanClass}}> queryAllByLimit({{=beanClass}} {{=beanVarName}}, @Param(\"pageable\") Pageable pageable);\n\n    /** \n     * 统计总行数\n     *\n     * @param {{=beanVarName}} 查询条件\n     * @return 总行数\n     */\n    long count({{=beanClass}} {{=beanVarName}});\n\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 影响行数\n     */\n    int insert({{=beanClass}} {{=beanVarName}});\n\n    /** \n     * 批量新增数据\n     *\n     * @param entities List<{{=beanClass}}> 实例对象列表\n     * @return 影响行数\n     */\n    int insertBatch(@Param(\"entities\") List<{{=beanClass}}> entities);\n    \n    /** \n     * 批量新增或按主键更新数据\n     *\n     * @param entities List<{{=beanClass}}> 实例对象列表\n     * @return 影响行数\n     */\n    int insertOrUpdateBatch(@Param(\"entities\") List<{{=beanClass}}> entities);\n    \n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 影响行数\n     */\n    int update({{=beanClass}} {{=beanVarName}});\n\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 影响行数\n     */\n    int deleteById({{=pkDataType}} {{=pkVarName}});\n}", "Mapper.xml": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    var pkField = \"UNDEFINED_ID\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkField = field.defKey;\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\">\n<mapper namespace=\"{{=pkgName}}.mapper.{{=beanClass}}Mapper\">\n    <resultMap type=\"{{=pkgName}}.entity.{{=beanClass}}\" id=\"{{=beanClass}}Map\">\n    {{~it.entity.fields:field:index}}\n        <result property=\"{{=it.func.camel(field.defKey,false)}}\" column=\"{{=field.defKey}}\" jdbcType=\"{{=field.dbType}}\"/>\n    {{~}}\n    </resultMap>\n    $blankline\n    <!-- 通过ID查询单条数据 -->\n    <select id=\"queryById\" resultMap=\"{{=beanClass}}Map\">\n        select\n            {{=it.entity.fields.map(function(e,i){return e.defKey}).join(',')}}\n        from {{=it.entity.defKey}}\n        where {{=pkField}} = #{{{=pkVarName}}}\n    </select>\n    $blankline\n    <!--分页查询指定行数据-->\n    <select id=\"queryAllByLimit\" resultMap=\"{{=beanClass}}Map\">\n        select\n            {{=it.entity.fields.map(function(e,i){return e.defKey}).join(',')}}\n        from {{=it.entity.defKey}}\n        <where>\n        {{~it.entity.fields:field:index}}\n            <if test=\"{{=it.func.camel(field.defKey,false)}} != null and {{=it.func.camel(field.defKey,false)}} != ''\">\n                and {{=field.defKey}} = #{{{=it.func.camel(field.defKey,false)}}}\n            </if>\n        {{~}}\n        </where>\n        limit #{pageable.offset}, #{pageable.pageSize}\n    </select>\n    $blankline\n    <!--统计总行数-->\n    <select id=\"count\" resultType=\"java.lang.Long\">\n        select count(1)\n        from {{=it.entity.defKey}}\n        <where>\n        {{~it.entity.fields:field:index}}\n            <if test=\"{{=it.func.camel(field.defKey,false)}} != null and {{=it.func.camel(field.defKey,false)}} != ''\">\n                and {{=field.defKey}} = #{{{=it.func.camel(field.defKey,false)}}}\n            </if>\n        {{~}}\n        </where>\n    </select>\n    $blankline\n    <!--新增数据-->\n    <insert id=\"insert\" keyProperty=\"{{=pkField}}\" useGeneratedKeys=\"true\">\n        insert into {{=it.entity.defKey}}({{=it.entity.fields.map(function(e,i){return e.defKey}).join(',')}})\n        values ({{=it.entity.fields.map(function(e,i){return '#{'+it.func.camel(e.defKey,false)+'}'}).join(',')}})\n    </insert>\n    $blankline\n    <!-- 批量新增数据 -->\n    <insert id=\"insertBatch\" keyProperty=\"{{=pkField}}\" useGeneratedKeys=\"true\">\n        insert into {{=it.entity.defKey}}({{=it.entity.fields.map(function(e,i){return e.defKey}).join(',')}})\n        values\n        <foreach collection=\"entities\" item=\"entity\" separator=\",\">\n            ({{=it.entity.fields.map(function(e,i){return '#{entity.'+it.func.camel(e.defKey,false)+'}'}).join(',')}})\n        </foreach>\n    </insert>\n    $blankline\n    <!-- 批量新增或按主键更新数据 -->\n    <insert id=\"insertOrUpdateBatch\" keyProperty=\"{{=pkField}}\" useGeneratedKeys=\"true\">\n        insert into {{=it.entity.defKey}}({{=it.entity.fields.map(function(e,i){return e.defKey}).join(',')}})\n        values\n        <foreach collection=\"entities\" item=\"entity\" separator=\",\">\n            ({{=it.entity.fields.map(function(e,i){return '#{entity.'+it.func.camel(e.defKey,false)+'}'}).join(',')}})\n        </foreach>\n        on duplicate key update\n        {{=it.entity.fields.map(function(e,i){return e.defKey + '=values('+e.defKey+')'}).join(',\\n\\t\\t')}}\n    </insert>\n    $blankline\n    <!-- 更新数据 -->\n    <update id=\"update\">\n        update {{=it.entity.defKey}}\n        <set>\n        {{~it.entity.fields:field:index}}\n            <if test=\"{{=it.func.camel(field.defKey,false)}} != null and {{=it.func.camel(field.defKey,false)}} != ''\">\n                {{=field.defKey}} = #{{{=it.func.camel(field.defKey,false)}}},\n            </if>\n        {{~}}\n        </set>\n        where {{=pkField}} = #{{{=pkVarName}}}\n    </update>\n    $blankline\n    <!--通过主键删除-->\n    <delete id=\"deleteById\">\n        delete from {{=it.entity.defKey}} where {{=pkField}} = #{{{=pkVarName}}}\n    </delete>\n</mapper>\n\n", "Entity": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.entity;\n$blankline\nimport io.swagger.annotations.ApiModel;\nimport io.swagger.annotations.ApiModelProperty;\nimport java.io.Serializable;\nimport java.util.Date;\n$blankline\n\n /**\n * {{=it.entity.defName}};{{=it.entity.comment}}\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@ApiModel(value = \"{{=it.entity.defName}}\",description = \"{{=it.entity.comment}}\")\npublic class {{=beanClass}} implements Serializable,Cloneable{\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    @ApiModelProperty(name = \"{{=field.defName}}\",notes = \"{{=field.comment}}\")\n    private {{=field.type}} {{=it.func.camel(field.defKey,false)}} ;\n{{~}}\n$blankline\n\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public {{=field.type}} get{{=it.func.camel(field.defKey,true)}}(){\n        return this.{{=it.func.camel(field.defKey,false)}};\n    }\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public void set{{=it.func.camel(field.defKey,true)}}({{=field.type}} {{= it.func.camel(field.defKey,false) }}){\n        this.{{=it.func.camel(field.defKey,false)}}={{=it.func.camel(field.defKey,false)}};\n    }\n{{~}}\n}"}, {"applyFor": "A2EE7B4A-CE62-4290-B00C-B26C1BF18073", "type": "appCode", "Controller": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.controller;\n$blankline\nimport java.util.List;\nimport io.swagger.annotations.Api;\nimport io.swagger.annotations.ApiOperation;\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.data.domain.PageImpl;\nimport org.springframework.data.domain.PageRequest;\nimport org.springframework.http.ResponseEntity;\nimport org.springframework.web.bind.annotation.*;\nimport {{=pkgName}}.entity.{{=beanClass}};\nimport {{=pkgName}}.service.{{=serviceClass}};\n$blankline\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表控制层\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@Api(tags = \"{{=it.entity.defName}}对象功能接口\")\n@RestController\n@RequestMapping(\"/{{=it.func.camel(it.entity.defKey,false)}}\")\npublic class {{=beanClass}}Controller{\n    @Autowired\n    private {{=serviceClass}} {{=serviceVarName}};\n    $blankline\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    @ApiOperation(\"通过ID查询单条数据\")\n    @GetMapping(\"{{{=it.func.camel(pkVarName,false)}}}\")\n    public ResponseEntity<{{=beanClass}}> queryById({{=pkDataType}} {{=pkVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.queryById({{=pkVarName}}));\n    }\n    $blankline\n    /** \n     * 分页查询\n     *\n     * @param {{=beanVarName}} 筛选条件\n     * @param pageRequest 分页对象\n     * @return 查询结果\n     */\n    @ApiOperation(\"分页查询\")\n    @GetMapping\n    public ResponseEntity<PageImpl<{{=beanClass}}>> paginQuery({{=beanClass}} {{=beanVarName}}, PageRequest pageRequest){\n        //1.分页参数\n        long current = pageRequest.getPageNumber();\n        long size = pageRequest.getPageSize();\n\n        //2.分页查询\n        /*把Mybatis的分页对象做封装转换，MP的分页对象上有一些SQL敏感信息，还是通过spring的分页模型来封装数据吧*/\n        com.baomidou.mybatisplus.extension.plugins.pagination.Page<{{=beanClass}}> pageResult = {{=serviceVarName}}.paginQuery({{=beanVarName}}, current,size);\n\n        //3. 分页结果组装\n        List<{{=beanClass}}> dataList = pageResult.getRecords();\n        long total = pageResult.getTotal();\n        PageImpl<{{=beanClass}}> retPage = new PageImpl<{{=beanClass}}>(dataList,pageRequest,total);\n        return ResponseEntity.ok(retPage);\n    }\n    $blankline\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    @ApiOperation(\"新增数据\")\n    @PostMapping\n    public ResponseEntity<{{=beanClass}}> add({{=beanClass}} {{=beanVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.insert({{=beanVarName}}));\n    }\n    $blankline\n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    @ApiOperation(\"更新数据\")\n    @PutMapping\n    public ResponseEntity<{{=beanClass}}> edit({{=beanClass}} {{=beanVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.update({{=beanVarName}}));\n    }\n    $blankline\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 是否成功\n     */\n    @ApiOperation(\"通过主键删除数据\")\n    @DeleteMapping\n    public ResponseEntity<Boolean> deleteById({{=pkDataType}} {{=pkVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.deleteById({{=pkVarName}}));\n    }\n}", "Service": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.service;\n$blankline\nimport com.baomidou.mybatisplus.extension.plugins.pagination.Page;\nimport {{=pkgName}}.entity.{{=beanClass}};\n$blankline\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表服务接口\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\npublic interface {{=serviceClass}}{\n    $blankline\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    {{=beanClass}} queryById({{=pkDataType}} {{=pkVarName}});\n    $blankline\n    /**\n     * 分页查询\n     *\n     * @param {{=beanVarName}} 筛选条件\n     * @param current 当前页码\n     * @param size  每页大小\n     * @return\n     */\n    Page<{{=beanClass}}> paginQuery({{=beanClass}} {{=beanVarName}}, long current, long size);\n\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    {{=beanClass}} insert({{=beanClass}} {{=beanVarName}});\n\n    \n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    {{=beanClass}} update({{=beanClass}} {{=beanVarName}});\n\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 是否成功\n     */\n    boolean deleteById({{=pkDataType}} {{=pkVarName}});\n}", "ServiceImpl": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkVarNameU = \"UndefinedId\";\n    var pkFieldKey = \"UNDEFINED\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkFieldKey = field.defKey;\n            pkVarName = it.func.camel(field.defKey,false);\n            pkVarNameU = it.func.camel(field.defKey,true);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    var mapperName = beanVarName+'Mapper';\n    \n}}package {{=pkgName}}.service.impl;\n$blankline\nimport cn.hutool.core.util.StrUtil;\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.stereotype.Service;\nimport com.baomidou.mybatisplus.core.metadata.IPage;\nimport com.baomidou.mybatisplus.extension.plugins.pagination.Page;\nimport com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;\nimport com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;\n\nimport {{=pkgName}}.entity.{{=beanClass}};\nimport {{=pkgName}}.mapper.{{=beanClass}}Mapper;\nimport {{=pkgName}}.service.{{=serviceClass}};\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表服务实现类\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@Service\npublic class {{=serviceClass}}Impl implements {{=serviceClass}}{\n    @Autowired\n    private {{=beanClass}}Mapper {{=mapperName}};\n    $blankline\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    public {{=beanClass}} queryById({{=pkDataType}} {{=pkVarName}}){\n        return {{=mapperName}}.selectById({{=pkVarName}});\n    }\n    $blankline\n    /**\n     * 分页查询\n     *\n     * @param {{=beanVarName}} 筛选条件\n     * @param current 当前页码\n     * @param size  每页大小\n     * @return\n     */\n    public Page<{{=beanClass}}> paginQuery({{=beanClass}} {{=beanVarName}}, long current, long size){\n        //1. 构建动态查询条件\n        LambdaQueryWrapper<{{=beanClass}}> queryWrapper = new LambdaQueryWrapper<>();\n        {{~it.entity.fields.filter(function(e){return e[\"type\"]===\"String\"&&e.defKey !== pkFieldKey}):field:index}}\n        if(StrUtil.isNotBlank({{=beanVarName}}.get{{=it.func.camel(field.defKey,true)}}())){\n            queryWrapper.eq({{=beanClass}}::get{{=it.func.camel(field.defKey,true)}}, {{=beanVarName}}.get{{=it.func.camel(field.defKey,true)}}());\n        }\n        {{~}}\n\n        //2. 执行分页查询\n        Page<{{=beanClass}}> pagin = new Page<>(current , size , true);\n        IPage<{{=beanClass}}> selectResult = {{=mapperName}}.selectByPage(pagin , queryWrapper);\n        pagin.setPages(selectResult.getPages());\n        pagin.setTotal(selectResult.getTotal());\n        pagin.setRecords(selectResult.getRecords());\n\n        //3. 返回结果\n        return pagin;\n    }\n    $blankline\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    public {{=beanClass}} insert({{=beanClass}} {{=beanVarName}}){\n        {{=mapperName}}.insert({{=beanVarName}});\n        return {{=beanVarName}};\n    }\n    $blankline\n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    public {{=beanClass}} update({{=beanClass}} {{=beanVarName}}){\n        //1. 根据条件动态更新\n        LambdaUpdateChainWrapper<{{=beanClass}}> chainWrapper = new LambdaUpdateChainWrapper<{{=beanClass}}>({{=mapperName}});\n        {{~it.entity.fields.filter(function(e){return e[\"type\"]===\"String\"&&e.defKey !== pkFieldKey}):field:index}}\n        if(StrUtil.isNotBlank({{=beanVarName}}.get{{=it.func.camel(field.defKey,true)}}())){\n            chainWrapper.eq({{=beanClass}}::get{{=it.func.camel(field.defKey,true)}}, {{=beanVarName}}.get{{=it.func.camel(field.defKey,true)}}());\n        }\n        {{~}}\n        //2. 设置主键，并更新\n        chainWrapper.set({{=beanClass}}::get{{=pkVarNameU}}, {{=beanVarName}}.get{{=pkVarNameU}}());\n        boolean ret = chainWrapper.update();\n        //3. 更新成功了，查询最最对象返回\n        if(ret){\n            return queryById({{=beanVarName}}.get{{=pkVarNameU}}());\n        }else{\n            return {{=beanVarName}};\n        }\n    }\n    $blankline\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 是否成功\n     */\n    public boolean deleteById({{=pkDataType}} {{=pkVarName}}){\n        int total = {{=mapperName}}.deleteById({{=pkVarName}});\n        return total > 0;\n    }\n}", "Mapper": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.mapper;\n$blankline\n\nimport com.baomidou.mybatisplus.core.conditions.Wrapper;\nimport com.baomidou.mybatisplus.core.mapper.BaseMapper;\nimport com.baomidou.mybatisplus.core.metadata.IPage;\nimport com.baomidou.mybatisplus.core.toolkit.Constants;\nimport org.apache.ibatis.annotations.Mapper;\nimport org.apache.ibatis.annotations.Param;\nimport {{=pkgName}}.entity.{{=beanClass}};\n$blankline\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表数据库访问层\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@Mapper\npublic interface {{=beanClass}}Mapper  extends BaseMapper<{{=beanClass}}>{\n    /** \n     * 分页查询指定行数据\n     *\n     * @param page 分页参数\n     * @param wrapper 动态查询条件\n     * @return 分页对象列表\n     */\n    IPage<{{=beanClass}}> selectByPage(IPage<{{=beanClass}}> page , @Param(Constants.WRAPPER) Wrapper<{{=beanClass}}> wrapper);\n}", "Mapper.xml": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    var pkField = \"UNDEFINED_ID\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkField = field.defKey;\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\">\n$blankline\n\n<mapper namespace=\"{{=pkgName}}.mapper.{{=beanClass}}Mapper\">\n     <select id=\"selectByPage\" resultType=\"{{=pkgName}}.entity.{{=beanClass}}\">\n        select * from user ${ew.customSqlSegment}\n    </select>\n</mapper>\n\n", "Entity": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.entity;\n$blankline\nimport io.swagger.annotations.ApiModel;\nimport io.swagger.annotations.ApiModelProperty;\nimport com.baomidou.mybatisplus.annotation.TableName;\nimport com.baomidou.mybatisplus.annotation.TableId;\nimport java.io.Serializable;\nimport java.util.Date;\n$blankline\n\n /**\n * {{=it.entity.defName}};{{=it.entity.comment}}\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@ApiModel(value = \"{{=it.entity.defName}}\",description = \"{{=it.entity.comment}}\")\n@TableName(\"{{=it.entity.defKey}}\")\npublic class {{=beanClass}} implements Serializable,Cloneable{\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    @ApiModelProperty(name = \"{{=field.defName}}\",notes = \"{{=field.comment}}\")\n    {{? field.primaryKey }}\n    @TableId\n    {{?}}\n    private {{=field.type}} {{=it.func.camel(field.defKey,false)}} ;\n{{~}}\n$blankline\n\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public {{=field.type}} get{{=it.func.camel(field.defKey,true)}}(){\n        return this.{{=it.func.camel(field.defKey,false)}};\n    }\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public void set{{=it.func.camel(field.defKey,true)}}({{=field.type}} {{= it.func.camel(field.defKey,false) }}){\n        this.{{=it.func.camel(field.defKey,false)}}={{=it.func.camel(field.defKey,false)}};\n    }\n{{~}}\n}"}, {"applyFor": "29D1CE08-4C35-4D2D-AAA9-23D93305B52E", "type": "dbDDL", "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.dbType}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}} {{= field.notNull ? 'NOT NULL' : '' }} {{= field.autoIncrement ? 'AUTO_INCREMENT' : '' }} {{= field.defaultValue ? it.func.join('DEFAULT',field.defaultValue,' ') : '' }} COMMENT '{{=it.func.join(field.defName,field.comment,';')}}' {{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n)  COMMENT = '{{=it.func.join(it.entity.defName,it.entity.comment,';') }}';\n$blankline\n", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "deleteTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "deleteIndex": "{{~ it.entity.indexes:index}}\nDROP INDEX {{=index.defKey}} ;\n{{~}}", "message": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChanged(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push('代码:'+(before.defKey||'NULL')+'->'+(after.defKey||'NULL'));\n        }\n        if(before.defName !== after.defName){\n            ret.push('显示名称:'+(before.defName||'NULL')+'->'+(after.defName||'NULL'));\n        }\n        if(before.comment !== after.comment){\n            ret.push('说明:'+(before.comment||'NULL')+'->'+(after.comment||'NULL'));\n        }\n        if(ret.length>0){\n            return '    基本信息:\\n\\t'+ret.join('\\n\\t');\n        }\n        return '';\n    };\n    \n    function buildAddedDesc(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        \n        for (let field of fieldAdded) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildRemovedDesc(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        \n        for (let field of fieldRemoved) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildModifiedDesc(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        for (let field1 of fieldModified) { \n            let row = [];\n            let field = field1.before;\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n}}\n\n\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n{{? modifyEntities && modifyEntities.length > 0}}\n/* --------------- 修改表 --------------- */\n{{~ modifyEntities:entity}}\n{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]{{let changeText=baseChanged(entity.data.baseChanged);}}\n{{=baseChanged(entity.data.baseChanged)}}\n    {{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n    修改字段：\n    {{='\\t'}}{{=buildModifiedDesc(entity).join('\\n\\t')}}{{?}}{{\n        /*计算是否调整了属性*/\n        let propAdded = entity.data.propAdded || [];\n        let propRemoved = entity.data.propRemoved || [];\n        let propModified = entity.data.propModified || [];\n        let changed = propAdded.length>0 || propRemoved.length>0 || propModified.length>0;\n        /*计算关联是否调整*/\n        let refEntityAdd = entity.data.refEntityAdd || [];\n        let refEntityRemoved = entity.data.refEntityRemoved || [];\n        let relaArray = [];\n        for (let rela of refEntityAdd) {\n            relaArray.push('建立关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        for (let rela of refEntityRemoved) {\n            relaArray.push('解除关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        /*索引是否修改过*/\n        let indexChanged = entity.data.indexChanged;\n    }}{{=indexChanged?'\\n\\t更改了索引':''}}{{=changed?'\\n\\t更改了属性':''}}{{=relaArray.length>0?('\\n\\t'+relaArray.join('\\n\\t')):''}}\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n    添加字段：\n{{='\\t'}}{{=buildAddedDesc(entity).join('\\n\\t')}}\n{{?}}{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n    删除字段：\n{{='\\t'}}{{=buildRemovedDesc(entity).join('\\n\\t')}}\n{{?}}\n{{~}}\n{{?}}\n", "update": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChangedDDL(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push('ALTER TABLE '+before.defKey+' RENAME TO '+after.defKey);\n        }\n        let commentText = '';\n        let commentChanged = false;\n        if(before.defName !== after.defName){\n            commentText = after.defName;\n            commentChanged = true;\n        }\n        if(before.comment !== after.comment){\n            commentChanged = true;\n            if(commentText){\n                commentText = (commentText+ ';'+after.comment)\n            }else{\n                commentText = after.comment\n            }\n        }\n        if(commentChanged){\n            ret.push('ALTER TABLE '+after.defKey+' COMMENT \\''+commentText+'\\'');\n        }\n        let baseText = '-- 基本信息:\\n';\n        return baseText+ret.join(';\\n')+';';\n    };\n    \n    function buildAddedDDL(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        if(fieldAdded.length == 0){\n            return '';\n        }\n        \n        let firstDDL = 'ALTER TABLE '+entity.data.baseInfo.defKey;\n        for (let field of fieldAdded) { \n            let ddlItem = 'ADD COLUMN '+field.defKey+' '+field.dbType;\n            /*处理数据类型长度*/\n            if(field.len>0){\n                ddlItem += ('('+field.len);\n                if(parseInt(field.scale)>0){\n                    ddlItem += (','+field.scale);\n                }\n                ddlItem += ')';\n            }\n            if(field.notNull){\n                ddlItem += ' NOT NULL';\n            }\n            if(field.autoIncrement){\n                ddlItem += ' AUTO_INCREMENT';\n            }\n            if(field.defaultValue){\n                ddlItem += (' DEFAULT' + field.defaultValue);\n            }\n            ddlItem += (' COMMENT \\''+field.defName+';'+field.comment+'\\'');\n            \n            if(field.index>0 && field.afterFieldKey){\n                ddlItem += (' AFTER '+field.afterFieldKey);\n            }\n            ret.push(ddlItem);\n        }\n        return firstDDL+'\\n'+ret.join(',\\n');\n    };\n    \n    function buildRemovedDDL(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        if(fieldRemoved.length == 0){\n            return '';\n        }\n        \n        let firstDDL = 'ALTER TABLE '+entity.data.baseInfo.defKey;\n        for (let field of fieldRemoved) { \n            ret.push('DROP '+field.defKey);\n        }\n        return firstDDL+'\\n'+ret.join(',\\n');\n    };\n    \n    function buildModifiedDDL(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        let firstDDL = 'ALTER TABLE '+entity.data.baseInfo.defKey;\n        for (let field of fieldModified) { \n            let changeDDL = '';\n            let before = field.before || {};\n            let after = field.after || {};\n            if(before.defKey === after.defKey){\n                changeDDL += (' MODIFY COLUMN '+after.defKey);\n            }else{\n                changeDDL += (' CHANGE COLUMN '+before.defKey+' '+after.defKey);\n            }\n            changeDDL += (' '+after.dbType);\n            if(after.len>0){\n                changeDDL += ('('+after.len);\n                if(parseInt(field.scale)>0){\n                    changeDDL += (','+field.scale);\n                }\n                changeDDL += ')';\n            }\n            if(after.notNull){\n                changeDDL += ' NOT NULL';\n            }\n            let defaultValue = '';\n            if(after.defaultValue != null && after.defaultValue.length>0){\n                defaultValue = (after.defaultValue);\n            }else{\n                defaultValue = 'NULL';\n            }\n            changeDDL += (' DEFAULT ' + defaultValue);\n            let comment = after.comment||'';\n            changeDDL += (' COMMENT \\''+comment+'\\';');\n            ret.push(firstDDL+' '+changeDDL);\n        }\n        return ret;\n    };\n}}\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=it.func.createDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=it.func.dropDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? modifyEntities && modifyEntities.length > 0}}\n{{~ modifyEntities:entity}}\n/* --------------- 修改表 --------------- */\n-- 修改表：{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]\n{{=baseChangedDDL(entity.data.baseChanged)}}\n{{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n-- 修改字段：\n{{=buildModifiedDDL(entity).join('\\n')}}\n{{?}}{{\n/*索引是否修改过*/\nlet indexChanged = entity.data.indexChanged;\n}}\n{{? indexChanged }}\n{{=it.func.indexRebuildDDL(entity.data.baseInfo,entity.data.newIndexes,entity.data.fullFields,entity['type'])}}\n{{?}}\n\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n-- 添加字段：\n{{=buildAddedDDL(entity)}};\n{{?}}\n\n{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n-- 删除字段：\n{{=buildRemovedDDL(entity)}};\n{{?}}\n{{~}}\n{{?}}"}, {"applyFor": "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542", "type": "dbDDL", "createTable": "CREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{= field.notNull ? ' NOT NULL' : '' }}{{= field.autoIncrement ? '' : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "deleteTable": "DROP TABLE {{=it.entity.defKey}};", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "deleteIndex": "{{~ it.entity.indexes:index}}\nDROP INDEX {{=index.defKey}} ;\n{{~}}", "message": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChanged(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push('代码:'+(before.defKey||'NULL')+'->'+(after.defKey||'NULL'));\n        }\n        if(before.defName !== after.defName){\n            ret.push('显示名称:'+(before.defName||'NULL')+'->'+(after.defName||'NULL'));\n        }\n        if(before.comment !== after.comment){\n            ret.push('说明:'+(before.comment||'NULL')+'->'+(after.comment||'NULL'));\n        }\n        if(ret.length>0){\n            return '    基本信息:\\n\\t'+ret.join('\\n\\t');\n        }\n        return '';\n    };\n    \n    function buildAddedDesc(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        \n        for (let field of fieldAdded) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildRemovedDesc(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        \n        for (let field of fieldRemoved) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildModifiedDesc(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        for (let field1 of fieldModified) { \n            let row = [];\n            let field = field1.before;\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n}}\n{{? createEntities && createEntities.length > 0}}\n/* -------------------------------------------------- */\n创建表：\n{{~ createEntities:entity}}\n    {{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* -------------------------------------------------- */\n删除表：\n{{~ dropEntities:entity}}\n    {{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n\n{{? modifyEntities && modifyEntities.length > 0}}\n{{~ modifyEntities:entity}}\n/* -------------------------------------------------- */\n修改表：{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]{{let changeText=baseChanged(entity.data.baseChanged);}}\n{{=baseChanged(entity.data.baseChanged)}}\n    {{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n    添加字段：\n    {{='\\t'}}{{=buildAddedDesc(entity).join('\\n\\t')}}\n    {{?}}{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n    删除字段：\n    {{='\\t'}}{{=buildRemovedDesc(entity).join('\\n\\t')}}\n    {{?}}{{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n    修改字段：\n    {{='\\t'}}{{=buildModifiedDesc(entity).join('\\n\\t')}}\n    {{?}}{{\n        /*计算是否调整了属性*/\n        let propAdded = entity.data.propAdded || [];\n        let propRemoved = entity.data.propRemoved || [];\n        let propModified = entity.data.propModified || [];\n        let changed = propAdded.length>0 || propRemoved.length>0 || propModified.length>0;\n        /*计算关联是否调整*/\n        let refEntityAdd = entity.data.refEntityAdd || [];\n        let refEntityRemoved = entity.data.refEntityRemoved || [];\n        let relaArray = [];\n        for (let rela of refEntityAdd) {\n            relaArray.push('\\n\\t建立关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        for (let rela of refEntityRemoved) {\n            relaArray.push('\\n\\t解除关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        /*索引是否修改过*/\n        let indexChanged = entity.data.indexChanged;\n    }}\n{{=indexChanged?'\\n\\t更改了索引':''}}\n{{=changed?'\\n\\t更改了属性':''}}\n{{=relaArray.length>0?relaArray.join(''):''}}\n{{~}}\n{{?}}", "update": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChangedDDL(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push(`ALTER TABLE ${before.defKey} RENAME TO ${after.defKey}`);\n        }\n        let commentText = '';\n        let commentChanged = false;\n        if(before.defName !== after.defName){\n            commentText = after.defName;\n            commentChanged = true;\n        }\n        if(before.comment !== after.comment){\n            commentChanged = true;\n            if(commentText){\n                commentText = (commentText+ ';'+after.comment)\n            }else{\n                commentText = after.comment\n            }\n        }\n        if(commentChanged){\n            let myText = `COMMENT ON TABLE ${after.defKey} IS '${commentText}'`;\n            ret.push(myText);\n        }\n        let baseText = '-- 基本信息:\\n';\n        return baseText+ret.join(';\\n')+';';\n    };\n    \n    function buildAddedDDL(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        if(fieldAdded.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldAdded) { \n            let ddlItem = `ADD (${field.defKey} ${field.dbType}`;\n            /*处理数据类型长度*/\n            if(field.len>0){\n                ddlItem += ('('+field.len);\n                if(parseInt(field.scale)>0){\n                    ddlItem += (','+field.scale);\n                }\n                ddlItem += ')';\n            }\n            let defaultValue = field.defaultValue;\n            defaultValue = (defaultValue==null)?\"\":(\"\"+defaultValue);\n            if(defaultValue.length>0){\n                ddlItem += (' DEFAULT ' + defaultValue);\n            }\n            if(field.notNull){\n                ddlItem += ' NOT NULL';\n            }\n            ddlItem += ')';\n            ret.push(`${firstDDL} ${ddlItem}`);\n            \n            /*处理字段注释*/\n            let fieldComments = [];\n            if(field.defName != null &&field.defName.length>0){\n                fieldComments.push(field.defName);\n            }\n            if(field.comment != null &&field.comment.length>0){\n                fieldComments.push(field.comment);\n            }\n            let commentText = fieldComments.join(';');\n            if(commentText != null && commentText.length > 0){\n                let commentDDL = `COMMENT ON COLUMN ${entity.data.baseInfo.defKey}.${field.defKey} IS '${commentText}'`;\n                 ret.push(commentDDL);\n            }\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildRemovedDDL(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        if(fieldRemoved.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldRemoved) { \n            ret.push(`${firstDDL} DROP COLUMN ${field.defKey}`);\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildModifiedDDL(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldModified) { \n            let changeDDL = '';\n            let before = field.before || {};\n            let after = field.after || {};\n            if(before.defKey !== after.defKey){\n                let renameText = `ALTER TABLE ${entity.data.baseInfo.defKey} RENAME COLUMN ${before.defKey} TO ${after.defKey};`;\n                ret.push(renameText);\n            }\n            /*如果没有变化，则不生成变更语句*/\n            if(before.dbType === after.dbType \n            && before['len'] === after['len'] \n            && before.scale === after.scale\n            && before.primaryKey === after.primaryKey\n            && before.notNull === after.notNull\n            && before.autoIncrement === after.autoIncrement\n            && before.defaultValue === after.defaultValue){\n                continue;\n            }\n            changeDDL += ('MODIFY ('+after.defKey+'');\n            changeDDL += (' '+after.dbType);\n            if(after.len>0){\n                changeDDL += ('('+after.len);\n                if(parseInt(field.scale)>0){\n                    changeDDL += (','+field.scale);\n                }\n                changeDDL += ')';\n            }\n            let defaultValue = after.defaultValue;\n            defaultValue = (defaultValue==null)?\"\":(\"\"+defaultValue);\n            if(defaultValue.length>0){\n                changeDDL += (' DEFAULT ' + defaultValue);\n            }\n            \n            if(after.notNull){\n                changeDDL += ' NOT NULL';\n            }\n            changeDDL += ')';\n            ret.push(`${firstDDL} ${changeDDL};`);\n        }\n        return ret;\n    };\n}}\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=it.func.createDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=it.func.dropDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? modifyEntities && modifyEntities.length > 0}}\n{{~ modifyEntities:entity}}\n/* --------------- 修改表 --------------- */\n-- 修改表：{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]\n{{=baseChangedDDL(entity.data.baseChanged)}}\n{{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n-- 修改字段：\n{{=buildModifiedDDL(entity).join('\\n')}}\n{{?}}{{\n/*索引是否修改过*/\nlet indexChanged = entity.data.indexChanged;\n}}\n{{? indexChanged }}\n{{=it.func.indexRebuildDDL(entity.data.baseInfo,entity.data.newIndexes,entity.data.fullFields,entity['type'])}}\n{{?}}\n\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n-- 添加字段：\n{{=buildAddedDDL(entity)}};\n{{?}}\n\n{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n-- 删除字段：\n{{=buildRemovedDDL(entity)}};\n{{?}}\n{{~}}\n{{?}}"}, {"applyFor": "BFC87171-C74F-494A-B7C2-76B9C55FACC9", "type": "dbDDL", "createTable": "IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[{{=it.entity.defKey}}]') AND type in (N'U')) DROP TABLE [dbo].[{{=it.entity.defKey}}];\n\nCREATE TABLE [dbo].[{{=it.entity.defKey}}](\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= field.autoIncrement ? ' IDENTITY(1,1)' : '' }}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}EXEC sp_addextendedproperty 'MS_Description', '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}', 'SCHEMA', dbo, 'table', {{=it.entity.defKey}}, null, null;{{?}}\n{{~it.entity.fields:field:index}}\nEXEC sp_addextendedproperty 'MS_Description', '{{=it.func.join(field.defName,field.comment,';')}}', 'SCHEMA', dbo, 'table', {{=it.entity.defKey}}, 'column', {{=field.defKey}};\n{{~}}\n", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "deleteTable": "IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[{{=it.entity.defKey}}]') AND type in (N'U')) DROP TABLE [dbo].[{{=it.entity.defKey}}];", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "deleteIndex": "", "message": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChanged(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push('代码:'+(before.defKey||'NULL')+'->'+(after.defKey||'NULL'));\n        }\n        if(before.defName !== after.defName){\n            ret.push('显示名称:'+(before.defName||'NULL')+'->'+(after.defName||'NULL'));\n        }\n        if(before.comment !== after.comment){\n            ret.push('说明:'+(before.comment||'NULL')+'->'+(after.comment||'NULL'));\n        }\n        if(ret.length>0){\n            return '    基本信息:\\n\\t'+ret.join('\\n\\t');\n        }\n        return '';\n    };\n    \n    function buildAddedDesc(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        \n        for (let field of fieldAdded) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildRemovedDesc(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        \n        for (let field of fieldRemoved) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildModifiedDesc(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        for (let field1 of fieldModified) { \n            let row = [];\n            let field = field1.before;\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n}}\n\n\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n{{? modifyEntities && modifyEntities.length > 0}}\n/* --------------- 修改表 --------------- */\n{{~ modifyEntities:entity}}\n{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]{{let changeText=baseChanged(entity.data.baseChanged);}}\n{{=baseChanged(entity.data.baseChanged)}}\n    {{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n    修改字段：\n    {{='\\t'}}{{=buildModifiedDesc(entity).join('\\n\\t')}}{{?}}{{\n        /*计算是否调整了属性*/\n        let propAdded = entity.data.propAdded || [];\n        let propRemoved = entity.data.propRemoved || [];\n        let propModified = entity.data.propModified || [];\n        let changed = propAdded.length>0 || propRemoved.length>0 || propModified.length>0;\n        /*计算关联是否调整*/\n        let refEntityAdd = entity.data.refEntityAdd || [];\n        let refEntityRemoved = entity.data.refEntityRemoved || [];\n        let relaArray = [];\n        for (let rela of refEntityAdd) {\n            relaArray.push('建立关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        for (let rela of refEntityRemoved) {\n            relaArray.push('解除关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        /*索引是否修改过*/\n        let indexChanged = entity.data.indexChanged;\n    }}{{=indexChanged?'\\n\\t更改了索引':''}}{{=changed?'\\n\\t更改了属性':''}}{{=relaArray.length>0?('\\n\\t'+relaArray.join('\\n\\t')):''}}\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n    添加字段：\n{{='\\t'}}{{=buildAddedDesc(entity).join('\\n\\t')}}\n{{?}}{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n    删除字段：\n{{='\\t'}}{{=buildRemovedDesc(entity).join('\\n\\t')}}\n{{?}}\n{{~}}\n{{?}}", "update": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChangedDDL(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push(`EXEC sp_rename '${before.defKey}','${after.defKey}'`);\n        }\n        let commentText = '';\n        let commentChanged = false;\n        if(before.defName !== after.defName){\n            commentText = after.defName;\n            commentChanged = true;\n        }\n        if(before.comment !== after.comment){\n            commentChanged = true;\n            if(commentText){\n                commentText = (commentText+ ';'+after.comment)\n            }else{\n                commentText = after.comment\n            }\n        }\n        if(commentChanged){\n            let myText = `IF ((SELECT COUNT(*) FROM ::fn_listextendedproperty('MS_Description','SCHEMA', 'dbo','TABLE', '${after.defKey}', NULL, NULL)) > 0)\n            \\n\\tEXEC sp_updateextendedproperty 'MS_Description', '${commentText}','SCHEMA', 'dbo','TABLE', '${after.defKey}'\n            \\nELSE\n            \\n\\tEXEC sp_addextendedproperty 'MS_Description', '${commentText}', 'SCHEMA', 'dbo','TABLE', '${after.defKey}'\n            `;\n            ret.push(myText);\n            /*ret.push('ALTER TABLE '+after.defKey+' COMMENT \\''+commentText+'\\'');*/\n        }\n        let baseText = '-- 基本信息:\\n';\n        return baseText+ret.join(';\\n')+';';\n    };\n    \n    function buildAddedDDL(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        if(fieldAdded.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE [dbo].[${entity.data.baseInfo.defKey}]`;\n        for (let field of fieldAdded) { \n            let ddlItem = `ADD [${field.defKey}] ${field.dbType}`;\n            /*处理数据类型长度*/\n            if(field.len>0){\n                ddlItem += ('('+field.len);\n                if(parseInt(field.scale)>0){\n                    ddlItem += (','+field.scale);\n                }\n                ddlItem += ')';\n            }\n            let defaultValue = field.defaultValue;\n            defaultValue = (defaultValue==null)?\"\":(\"\"+defaultValue);\n            if(defaultValue.length>0){\n                ddlItem += (' DEFAULT ' + defaultValue);\n            }\n            if(field.notNull){\n                ddlItem += ' NOT NULL';\n            }\n            ret.push(`${firstDDL} ${ddlItem}`);\n            \n            /*处理字段注释*/\n            let fieldComments = [];\n            if(field.defName != null &&field.defName.length>0){\n                fieldComments.push(field.defName);\n            }\n            if(field.comment != null &&field.comment.length>0){\n                fieldComments.push(field.comment);\n            }\n            let commentText = fieldComments.join(';');\n            if(commentText != null && commentText.length > 0){\n                let commentDDL = `EXEC sp_addextendedproperty 'MS_Description', N'${commentText}','SCHEMA', N'dbo','TABLE', N'${entity.data.baseInfo.defKey}','COLUMN', N'${field.defKey}'`;\n                ret.push(commentDDL);\n            }\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildRemovedDDL(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        if(fieldRemoved.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE [dbo].[${entity.data.baseInfo.defKey}]`;\n        for (let field of fieldRemoved) { \n            ret.push(`${firstDDL} DROP COLUMN [${field.defKey}]`);\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildModifiedDDL(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        let firstDDL = `ALTER TABLE [dbo].[${entity.data.baseInfo.defKey}]`;\n        for (let field of fieldModified) { \n            let changeDDL = '';\n            let before = field.before || {};\n            let after = field.after || {};\n            if(before.defKey === after.defKey){\n                changeDDL += (' ALTER COLUMN ['+after.defKey+']');\n            }else{\n                let renameText = `EXEC sp_rename '[dbo].[${entity.data.baseInfo.defKey}].[${before.defKey}]','${after.defKey}','COLUMN';`;\n                ret.push(renameText);\n                continue;\n            }\n            changeDDL += (' '+after.dbType);\n            if(after.len>0){\n                changeDDL += ('('+after.len);\n                if(parseInt(after.scale)>0){\n                    changeDDL += (','+after.scale);\n                }\n                changeDDL += ')';\n            }\n            let defaultValue = after.defaultValue;\n            defaultValue = (defaultValue==null)?\"\":(\"\"+defaultValue);\n            if(defaultValue.length>0){\n                changeDDL += (' DEFAULT ' + defaultValue);\n            }\n            \n            if(after.notNull){\n                changeDDL += ' NOT NULL';\n            }\n            ret.push(`${firstDDL} ${changeDDL};`);\n        }\n        return ret;\n    };\n}}\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=it.func.createDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=it.func.dropDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? modifyEntities && modifyEntities.length > 0}}\n{{~ modifyEntities:entity}}\n/* --------------- 修改表 --------------- */\n-- 修改表：{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]\n{{=baseChangedDDL(entity.data.baseChanged)}}\n{{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n-- 修改字段：\n{{=buildModifiedDDL(entity).join('\\n')}}\n{{?}}{{\n/*索引是否修改过*/\nlet indexChanged = entity.data.indexChanged;\n}}\n{{? indexChanged }}\n{{=it.func.indexRebuildDDL(entity.data.baseInfo,entity.data.newIndexes,entity.data.fullFields,entity['type'])}}\n{{?}}\n\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n-- 添加字段：\n{{=buildAddedDDL(entity)}};\n{{?}}\n\n{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n-- 删除字段：\n{{=buildRemovedDDL(entity)}};\n{{?}}\n{{~}}\n{{?}}"}, {"applyFor": "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022", "type": "dbDDL", "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{? field.autoIncrement}}SERIAL{{??}}{{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "deleteTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "deleteIndex": "{{~ it.entity.indexes:index}}\nDROP INDEX {{=index.defKey}} ;\n{{~}}", "message": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChanged(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push('代码:'+(before.defKey||'NULL')+'->'+(after.defKey||'NULL'));\n        }\n        if(before.defName !== after.defName){\n            ret.push('显示名称:'+(before.defName||'NULL')+'->'+(after.defName||'NULL'));\n        }\n        if(before.comment !== after.comment){\n            ret.push('说明:'+(before.comment||'NULL')+'->'+(after.comment||'NULL'));\n        }\n        if(ret.length>0){\n            return '    基本信息:\\n\\t'+ret.join('\\n\\t');\n        }\n        return '';\n    };\n    \n    function buildAddedDesc(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        \n        for (let field of fieldAdded) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildRemovedDesc(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        \n        for (let field of fieldRemoved) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildModifiedDesc(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        for (let field1 of fieldModified) { \n            let row = [];\n            let field = field1.before;\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n}}\n\n\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n{{? modifyEntities && modifyEntities.length > 0}}\n/* --------------- 修改表 --------------- */\n{{~ modifyEntities:entity}}\n{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]{{let changeText=baseChanged(entity.data.baseChanged);}}\n{{=baseChanged(entity.data.baseChanged)}}\n    {{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n    修改字段：\n    {{='\\t'}}{{=buildModifiedDesc(entity).join('\\n\\t')}}{{?}}{{\n        /*计算是否调整了属性*/\n        let propAdded = entity.data.propAdded || [];\n        let propRemoved = entity.data.propRemoved || [];\n        let propModified = entity.data.propModified || [];\n        let changed = propAdded.length>0 || propRemoved.length>0 || propModified.length>0;\n        /*计算关联是否调整*/\n        let refEntityAdd = entity.data.refEntityAdd || [];\n        let refEntityRemoved = entity.data.refEntityRemoved || [];\n        let relaArray = [];\n        for (let rela of refEntityAdd) {\n            relaArray.push('建立关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        for (let rela of refEntityRemoved) {\n            relaArray.push('解除关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        /*索引是否修改过*/\n        let indexChanged = entity.data.indexChanged;\n    }}{{=indexChanged?'\\n\\t更改了索引':''}}{{=changed?'\\n\\t更改了属性':''}}{{=relaArray.length>0?('\\n\\t'+relaArray.join('\\n\\t')):''}}\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n    添加字段：\n{{='\\t'}}{{=buildAddedDesc(entity).join('\\n\\t')}}\n{{?}}{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n    删除字段：\n{{='\\t'}}{{=buildRemovedDesc(entity).join('\\n\\t')}}\n{{?}}\n{{~}}\n{{?}}", "update": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChangedDDL(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push(`ALTER TABLE ${before.defKey} RENAME TO ${after.defKey}`);\n        }\n        let commentText = '';\n        let commentChanged = false;\n        if(before.defName !== after.defName){\n            commentText = after.defName;\n            commentChanged = true;\n        }\n        if(before.comment !== after.comment){\n            commentChanged = true;\n            if(commentText){\n                commentText = (commentText+ ';'+after.comment)\n            }else{\n                commentText = after.comment\n            }\n        }\n        if(commentChanged){\n            let myText = `COMMENT ON TABLE ${after.defKey} IS '${commentText}'`;\n            ret.push(myText);\n        }\n        let baseText = '-- 基本信息:\\n';\n        return baseText+ret.join(';\\n')+';';\n    };\n    \n    function buildAddedDDL(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        if(fieldAdded.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldAdded) { \n            let ddlItem = `ADD COLUMN ${field.defKey} ${field.dbType}`;\n            /*处理数据类型长度*/\n            if(field.len>0){\n                ddlItem += ('('+field.len);\n                if(parseInt(field.scale)>0){\n                    ddlItem += (','+field.scale);\n                }\n                ddlItem += ')';\n            }\n            if(field.notNull){\n                ddlItem += ' NOT NULL';\n            }            \n            let defaultValue = field.defaultValue;\n            defaultValue = (defaultValue==null)?\"\":(\"\"+defaultValue);\n            if(defaultValue.length>0){\n                ddlItem += (' DEFAULT ' + defaultValue);\n            }\n\n            ret.push(`${firstDDL} ${ddlItem}`);\n            \n            /*处理字段注释*/\n            let fieldComments = [];\n            if(field.defName != null &&field.defName.length>0){\n                fieldComments.push(field.defName);\n            }\n            if(field.comment != null &&field.comment.length>0){\n                fieldComments.push(field.comment);\n            }\n            let commentText = fieldComments.join(';');\n            if(commentText != null && commentText.length > 0){\n                let commentDDL = `COMMENT ON COLUMN ${entity.data.baseInfo.defKey}.${field.defKey} IS '${commentText}'`;\n                 ret.push(commentDDL);\n            }\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildRemovedDDL(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        if(fieldRemoved.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldRemoved) { \n            ret.push(`${firstDDL} DROP COLUMN ${field.defKey}`);\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildModifiedDDL(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldModified) { \n            let before = field.before || {};\n            let after = field.after || {};\n            if(before.defKey !== after.defKey){\n                let renameText = `ALTER TABLE ${entity.data.baseInfo.defKey} RENAME COLUMN ${before.defKey} TO ${after.defKey};`;\n                ret.push(renameText);\n            }\n            /*如果没有变化，则不生成变更语句*/\n            if(before.dbType === after.dbType \n            && before['len'] === after['len'] \n            && before.scale === after.scale\n            && before.primaryKey === after.primaryKey\n            && before.notNull === after.notNull\n            && before.autoIncrement === after.autoIncrement\n            && before.defaultValue === after.defaultValue){\n                continue;\n            }\n            if(before.dbType !== after.dbType || before.len !== after.len || before.scale !== after.scale){\n                let dbTypeDDL = `ALTER TABLE ${entity.data.baseInfo.defKey} ALTER COLUMN ${after.defKey} TYPE ${before.dbType}`;\n                if(after.len>0){\n                    dbTypeDDL += ('('+after.len);\n                    if(parseInt(after.scale)>0){\n                        dbTypeDDL += (','+after.scale);\n                    }\n                    dbTypeDDL += ')';\n                }\n                ret.push(dbTypeDDL+';');\n            }\n            \n            if(before.defaultValue !== after.defaultValue){\n                let defaultDDL = '';\n                let defaultValue = after.defaultValue;\n                defaultValue = (defaultValue==null)?\"NULL\":(\"\"+defaultValue);\n                if(defaultValue.length>0){\n                    defaultDDL += ('SET DEFAULT ' + defaultValue);\n                }\n                let defaultTpl = `ALTER TABLE ${entity.data.baseInfo.defKey} ALTER COLUMN ${defaultDDL};`;\n                ret.push(defaultTpl);\n            }\n            \n            if(before.notNull !== after.notNull){\n                let notNullDDL= 'SET NULL';\n                if(after.notNull){\n                    let notNullDDL= 'SET NOT NULL';\n                }\n                let notNullTpl = `ALTER TABLE ${entity.data.baseInfo.defKey} ALTER COLUMN ${notNullDDL};`;\n                ret.push(notNullTpl);\n            }\n        }\n        return ret;\n    };\n}}\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=it.func.createDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=it.func.dropDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? modifyEntities && modifyEntities.length > 0}}\n{{~ modifyEntities:entity}}\n/* --------------- 修改表 --------------- */\n-- 修改表：{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]\n{{=baseChangedDDL(entity.data.baseChanged)}}\n{{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n-- 修改字段：\n{{=buildModifiedDDL(entity).join('\\n')}}\n{{?}}{{\n/*索引是否修改过*/\nlet indexChanged = entity.data.indexChanged;\n}}\n{{? indexChanged }}\n-- 索引重建\n{{=it.func.indexRebuildDDL(entity.data.baseInfo,entity.data.newIndexes,entity.data.fullFields,entity['type'])}}\n{{?}}\n\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n-- 添加字段：\n{{=buildAddedDDL(entity)}};\n{{?}}\n\n{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n-- 删除字段：\n{{=buildRemovedDDL(entity)}};\n{{?}}\n{{~}}\n{{?}}"}, {"type": "dbDDL", "applyFor": "89504F5D-94BF-4C9E-8B2E-44F37305FED5", "createTable": "CREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= field.autoIncrement ? '' : '' }}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n"}, {"type": "dbDDL", "applyFor": "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307", "createTable": "CREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= field.autoIncrement ? '' : '' }}{{= field.defaultValue ? it.func.join(' DEFAULT ','('+field.defaultValue+')',' ') : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n"}, {"type": "dbDDL", "applyFor": "592C7013-143D-4E7B-AF64-0D7BF1E28230", "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= field.autoIncrement ? ' AUTO_INCREMENT' : '' }}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n"}, {"type": "dbDDL", "applyFor": "77BD85E5-9D0D-4096-8427-CBA306FC9C6A", "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n"}, {"applyFor": "11D1FB71-A587-4217-89BA-611B8A1F83E0", "type": "dbDDL", "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}} {{= field.notNull ? 'NOT NULL' : '' }} {{= field.autoIncrement ? 'AUTO_INCREMENT' : '' }} {{= field.defaultValue ? it.func.join('DEFAULT',field.defaultValue,' ') : '' }} COMMENT '{{=it.func.join(field.defName,field.comment,';')}}' {{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n)  COMMENT '{{=it.func.join(it.entity.defName,it.entity.comment,';') }}';\n$blankline\n", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "deleteTable": "", "createIndex": "", "deleteIndex": "", "message": "", "update": ""}, {"applyFor": "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1", "type": "dbDDL", "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}} {{= field.notNull ? 'NOT NULL' : '' }} {{= field.autoIncrement ? 'AUTOINCREMENT' : '' }} {{= field.defaultValue ? it.func.join('DEFAULT',field.defaultValue,' ') : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }} --{{=it.func.join(field.defName,field.comment,';')}}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n)  ; --{{=it.func.join(it.entity.defName,it.entity.comment,';') }}\n$blankline\n", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n"}, {"type": "dbDDL", "applyFor": "dictSQLTemplate", "content": "/* 插入字典总表[{{=it.dict.defKey}}-{{=it.dict.defName}}] */\nINSERT INTO SYS_DICT(KEY_,LABEL,INTRO,REVISION) VALUES('{{=it.dict.defKey}}','{{=it.dict.defName}}','{{=it.dict.intro}}',1);\n/* 插入字典明细表 */\n{{~it.dict.items:item:index}}\nINSERT INTO SYS_DICT_ITEM(DICT_KEY,KEY_,LABEL,SORT_,INTRO,REVISION) VALUES('{{=it.dict.defKey}}','{{=item.defKey}}','{{=item.defName}}','{{=item.sort}}','{{=item.intro}}',1);\n{{~}}"}, {"applyFor": "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2", "type": "dbDDL", "createTable": "/**字段名,关键字等全部用的小写*/\ndrop table if exists {{=it.entity.defKey}};\n/**补充上库名,external关键字根据建表规范看是否添加*/\ncreate [external] table if not exists {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n/**这里把varchar,char,text,date,datetime字段全部映射为string类型.tinyint unsigned,bit,Integer,tinyint,smallint,mediumint映射为int类型,int unsigned映射为bigint.其它自定义映射规则根据自己情况修改*/\n/**当长度>0只有为decimal类型或double类型时才保留长度和小数的位数*/\n{{~it.entity.fields:field:index}}\n    {{=it.func.lowerCase(field.defKey)}} {{=it.func.lowerCase(field.dbType)=='varchar'||it.func.lowerCase(field.dbType)=='char'||it.func.lowerCase(field.dbType)=='text'||it.func.lowerCase(field.dbType)=='date'||it.func.lowerCase(field.dbType)=='datetime' ? 'string':it.func.lowerCase(field.dbType)=='tinyint unsigned'||it.func.lowerCase(field.dbType)=='bit'||it.func.lowerCase(field.dbType)=='integer'||it.func.lowerCase(field.dbType)=='tinyint'||it.func.lowerCase(field.dbType)=='smallint'||it.func.lowerCase(field.dbType)=='mediumint' ? 'int':it.func.lowerCase(field.dbType)=='int unsigned' ? 'bigint':it.func.lowerCase(field.dbType)}}{{?field.len>0&&(it.func.lowerCase(field.dbType)=='decimal'||it.func.lowerCase(field.dbType)=='double')}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{=')'}}{{?}}{{?}} comment '{{=it.func.join(field.defName,field.comment,'')}}' {{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n{{?}}\n)\n{{\n    let partitionedBy = it.entity.properties['partitioned by'];\n    partitionedBy = partitionedBy?partitionedBy:'请在扩展属性中配置[partitioned by]属性';\n}}\ncomment '{{=it.func.join(it.entity.defName,';') }}'\n/**是否分区表,分区字段名和字段注释自定义*/\n[partitioned by {{=partitionedBy}}]\n/**文件存储格式自定义*/\n[stored as orc]\n/**hdfs上的地址自定义*/\n[location xxx]\n;", "createView": "", "deleteTable": "", "createIndex": "", "deleteIndex": "", "message": "", "update": ""}, {"applyFor": "B91D99E0-9B7C-416C-8737-B760957DAF09", "type": "appCode", "content": "{{\n    var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1<10?\"0\"+today.getMonth():today.getMonth();\n    var days=today.getDate()<10?\"0\"+today.getDate():today.getDate();\n    var hours = today.getHours()<10?\"0\"+today.getHours():today.getHours();         \n\tvar minutes = today.getMinutes()<10?\"0\"+today.getMinutes():today.getMinutes();      \n\tvar seconds = today.getSeconds()<10?\"0\"+today.getSeconds():today.getSeconds();    \n}}\n// Package models  {{=it.func.join(it.entity.defName,it.entity.comment,'，')}}\n// author : http://www.liyang.love\n// date : {{=fullYear}}-{{=month}}-{{=days}} {{=hours}}:{{=minutes}}\n// desc : {{=it.func.join(it.entity.defName,it.entity.comment,'，')}}\npackage models\n\n$blankline\n\n// {{=it.func.camel(it.entity.defKey,true) }}  {{=it.func.join(it.entity.defName,it.entity.comment,'，')}}。\n// 说明:{{=it.entity.comment}}\n// 表名:{{=it.entity.defKey}}\n// group: {{=it.func.camel(it.entity.defKey,true) }}\n// obsolete:\n// appliesto:go 1.8+;\n// namespace:hongmouer.his.models.{{=it.func.camel(it.entity.defKey,true) }}\n// assembly: hongmouer.his.models.go\n// class:HongMouer.HIS.Models.{{=it.func.camel(it.entity.defKey,true) }}\n// version:{{=fullYear}}-{{=month}}-{{=days}} {{=hours}}:{{=minutes}}\ntype {{=it.func.camel(it.entity.defKey,true) }} struct {\n    {{~it.entity.fields:field:index}}\n    {{=formatGoLang(it.func.camel(field.defKey,true),null,field,it.entity.fields,null,1)}} {{=formatGoLang(field.type,\"type\",field,it.entity.fields,10,3)}}  `gorm:\"column:{{=field.primaryKey?\"primaryKey;\":\"\"}}{{=field.defKey}}\" json:\"{{=it.func.camel(field.defKey,true)}}\"` {{=formatGoLang(\"gorm:column:\"+field.defKey+\" json:\"+it.func.camel(field.defKey,true),null,field,it.entity.fields,null,2)}}  //type:{{=formatGoLang(field.type,\"type\",field,it.entity.fields,null,3)}}  comment:{{=formatGoLang(it.func.join(field.defName,field.comment,';'),\"defName\",field,it.entity.fields,null,4)}}  version:{{=fullYear}}-{{=month}}-{{=days}} {{=hours}}:{{=minutes}}\n    {{~}}\n}\n\n\n$blankline\n// TableName 表名:{{=it.entity.defKey}}，{{=it.entity.defName}}。\n// 说明:{{=it.entity.comment}}\nfunc (ZentaoUserInfo) TableName() string {\n\treturn \"{{=it.entity.defKey}}\"\n}\n\n{{\n\nfunction formatGoLang(str, fieldName, field, fileds, emptLength, isFiled) {\n    var maxLength = 0;\n\n    if (isFiled == 1) {\n        for (var i = 0; i < fileds.length; i++) {\n            if (getBlength(it.func.camel(fileds[i].defKey, true)) > maxLength) {\n                maxLength = getBlength(it.func.camel(fileds[i].defKey, true)) + 2;\n            }\n        }\n    } else if (isFiled == 2) {\n        for (var i = 0; i < fileds.length; i++) {\n            var newStr = \"gorm:column:\" + fileds[i].defKey + \" json:\" + it.func.camel(fileds[i].defKey, true);\n            if (getBlength(newStr) > maxLength) {\n                maxLength = getBlength(newStr) + 2;\n            }\n        }\n        var empt = \"\";\n        var strLength = getBlength(str);\n        if (field.primaryKey) {\n            strLength += getBlength(\"primaryKey;\");\n        }\n        for (var j = 0; j < maxLength - strLength; j++) {\n            empt += ' ';\n        }\n        return empt;\n    } else if (isFiled == 3) {\n        /*获取某个字段的最大长度*/\n        for (var i = 0; i < fileds.length; i++) {\n            var newStr = eval(\"fileds[\" + i + \"].\" + fieldName);\n            if (getBlength(newStr) > maxLength) {\n                maxLength = getBlength(newStr) + 1;\n            }\n        }\n    } else if (isFiled == 4) {\n        /*获取某个字段的最大长度*/\n        for (var i = 0; i < fileds.length; i++) {\n            var newStr = fileds[i].comment + \";\" + fileds[i].defName;\n            if (getBlength(newStr) > maxLength) {\n                maxLength = getBlength(newStr) + 1;\n            }\n        }\n    }\n    else {\n        maxLength = emptLength;\n    }\n\n    var strLength = getBlength(str);\n    for (var j = 0; j < maxLength - strLength; j++) {\n        str += ' ';\n    }\n    return str;\n}\n\nfunction getBlength(str) {\n    var n = 0;\n    for (var i = str.length; i--;) {\n        n += str.charCodeAt(i) > 255 ? 2 : 1;\n    }\n    return n;\n} \n\n}}"}], "generatorDoc": {"docTemplate": "E:\\work\\国际验单\\经营分析会\\数据模型\\经营分析会-模板.docx"}, "relationFieldSize": 200, "uiHint": [{"defKey": "Input", "defName": "普通输入框", "id": "642D2E0A-8846-4549-BE56-8C0473F26EDE"}, {"defKey": "Select", "defName": "下拉输入框", "id": "FB111359-2B73-4443-926C-08A98E446448"}, {"defKey": "CheckBox", "defName": "复选框", "id": "0CB8A6C9-1115-4FC0-B51E-5C028065082F"}, {"defKey": "RadioBox", "defName": "单选框", "id": "5C04987A-260F-4B7C-A5D5-22A181AAE9CA"}, {"defKey": "Double", "defName": "小数输入", "id": "8D5BAFE4-E15C-4707-A047-8EE59C58E70F"}, {"defKey": "Integer", "defName": "整数输入", "id": "9999AF2A-A44E-415C-A2DC-D7C613BD0073"}, {"defKey": "Money", "defName": "金额输入", "id": "2B0C3D0C-7BAF-4B36-81AD-9362B5E5DC2E"}, {"defKey": "Date", "defName": "日期输入", "id": "E4D94E14-F695-487F-AFC2-4D888009B7DA"}, {"defKey": "DataYearMonth", "defName": "年月输入", "id": "936927E3-DD2D-4096-87FD-074CDE278D59"}, {"defKey": "Text", "defName": "长文本输入", "id": "D89DD4F1-ADAC-4469-BF8D-B3FF41AE7963"}, {"defKey": "RichText", "defName": "富文本输入", "id": "C134EB1F-4CFF-49E0-882F-2C6FB275CB20"}], "headers": [{"refKey": "def<PERSON><PERSON>", "hideInGraph": false, "value": "字段代码"}, {"refKey": "defName", "hideInGraph": false, "value": "显示名称"}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": true, "value": "主键"}, {"refKey": "notNull", "hideInGraph": true, "value": "不为空"}, {"refKey": "autoIncrement", "hideInGraph": true, "value": "自增"}, {"refKey": "domain", "hideInGraph": true, "value": "数据域"}, {"refKey": "type", "hideInGraph": true, "value": "数据类型"}, {"refKey": "len", "hideInGraph": true, "value": "长度"}, {"refKey": "scale", "hideInGraph": true, "value": "小数位数"}, {"refKey": "comment", "hideInGraph": true, "value": "说明"}, {"refKey": "refDict", "hideInGraph": true, "value": "数据字典"}, {"refKey": "defaultValue", "hideInGraph": true, "value": "默认值"}, {"refKey": "isStandard", "hideInGraph": true, "value": "标准字段"}, {"refKey": "uiHint", "hideInGraph": true, "value": "UI建议"}, {"refKey": "extProps", "hideInGraph": true, "value": "拓展属性"}], "menuWidth": "241px", "modelType": "modalAll"}, "entities": [{"id": "47F82A27-1BF1-44E6-91A2-315765195250", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "t_batch", "defName": "导入批次表", "comment": "", "properties": {"partitioned by": "(date string)", "row format delimited": "", "fields terminated by ','": "", "collection items terminated by '-'": "", "map keys terminated by ':'": "", "store as textfile;": ""}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": true}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": true}, {"refKey": "len", "hideInGraph": true}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id", "defName": "ID", "comment": "批次号", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "refDict": "", "uiHint": "", "id": "FFB5CE74-A708-4B76-9A65-C54C9CFBEF46"}, {"defKey": "revision", "defName": "乐观锁", "comment": "数据版本号", "domain": "FB37AF1B-4C9D-42E5-B53E-9FDAE58F2C48", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "67E99622-D51A-4F6F-B240-04ABC0310549"}, {"defKey": "create_user_id", "defName": "创建人id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "2A9D057F-230F-4AD0-95FA-7E75473847B7"}, {"defKey": "create_user_name", "defName": "创建人", "comment": "", "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "54EE73CB-2BC1-4E9D-9027-2B99BC47EBC8"}, {"defKey": "create_dept_id", "defName": "创建部门id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "06C183AB-47D3-458A-B740-5A9B341D0370"}, {"defKey": "create_dept_name", "defName": "创建部门", "comment": "", "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "844FA14C-4CA4-468B-8B42-9F5C73B9AC78"}, {"defKey": "create_time", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "90AC6505-7500-429B-A5E8-29B98FEED5F4"}, {"defKey": "update_user_id", "defName": "修改人id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "8EA09EB4-E51C-4EAD-B000-E4D2C9FC53DA"}, {"defKey": "update_user_name", "defName": "修改人", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "46043516-470F-4E8B-B9EA-750A08C1AB4B"}, {"defKey": "update_dept_id", "defName": "修改部门id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "4F3F457A-25E6-4F5E-8E3F-CC197AA1298C"}, {"defKey": "update_dept_name", "defName": "修改部门", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "id": "526668B8-2A97-4F98-A186-E867B526000B"}, {"defKey": "update_time", "defName": "修改时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "5AD728C1-90BD-456E-8262-019FBD4A606F"}, {"defKey": "module_type", "defName": "模板类型", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "234455ED-4015-488A-97DC-A7F20B3D4842", "id": "27B4418F-3A9F-4995-BB43-2B8AAE6AF1D8"}, {"defKey": "num", "defName": "记录数", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "51EF7713-57ED-417E-B09A-5D5051D6E9D6", "id": "9F15F115-08BC-4BE2-89E8-8022A0A56D57"}], "correlations": [], "indexes": []}, {"id": "6DCCA81D-348C-46BF-9EAC-C708BC415ECD", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "t_commercial_export_manual", "defName": "商业出口手工", "comment": "", "properties": {"partitioned by": "(date string)", "row format delimited": "", "fields terminated by ','": "", "collection items terminated by '-'": "", "map keys terminated by ':'": "", "store as textfile;": ""}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": true}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": true}, {"refKey": "len", "hideInGraph": true}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id", "defName": "ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "refDict": "", "uiHint": "", "id": "4AE886B9-3EDC-4D67-BD9F-90F5CB9D6B98"}, {"defKey": "revision", "defName": "乐观锁", "comment": "数据版本号", "domain": "FB37AF1B-4C9D-42E5-B53E-9FDAE58F2C48", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "EDE7A767-CC94-47AC-9E9F-F364A091FE26"}, {"defKey": "create_user_id", "defName": "创建人id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "EB85ABCE-2F20-4874-B619-CF6511C9662D"}, {"defKey": "create_user_name", "defName": "创建人", "comment": "", "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "30DB8AAB-BDFB-4775-B9C1-640DE9E20962"}, {"defKey": "create_dept_id", "defName": "创建部门id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "0BD0D649-FE2F-42E8-99ED-4912331C2B82"}, {"defKey": "create_dept_name", "defName": "创建部门", "comment": "", "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "E4DAEB22-98A7-494D-A5F3-680982D1B548"}, {"defKey": "create_time", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "44E5475F-2081-4F77-BC3F-3E0FCB6AD745"}, {"defKey": "update_user_id", "defName": "修改人id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "E59AE4A2-1F32-439B-B9B2-77B3F1235C93"}, {"defKey": "update_user_name", "defName": "修改人", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "6CF91666-1D9C-4A6D-968C-290D70725B30"}, {"defKey": "update_dept_id", "defName": "修改部门id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "99458ADA-4FB9-4A8B-9B8B-F4F0F0E3E884"}, {"defKey": "update_dept_name", "defName": "修改部门", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "id": "EFB2A0C0-C128-491C-98B9-F105D0EB7FBB"}, {"defKey": "update_time", "defName": "修改时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "79B69F7E-DCC7-41BF-B99C-FD201F116601"}, {"defKey": "business_product", "defName": "业务产品", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "id": "B2B143B2-5F85-4029-8724-59D9DDFC24B2"}, {"defKey": "customer_code", "defName": "大客户号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "5910B283-83B2-4608-8642-6287A2E58C5E"}, {"defKey": "customer_name", "defName": "大宗客户", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "CF8680CD-7A15-4336-ABD1-C64D6CA2FCCC", "id": "439C9DCE-3D73-4832-BEEC-09E6274384A1"}, {"defKey": "item_no", "defName": "邮件号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "FC36D330-11BA-4528-AA53-4675EA07F5CB"}, {"defKey": "receive_country", "defName": "寄达国", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "id": "7E46CE11-2D58-4CD8-9B5B-7E05B0301A2A"}, {"defKey": "sender", "defName": "寄件人", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "id": "756CCBAC-6783-46DF-853A-F10CE0791D09"}, {"defKey": "weight", "defName": "重量", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "FF4459C5-6B45-4DBF-8FC0-E06239BC05B4", "id": "6CD10A7A-ACCA-4E16-A60E-25085F37E60D"}, {"defKey": "amount", "defName": "金额", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "C3B1681B-99F9-4818-9E80-DE1652A51D85", "id": "FC0EE35D-2341-4039-BD6E-45EBAE7CC50D"}, {"defKey": "posting_datetime", "defName": "收寄时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "1F4E857A-5069-41D9-8923-8590F7B14861"}, {"defKey": "batch_id", "defName": "批次id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "907FF023-06E8-45D0-AC89-83C8F5ABE584"}], "correlations": [], "indexes": []}, {"id": "990632EC-D864-4FE3-850B-CA600A8BA575", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "t_commercial_export_new", "defName": "商业出口新一代", "comment": "", "properties": {"partitioned by": "(date string)", "row format delimited": "", "fields terminated by ','": "", "collection items terminated by '-'": "", "map keys terminated by ':'": "", "store as textfile;": ""}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": true}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": true}, {"refKey": "len", "hideInGraph": true}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id", "defName": "ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "refDict": "", "uiHint": "", "id": "40AA05AF-0F97-4D0F-AC87-336E823C59EC"}, {"defKey": "revision", "defName": "乐观锁", "comment": "数据版本号", "domain": "FB37AF1B-4C9D-42E5-B53E-9FDAE58F2C48", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "31CEB1E4-1AB1-48C5-AD9C-6A5983706244"}, {"defKey": "create_user_id", "defName": "创建人id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "984657A0-C51E-471C-8274-915E2DB7FB0F"}, {"defKey": "create_user_name", "defName": "创建人", "comment": "", "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "71641860-AE2E-4441-B447-527588E28E14"}, {"defKey": "create_dept_id", "defName": "创建部门id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "BF32C6B3-4760-4CD1-88C4-7AA1DF035FAB"}, {"defKey": "create_dept_name", "defName": "创建部门", "comment": "", "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "D4EF0511-92A3-4271-A449-5892DDA20ADD"}, {"defKey": "create_time", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "AB48CFCF-CB20-4DC2-BD10-A7504D399C95"}, {"defKey": "update_user_id", "defName": "修改人id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "FE2CA077-159B-4BA9-871F-98F85BEE2581"}, {"defKey": "update_user_name", "defName": "修改人", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "3FE8EDE7-73BC-48F5-803D-A1C89974CCB7"}, {"defKey": "update_dept_id", "defName": "修改部门id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "6060E2A0-B489-41DA-82FC-E3F168941133"}, {"defKey": "update_dept_name", "defName": "修改部门", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "id": "69B4ABC6-9FEB-4FBE-B4CF-CFA68BE59F46"}, {"defKey": "update_time", "defName": "修改时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "AA11BE40-B35B-42E0-881F-7659CE18A8E7"}, {"defKey": "main_no", "defName": "main_no", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "1F640E09-6346-4C1E-ABDD-4D0510ED04AE"}, {"defKey": "sub", "defName": "sub", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "92EB8916-FAC5-4A21-916F-733BE356E5C5"}, {"defKey": "type", "defName": "type", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "C57903B0-2B70-4CC9-AA80-FA4FBA89C336"}, {"defKey": "posting_datetime", "defName": "收寄时间", "comment": "日期+时间", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "7E9B7965-1E8D-467D-99F8-6973E057EF58"}, {"defKey": "item_no", "defName": "邮件号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "BFC8F5D4-24EC-4E7D-9840-5B189EAED1B7"}, {"defKey": "dest", "defName": "寄达国代码", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "01861B22-193B-4FF3-AB23-660EB38D0FED"}, {"defKey": "destination_name", "defName": "寄达国名称", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "id": "00E16BA4-9E43-43AC-9E0B-A1999BC0CA9B"}, {"defKey": "weight", "defName": "重量", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "FF4459C5-6B45-4DBF-8FC0-E06239BC05B4", "id": "D2AEE0A4-21FA-4B1A-B046-96907C23601C"}, {"defKey": "walk_in_pickup", "defName": "walk_in_pickup", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "BB22DFF1-9A03-4D7C-8098-48734CEE8B5B", "id": "1466BFF4-DB06-4708-A7AA-2A638FAD95B9"}, {"defKey": "ar_pod", "defName": "ar_pod", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "BB22DFF1-9A03-4D7C-8098-48734CEE8B5B", "id": "0A6A3964-5F75-4F23-BEE7-207F3971763C"}, {"defKey": "kp_aba", "defName": "kp_aba", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "BB22DFF1-9A03-4D7C-8098-48734CEE8B5B", "id": "8F6E8BCA-BA51-432A-A877-8C38CD15E8B5"}, {"defKey": "csgn_no", "defName": "csgn_no", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "87F2DA13-4FFC-452D-ACB9-96CE0CACDB08"}, {"defKey": "pp", "defName": "pp", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "F938E9EC-B22C-43BB-9A9A-C567225EDFA6"}, {"defKey": "ps", "defName": "ps", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "DD13B2C7-60D1-474C-B13F-7EEAA8DC2264"}, {"defKey": "mail_sub_class", "defName": "mail_sub_class", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "BB22DFF1-9A03-4D7C-8098-48734CEE8B5B", "id": "6A79B0DB-E16C-4B3A-AB91-B7C5F9EE8C8D"}, {"defKey": "postage", "defName": "postage", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "C3B1681B-99F9-4818-9E80-DE1652A51D85", "id": "81F84B5E-9239-431E-85EE-B57EFA0A81E1"}, {"defKey": "sum_insured", "defName": "sum_insured", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "C3B1681B-99F9-4818-9E80-DE1652A51D85", "id": "0BBA9760-2109-4E30-B36B-0C17C839B722"}, {"defKey": "insur_premium", "defName": "insur_premium", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "C3B1681B-99F9-4818-9E80-DE1652A51D85", "id": "A2877A91-F3B5-4D11-BDE8-8DA8D132F6DD"}, {"defKey": "delivery_premium", "defName": "delivery_premium", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "C3B1681B-99F9-4818-9E80-DE1652A51D85", "id": "3875EBFC-4F65-4A27-8BE6-28FEECB6D422"}, {"defKey": "net_amount", "defName": "net_amount", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "C3B1681B-99F9-4818-9E80-DE1652A51D85", "id": "95F457FB-C80E-4D42-94BB-51268C2207FE"}, {"defKey": "delivery_type", "defName": "delivery_type", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "2F7128F9-692F-40F9-8CB5-C2CD1DA67B01"}, {"defKey": "input_datetime", "defName": "input_datetime", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "98D66C68-8A2A-4217-8AE0-4AC2492B784F"}, {"defKey": "acceptance_office", "defName": "acceptance_office", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "41BBA205-86E7-4D57-A3D7-767EB60228A2"}, {"defKey": "batch_id", "defName": "批次id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "refDict": "", "extProps": {}, "id": "B72025C1-9330-4B64-916A-6974428A8108"}], "correlations": [], "indexes": []}, {"id": "FF04F1FC-386B-40C7-ABEF-3AB9B548544A", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "t_air_mail_manual_regular_bills", "defName": "邮件空运手工导入常规账单", "comment": "", "properties": {"partitioned by": "(date string)", "row format delimited": "", "fields terminated by ','": "", "collection items terminated by '-'": "", "map keys terminated by ':'": "", "store as textfile;": ""}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": true}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": true}, {"refKey": "len", "hideInGraph": true}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id", "defName": "ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "refDict": "", "uiHint": "", "id": "40AA05AF-0F97-4D0F-AC87-336E823C59EC"}, {"defKey": "revision", "defName": "乐观锁", "comment": "数据版本号", "domain": "FB37AF1B-4C9D-42E5-B53E-9FDAE58F2C48", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "31CEB1E4-1AB1-48C5-AD9C-6A5983706244"}, {"defKey": "create_user_id", "defName": "创建人id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "984657A0-C51E-471C-8274-915E2DB7FB0F"}, {"defKey": "create_user_name", "defName": "创建人", "comment": "", "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "71641860-AE2E-4441-B447-527588E28E14"}, {"defKey": "create_dept_id", "defName": "创建部门id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "BF32C6B3-4760-4CD1-88C4-7AA1DF035FAB"}, {"defKey": "create_dept_name", "defName": "创建部门", "comment": "", "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "D4EF0511-92A3-4271-A449-5892DDA20ADD"}, {"defKey": "create_time", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "AB48CFCF-CB20-4DC2-BD10-A7504D399C95"}, {"defKey": "update_user_id", "defName": "修改人id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "FE2CA077-159B-4BA9-871F-98F85BEE2581"}, {"defKey": "update_user_name", "defName": "修改人", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "3FE8EDE7-73BC-48F5-803D-A1C89974CCB7"}, {"defKey": "update_dept_id", "defName": "修改部门id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "6060E2A0-B489-41DA-82FC-E3F168941133"}, {"defKey": "update_dept_name", "defName": "修改部门", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "id": "69B4ABC6-9FEB-4FBE-B4CF-CFA68BE59F46"}, {"defKey": "update_time", "defName": "修改时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "AA11BE40-B35B-42E0-881F-7659CE18A8E7"}, {"defKey": "serial_number", "defName": "序号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "51EF7713-57ED-417E-B09A-5D5051D6E9D6", "id": "1F640E09-6346-4C1E-ABDD-4D0510ED04AE"}, {"defKey": "date_of_shipment", "defName": "发运日期", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "92EB8916-FAC5-4A21-916F-733BE356E5C5"}, {"defKey": "start_place", "defName": "出发地", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "C57903B0-2B70-4CC9-AA80-FA4FBA89C336"}, {"defKey": "air_route", "defName": "航线", "comment": "日期+时间", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "7E9B7965-1E8D-467D-99F8-6973E057EF58"}, {"defKey": "flight_number", "defName": "航班号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "BFC8F5D4-24EC-4E7D-9840-5B189EAED1B7"}, {"defKey": "package_no", "defName": "总包号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "01861B22-193B-4FF3-AB23-660EB38D0FED"}, {"defKey": "bags", "defName": "袋数", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "51EF7713-57ED-417E-B09A-5D5051D6E9D6", "id": "00E16BA4-9E43-43AC-9E0B-A1999BC0CA9B"}, {"defKey": "weight", "defName": "重量", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "FF4459C5-6B45-4DBF-8FC0-E06239BC05B4", "id": "D2AEE0A4-21FA-4B1A-B046-96907C23601C"}, {"defKey": "email_type", "defName": "邮件类型", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "234455ED-4015-488A-97DC-A7F20B3D4842", "id": "1466BFF4-DB06-4708-A7AA-2A638FAD95B9"}, {"defKey": "settlement_price", "defName": "结算单价", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "C3B1681B-99F9-4818-9E80-DE1652A51D85", "id": "0A6A3964-5F75-4F23-BEE7-207F3971763C"}, {"defKey": "settlement_amount", "defName": "结算金额", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "C3B1681B-99F9-4818-9E80-DE1652A51D85", "id": "8F6E8BCA-BA51-432A-A877-8C38CD15E8B5"}, {"defKey": "batch_id", "defName": "批次id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "refDict": "", "extProps": {}, "id": "B72025C1-9330-4B64-916A-6974428A8108"}], "correlations": [], "indexes": []}, {"id": "75A16593-91C0-48E2-BCB8-574B27CEACAB", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "t_air_mail_manual_regular_bills_detail", "defName": "邮件空运手工导入常规账单明细", "comment": "", "properties": {"partitioned by": "(date string)", "row format delimited": "", "fields terminated by ','": "", "collection items terminated by '-'": "", "map keys terminated by ':'": "", "store as textfile;": ""}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": true}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": true}, {"refKey": "len", "hideInGraph": true}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id", "defName": "ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "refDict": "", "uiHint": "", "id": "40AA05AF-0F97-4D0F-AC87-336E823C59EC"}, {"defKey": "revision", "defName": "乐观锁", "comment": "数据版本号", "domain": "FB37AF1B-4C9D-42E5-B53E-9FDAE58F2C48", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "31CEB1E4-1AB1-48C5-AD9C-6A5983706244"}, {"defKey": "create_user_id", "defName": "创建人id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "984657A0-C51E-471C-8274-915E2DB7FB0F"}, {"defKey": "create_user_name", "defName": "创建人", "comment": "", "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "71641860-AE2E-4441-B447-527588E28E14"}, {"defKey": "create_dept_id", "defName": "创建部门id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "BF32C6B3-4760-4CD1-88C4-7AA1DF035FAB"}, {"defKey": "create_dept_name", "defName": "创建部门", "comment": "", "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "D4EF0511-92A3-4271-A449-5892DDA20ADD"}, {"defKey": "create_time", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "AB48CFCF-CB20-4DC2-BD10-A7504D399C95"}, {"defKey": "update_user_id", "defName": "修改人id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "FE2CA077-159B-4BA9-871F-98F85BEE2581"}, {"defKey": "update_user_name", "defName": "修改人", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "3FE8EDE7-73BC-48F5-803D-A1C89974CCB7"}, {"defKey": "update_dept_id", "defName": "修改部门id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "6060E2A0-B489-41DA-82FC-E3F168941133"}, {"defKey": "update_dept_name", "defName": "修改部门", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "id": "69B4ABC6-9FEB-4FBE-B4CF-CFA68BE59F46"}, {"defKey": "update_time", "defName": "修改时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "AA11BE40-B35B-42E0-881F-7659CE18A8E7"}, {"defKey": "send_org", "defName": "原寄局", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "1F640E09-6346-4C1E-ABDD-4D0510ED04AE"}, {"defKey": "date_of_shipment", "defName": "日期", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "92EB8916-FAC5-4A21-916F-733BE356E5C5"}, {"defKey": "air_route", "defName": "路由", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "C57903B0-2B70-4CC9-AA80-FA4FBA89C336"}, {"defKey": "package_no", "defName": "总包", "comment": "日期+时间", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "7E9B7965-1E8D-467D-99F8-6973E057EF58"}, {"defKey": "bags_no", "defName": "袋号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "00E16BA4-9E43-43AC-9E0B-A1999BC0CA9B"}, {"defKey": "weight", "defName": "重量", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "FF4459C5-6B45-4DBF-8FC0-E06239BC05B4", "id": "D2AEE0A4-21FA-4B1A-B046-96907C23601C"}, {"defKey": "email_type", "defName": "邮件类型", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "234455ED-4015-488A-97DC-A7F20B3D4842", "id": "1466BFF4-DB06-4708-A7AA-2A638FAD95B9"}, {"defKey": "bills_id", "defName": "账单id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "refDict": "", "extProps": {}, "id": "B72025C1-9330-4B64-916A-6974428A8108"}], "correlations": [], "indexes": []}, {"id": "65F9894E-BEC5-4E5C-A099-9C9A605AA92D", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "t_port_batch", "defName": "口岸导入批次表", "comment": "", "properties": {"partitioned by": "(date string)", "row format delimited": "", "fields terminated by ','": "", "collection items terminated by '-'": "", "map keys terminated by ':'": "", "store as textfile;": ""}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": true}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": true}, {"refKey": "len", "hideInGraph": true}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id", "defName": "ID", "comment": "批次号", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "refDict": "", "uiHint": "", "id": "FFB5CE74-A708-4B76-9A65-C54C9CFBEF46"}, {"defKey": "revision", "defName": "乐观锁", "comment": "数据版本号", "domain": "FB37AF1B-4C9D-42E5-B53E-9FDAE58F2C48", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "67E99622-D51A-4F6F-B240-04ABC0310549"}, {"defKey": "create_user_id", "defName": "创建人id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "2A9D057F-230F-4AD0-95FA-7E75473847B7"}, {"defKey": "create_user_name", "defName": "创建人", "comment": "", "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "54EE73CB-2BC1-4E9D-9027-2B99BC47EBC8"}, {"defKey": "create_dept_id", "defName": "创建部门id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "06C183AB-47D3-458A-B740-5A9B341D0370"}, {"defKey": "create_dept_name", "defName": "创建部门", "comment": "", "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "844FA14C-4CA4-468B-8B42-9F5C73B9AC78"}, {"defKey": "create_time", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "90AC6505-7500-429B-A5E8-29B98FEED5F4"}, {"defKey": "update_user_id", "defName": "修改人id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "8EA09EB4-E51C-4EAD-B000-E4D2C9FC53DA"}, {"defKey": "update_user_name", "defName": "修改人", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "46043516-470F-4E8B-B9EA-750A08C1AB4B"}, {"defKey": "update_dept_id", "defName": "修改部门id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "4F3F457A-25E6-4F5E-8E3F-CC197AA1298C"}, {"defKey": "update_dept_name", "defName": "修改部门", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "id": "526668B8-2A97-4F98-A186-E867B526000B"}, {"defKey": "update_time", "defName": "修改时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "5AD728C1-90BD-456E-8262-019FBD4A606F"}, {"defKey": "port_id", "defName": "口岸id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "DF6EFD2A-CEA7-4892-A569-07CAD4FF9B82"}, {"defKey": "port_name", "defName": "口岸名称", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "2915B007-42A9-4188-AC7B-EB007C8C5DB3", "id": "D06241F9-5CFA-40EE-9B3C-F5F21F8DCAE7"}, {"defKey": "channel_id", "defName": "渠道商id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "27B4418F-3A9F-4995-BB43-2B8AAE6AF1D8"}, {"defKey": "channel_name", "defName": "渠道商名称", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "2915B007-42A9-4188-AC7B-EB007C8C5DB3", "id": "B1C0A4E7-7233-4DBC-8F58-38B5D5203AF7"}, {"defKey": "bill_num", "defName": "账单数", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "51EF7713-57ED-417E-B09A-5D5051D6E9D6", "id": "E0D4C263-A0B8-47F6-A374-E70235894A20"}, {"defKey": "detail_num", "defName": "明细数", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "51EF7713-57ED-417E-B09A-5D5051D6E9D6", "id": "9F15F115-08BC-4BE2-89E8-8022A0A56D57"}, {"defKey": "settlement_type", "defName": "结算类型", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "A8E47560-709C-4095-B340-CAA9373D5D8C", "extProps": {}, "domain": "BB22DFF1-9A03-4D7C-8098-48734CEE8B5B", "id": "1AEBC71C-C6BE-464D-8578-E8AAD81285FA"}], "correlations": [], "indexes": []}, {"id": "DC1F6D58-C785-4D64-89EA-FA8AA9267BAB", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "t_air_mail_channel_bills_frist_bill", "defName": "邮件空运渠道账单第一邮通账单", "comment": "", "properties": {"partitioned by": "(date string)", "row format delimited": "", "fields terminated by ','": "", "collection items terminated by '-'": "", "map keys terminated by ':'": "", "store as textfile;": ""}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": true}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": true}, {"refKey": "len", "hideInGraph": true}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id", "defName": "ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "refDict": "", "uiHint": "", "id": "40AA05AF-0F97-4D0F-AC87-336E823C59EC"}, {"defKey": "revision", "defName": "乐观锁", "comment": "数据版本号", "domain": "FB37AF1B-4C9D-42E5-B53E-9FDAE58F2C48", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "31CEB1E4-1AB1-48C5-AD9C-6A5983706244"}, {"defKey": "create_user_id", "defName": "创建人id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "984657A0-C51E-471C-8274-915E2DB7FB0F"}, {"defKey": "create_user_name", "defName": "创建人", "comment": "", "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "71641860-AE2E-4441-B447-527588E28E14"}, {"defKey": "create_dept_id", "defName": "创建部门id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "BF32C6B3-4760-4CD1-88C4-7AA1DF035FAB"}, {"defKey": "create_dept_name", "defName": "创建部门", "comment": "", "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "D4EF0511-92A3-4271-A449-5892DDA20ADD"}, {"defKey": "create_time", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "AB48CFCF-CB20-4DC2-BD10-A7504D399C95"}, {"defKey": "update_user_id", "defName": "修改人id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "FE2CA077-159B-4BA9-871F-98F85BEE2581"}, {"defKey": "update_user_name", "defName": "修改人", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "3FE8EDE7-73BC-48F5-803D-A1C89974CCB7"}, {"defKey": "update_dept_id", "defName": "修改部门id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "6060E2A0-B489-41DA-82FC-E3F168941133"}, {"defKey": "update_dept_name", "defName": "修改部门", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "id": "69B4ABC6-9FEB-4FBE-B4CF-CFA68BE59F46"}, {"defKey": "update_time", "defName": "修改时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "AA11BE40-B35B-42E0-881F-7659CE18A8E7"}, {"defKey": "serial_number", "defName": "序号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "51EF7713-57ED-417E-B09A-5D5051D6E9D6", "id": "1F640E09-6346-4C1E-ABDD-4D0510ED04AE"}, {"defKey": "date_of_shipment", "defName": "发运日期", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "refDict": "", "extProps": {}, "id": "B2047EFA-9380-4033-9F8A-1EC4446DC3B6"}, {"defKey": "start_place", "defName": "出发地", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "refDict": "", "extProps": {}, "id": "A37346F0-627A-40B1-9787-90296DA84CFB"}, {"defKey": "air_route", "defName": "航线", "comment": "日期+时间", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "7E9B7965-1E8D-467D-99F8-6973E057EF58"}, {"defKey": "package_no", "defName": "总包号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "01861B22-193B-4FF3-AB23-660EB38D0FED"}, {"defKey": "bags", "defName": "袋数", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "51EF7713-57ED-417E-B09A-5D5051D6E9D6", "id": "00E16BA4-9E43-43AC-9E0B-A1999BC0CA9B"}, {"defKey": "flight_number", "defName": "航班号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "refDict": "", "extProps": {}, "id": "84E22F72-B89C-41F9-A7C6-DBF6ED82288F"}, {"defKey": "weight", "defName": "重量", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "FF4459C5-6B45-4DBF-8FC0-E06239BC05B4", "id": "D2AEE0A4-21FA-4B1A-B046-96907C23601C"}, {"defKey": "email_type", "defName": "邮件类型", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "234455ED-4015-488A-97DC-A7F20B3D4842", "id": "1466BFF4-DB06-4708-A7AA-2A638FAD95B9"}, {"defKey": "settlement_price", "defName": "结算单价", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "C3B1681B-99F9-4818-9E80-DE1652A51D85", "id": "0A6A3964-5F75-4F23-BEE7-207F3971763C"}, {"defKey": "settlement_amount", "defName": "结算金额", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "C3B1681B-99F9-4818-9E80-DE1652A51D85", "id": "8F6E8BCA-BA51-432A-A877-8C38CD15E8B5"}, {"defKey": "batch_id", "defName": "批次id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "refDict": "", "extProps": {}, "id": "B72025C1-9330-4B64-916A-6974428A8108"}, {"defKey": "port_id", "defName": "口岸id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "refDict": "", "extProps": {}, "id": "FB8001A7-9A08-4DD7-A710-8F280C038EBD"}, {"defKey": "port_name", "defName": "口岸名称", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "2915B007-42A9-4188-AC7B-EB007C8C5DB3", "refDict": "", "extProps": {}, "id": "F2DFE8BA-9761-493C-A059-35708FB507F1"}, {"defKey": "channel_id", "defName": "渠道商id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "refDict": "", "extProps": {}, "id": "6BD272A3-98AF-401E-A247-9F68062F46AF"}, {"defKey": "channel_name", "defName": "渠道商名称", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "2915B007-42A9-4188-AC7B-EB007C8C5DB3", "refDict": "", "extProps": {}, "id": "67202611-7D8A-4972-9FC3-CD3109053268"}], "correlations": [], "indexes": []}, {"id": "2510901D-F14D-461A-950B-67B58C454CD7", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "t_air_mail_channel_bills_frist_detail", "defName": "邮件空运渠道账单第一邮通明细", "comment": "", "properties": {"partitioned by": "(date string)", "row format delimited": "", "fields terminated by ','": "", "collection items terminated by '-'": "", "map keys terminated by ':'": "", "store as textfile;": ""}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": true}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": true}, {"refKey": "len", "hideInGraph": true}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id", "defName": "ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "refDict": "", "uiHint": "", "id": "40AA05AF-0F97-4D0F-AC87-336E823C59EC"}, {"defKey": "revision", "defName": "乐观锁", "comment": "数据版本号", "domain": "FB37AF1B-4C9D-42E5-B53E-9FDAE58F2C48", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "31CEB1E4-1AB1-48C5-AD9C-6A5983706244"}, {"defKey": "create_user_id", "defName": "创建人id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "984657A0-C51E-471C-8274-915E2DB7FB0F"}, {"defKey": "create_user_name", "defName": "创建人", "comment": "", "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "71641860-AE2E-4441-B447-527588E28E14"}, {"defKey": "create_dept_id", "defName": "创建部门id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "BF32C6B3-4760-4CD1-88C4-7AA1DF035FAB"}, {"defKey": "create_dept_name", "defName": "创建部门", "comment": "", "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "D4EF0511-92A3-4271-A449-5892DDA20ADD"}, {"defKey": "create_time", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "AB48CFCF-CB20-4DC2-BD10-A7504D399C95"}, {"defKey": "update_user_id", "defName": "修改人id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "FE2CA077-159B-4BA9-871F-98F85BEE2581"}, {"defKey": "update_user_name", "defName": "修改人", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "3FE8EDE7-73BC-48F5-803D-A1C89974CCB7"}, {"defKey": "update_dept_id", "defName": "修改部门id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "6060E2A0-B489-41DA-82FC-E3F168941133"}, {"defKey": "update_dept_name", "defName": "修改部门", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "id": "69B4ABC6-9FEB-4FBE-B4CF-CFA68BE59F46"}, {"defKey": "update_time", "defName": "修改时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "AA11BE40-B35B-42E0-881F-7659CE18A8E7"}, {"defKey": "serial_number", "defName": "序号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "51EF7713-57ED-417E-B09A-5D5051D6E9D6", "refDict": "", "extProps": {}, "id": "F4900A92-7A14-4A16-A497-C647E7C8C99C"}, {"defKey": "package_no", "defName": "总包", "comment": "日期+时间", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "7E9B7965-1E8D-467D-99F8-6973E057EF58"}, {"defKey": "bags_no", "defName": "袋号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "00E16BA4-9E43-43AC-9E0B-A1999BC0CA9B"}, {"defKey": "send_org", "defName": "原寄局", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "refDict": "", "extProps": {}, "id": "52C69DA3-4027-4EDB-941D-306C95C41D96"}, {"defKey": "date_of_shipment", "defName": "日期", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "refDict": "", "extProps": {}, "id": "5FFBAF90-9E8A-46D8-9E18-34B6A2FEA167"}, {"defKey": "air_route", "defName": "路由", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "C57903B0-2B70-4CC9-AA80-FA4FBA89C336"}, {"defKey": "weight", "defName": "重量", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "FF4459C5-6B45-4DBF-8FC0-E06239BC05B4", "id": "D2AEE0A4-21FA-4B1A-B046-96907C23601C"}, {"defKey": "email_type", "defName": "邮件类型", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "234455ED-4015-488A-97DC-A7F20B3D4842", "refDict": "", "extProps": {}, "id": "1FD013BE-94AC-4639-ABC4-2D927C9EBD19"}, {"defKey": "barcode", "defName": "条码", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "id": "1466BFF4-DB06-4708-A7AA-2A638FAD95B9"}, {"defKey": "bills_id", "defName": "账单id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "refDict": "", "extProps": {}, "id": "B72025C1-9330-4B64-916A-6974428A8108"}], "correlations": [], "indexes": []}, {"id": "F7233D85-57D5-4847-9FB9-09CF5B6956B7", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "t_routing", "defName": "路由表", "comment": "", "properties": {"partitioned by": "(date string)", "row format delimited": "", "fields terminated by ','": "", "collection items terminated by '-'": "", "map keys terminated by ':'": "", "store as textfile;": ""}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": true}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": true}, {"refKey": "len", "hideInGraph": true}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id", "defName": "ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": true, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "refDict": "", "uiHint": "", "id": "6A395AC7-6A5B-4251-8E6B-B8F51B195B48"}, {"defKey": "revision", "defName": "乐观锁", "comment": "数据版本号", "domain": "FB37AF1B-4C9D-42E5-B53E-9FDAE58F2C48", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "1793E26D-1F29-4D5C-8B67-B9289921DE2B"}, {"defKey": "create_user_id", "defName": "创建人id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "uiHint": "", "id": "9EDA6A09-0E41-4408-8956-756BA3D06205"}, {"defKey": "create_user_name", "defName": "创建人", "comment": "", "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "440D0E19-C25B-40A0-935B-758384F15671"}, {"defKey": "create_dept_id", "defName": "创建部门id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "FDDE167A-C643-4BD3-9EAF-20B7D7F25FF4"}, {"defKey": "create_dept_name", "defName": "创建部门", "comment": "", "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "F366D8A5-C2D2-45BF-B936-F14F008285DC"}, {"defKey": "create_time", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "F24C5033-011D-4311-911E-D00735CB92D0"}, {"defKey": "update_user_id", "defName": "修改人id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "6439C1A2-CD8D-440C-AE88-F35AA715D9F2"}, {"defKey": "update_user_name", "defName": "修改人", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "9D763E60-22AA-4E0E-AC04-7230347A47A1"}, {"defKey": "update_dept_id", "defName": "修改部门id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "1E0EC1F0-C2F9-4700-91CA-40F5513B1191"}, {"defKey": "update_dept_name", "defName": "修改部门", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "id": "0BB814B2-FB24-4191-B01C-CE04F47A15F6"}, {"defKey": "update_time", "defName": "修改时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "9DF796C8-BBAA-4DCA-B9EC-A7B39658927B"}, {"defKey": "routing_plan", "defName": "路由计划", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "7258A239-7014-49B5-B266-17DE88875864"}, {"defKey": "first_flight_number", "defName": "首航航班号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "234455ED-4015-488A-97DC-A7F20B3D4842", "id": "14CF9812-6CC0-4795-B909-2102FC5AA739"}, {"defKey": "first_resumed_flight", "defName": "第一续运航班航班号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "234455ED-4015-488A-97DC-A7F20B3D4842", "id": "A83F61B5-86B2-4D63-9A3E-FCF5D4D8D83D"}, {"defKey": "second_resumed_flight", "defName": "第二续运航班航班号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "234455ED-4015-488A-97DC-A7F20B3D4842", "id": "91BCF55D-C551-467D-9F1A-85374108AFFF"}, {"defKey": "send_port_code", "defName": "发运口岸代码", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "234455ED-4015-488A-97DC-A7F20B3D4842", "id": "E76A4832-8C5F-41D7-988A-1099946B5E3D"}, {"defKey": "send_port_name", "defName": "发运口岸", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "id": "1D17E649-8EEC-4EB0-809E-023C500DB02E"}, {"defKey": "reach_national", "defName": "寄达国家", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "id": "8A81ED70-9EBF-4E48-9F9C-FE5B74B53335"}, {"defKey": "arrival_port_code", "defName": "寄达口岸代码", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "234455ED-4015-488A-97DC-A7F20B3D4842", "id": "3EF26E09-CD83-4493-B65F-E05AE169D595"}, {"defKey": "arrival_port_name", "defName": "寄达互换局", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "id": "06597BD0-9EE9-482D-A2CF-F74F14E2B8B8"}, {"defKey": "supplier", "defName": "供应商", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "2915B007-42A9-4188-AC7B-EB007C8C5DB3", "id": "89F5CAF0-470C-491A-B84D-589833A60E97"}], "correlations": [], "indexes": []}, {"id": "5C33F8CC-B1FC-4AB6-8EE2-BC0CEE343E07", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "t_port", "defName": "口岸表", "comment": "", "properties": {"partitioned by": "(date string)", "row format delimited": "", "fields terminated by ','": "", "collection items terminated by '-'": "", "map keys terminated by ':'": "", "store as textfile;": ""}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": true}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": true}, {"refKey": "len", "hideInGraph": true}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id", "defName": "ID", "comment": "批次号", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "refDict": "", "uiHint": "", "id": "7DA48D55-DBE1-4517-979A-C12ACD36BAE2"}, {"defKey": "revision", "defName": "乐观锁", "comment": "数据版本号", "domain": "FB37AF1B-4C9D-42E5-B53E-9FDAE58F2C48", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "55992DCC-6378-4B00-9EE6-0D4751728A4C"}, {"defKey": "create_user_id", "defName": "创建人id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "14B91167-8BB2-4938-94D0-62C017247643"}, {"defKey": "create_dept_id", "defName": "创建部门id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "BCC8997B-8BB8-455B-BB68-C30BEBE84DAD"}, {"defKey": "create_user_name", "defName": "创建人", "comment": "", "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "F1BBBEEE-0D11-40F0-81E4-01740FC62944"}, {"defKey": "create_dept_name", "defName": "创建部门", "comment": "", "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "78F0E425-355F-40B9-BB43-D02FB7FA27E6"}, {"defKey": "create_time", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "97CC91BD-566A-400F-A6D1-1DC341B4BA11"}, {"defKey": "update_user_id", "defName": "修改人id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "3016636E-BC6B-45EC-9B46-7172EDB70634"}, {"defKey": "update_user_name", "defName": "修改人", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "1C36ACF2-47F8-4C90-A8ED-F768719E1C4B"}, {"defKey": "update_dept_id", "defName": "修改部门id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "130C9EB3-E211-4CE7-9C3C-C4B94C3F1F9A"}, {"defKey": "update_dept_name", "defName": "修改部门", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "id": "7DDFEFDD-F002-49D3-BB00-919C80468693"}, {"defKey": "update_time", "defName": "修改时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "82949628-EC9B-4348-BC52-C60E01A5D142"}, {"defKey": "port_id", "defName": "口岸id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "EB877C56-5C1B-4DD2-A652-F1AB917FDF2E"}, {"defKey": "port_name", "defName": "口岸名称", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "2915B007-42A9-4188-AC7B-EB007C8C5DB3", "id": "5CF97A13-F0F1-4EAD-8D32-5B33C99E33B4"}, {"defKey": "national", "defName": "口岸国家", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "refDict": "", "extProps": {}, "id": "29F81A0F-8FE1-4720-A158-5D0798358F36"}], "correlations": [], "indexes": []}, {"id": "4A2A31D6-F5F7-4257-AB84-FB6A5C3A5AC1", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "t_channel_price", "defName": "渠道价格表", "comment": "", "properties": {"partitioned by": "(date string)", "row format delimited": "", "fields terminated by ','": "", "collection items terminated by '-'": "", "map keys terminated by ':'": "", "store as textfile;": ""}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": true}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": true}, {"refKey": "len", "hideInGraph": true}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id", "defName": "ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "refDict": "", "uiHint": "", "id": "0F3854A6-0819-448E-98FD-F751570AA32D"}, {"defKey": "revision", "defName": "乐观锁", "comment": "数据版本号", "domain": "FB37AF1B-4C9D-42E5-B53E-9FDAE58F2C48", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "BB836BE6-F360-406D-9994-FE8179370222"}, {"defKey": "create_user_id", "defName": "创建人id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "50708A0F-4FBA-4929-86CF-5E11E0697647"}, {"defKey": "create_user_name", "defName": "创建人", "comment": "", "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "29E714E2-39FB-42C6-B3D7-815EA6DB6048"}, {"defKey": "create_dept_id", "defName": "创建部门id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "4A863B0E-AE89-4B0B-A7B3-CD2CFDF3AD9F"}, {"defKey": "create_dept_name", "defName": "创建部门", "comment": "", "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "4CAFA875-1EC0-40A0-A0FE-D82FAB01DD8B"}, {"defKey": "create_time", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "54DDB116-88F0-4965-A048-9FE83D0B9EE9"}, {"defKey": "update_user_id", "defName": "修改人id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "C4DDE2D4-2D1B-4A24-AA3F-45896AD200BC"}, {"defKey": "update_user_name", "defName": "修改人", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "DD5406BF-D77C-4535-A280-8C71767403CD"}, {"defKey": "update_dept_id", "defName": "修改部门id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "513F6935-96F2-45DF-9A15-38483B7DABFC"}, {"defKey": "update_dept_name", "defName": "修改部门", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "id": "D631D98E-936E-49B4-859F-90ED939BB2CF"}, {"defKey": "update_time", "defName": "修改时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "45B0A2DF-8442-4046-90A6-A5F139D513B0"}, {"defKey": "port_id", "defName": "口岸id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "refDict": "8BD5BF63-42AF-48AD-94D0-351EB86BEC1A", "extProps": {}, "id": "221C7FD6-B2F6-417D-8C1F-3702BDAFBB83"}, {"defKey": "port_name", "defName": "口岸名称", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "2915B007-42A9-4188-AC7B-EB007C8C5DB3", "refDict": "", "extProps": {}, "id": "EEE8D804-4405-4D20-BD6B-57CDDB3D2934"}, {"defKey": "national", "defName": "口岸国家", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "refDict": "", "extProps": {}, "id": "784454A9-B13F-4034-861D-BBC82C49D2DF"}, {"defKey": "flight_number", "defName": "航班号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "refDict": "", "extProps": {}, "id": "D8F8984D-C7CF-40CE-A5ED-F7D70A08CAA7"}, {"defKey": "email_type", "defName": "邮件类型", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "234455ED-4015-488A-97DC-A7F20B3D4842", "refDict": "DD615237-E4AC-4DB6-B483-69204140B254", "extProps": {}, "id": "75E58C35-A644-4AF1-BBF2-919EB9FE0560"}, {"defKey": "effective_start_date", "defName": "生效开始日期", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "3906540D-7570-426E-9D48-7CE6925E3D38"}, {"defKey": "effective_end_date", "defName": "生效结束日期", "comment": "最新记录生效结束时间为空", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "9D370C1F-27D3-48AA-A052-07B28AADFD14"}, {"defKey": "shipping_price", "defName": "发运费单价", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "C3B1681B-99F9-4818-9E80-DE1652A51D85", "refDict": "", "extProps": {}, "id": "F0607C45-AEE3-4960-938B-266BDBAFC9A8"}, {"defKey": "add_price", "defName": "附加费单价", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "C3B1681B-99F9-4818-9E80-DE1652A51D85", "id": "4CC03A31-85AE-4BB8-AFB2-F3F50A030387"}], "correlations": [], "indexes": []}, {"id": "336583BD-8C39-4844-98D6-ED209644F3A3", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "t_manual_price", "defName": "常规价格表", "comment": "", "properties": {"partitioned by": "(date string)", "row format delimited": "", "fields terminated by ','": "", "collection items terminated by '-'": "", "map keys terminated by ':'": "", "store as textfile;": ""}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": true}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": true}, {"refKey": "len", "hideInGraph": true}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id", "defName": "ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": false, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "refDict": "", "uiHint": "", "id": "3B4ECDA9-B6F7-4F13-80B3-E55C02DB2333"}, {"defKey": "revision", "defName": "乐观锁", "comment": "数据版本号", "domain": "FB37AF1B-4C9D-42E5-B53E-9FDAE58F2C48", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "922F5600-15DC-409F-A9D7-27CF14CF9570"}, {"defKey": "create_user_id", "defName": "创建人id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "F43646AE-54A1-4831-A135-93DB942A9269"}, {"defKey": "create_user_name", "defName": "创建人", "comment": "", "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "AEADBDB0-5CAC-4202-9AC9-CC5305F8943F"}, {"defKey": "create_dept_id", "defName": "创建部门id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "AD160838-75EB-441C-AF3A-C0AB98F64EEE"}, {"defKey": "create_dept_name", "defName": "创建部门", "comment": "", "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "2ACFAAAE-B8FE-43F8-A2AE-CF959639C239"}, {"defKey": "create_time", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "EEC4084F-B1AA-4EC5-B025-F59982095D02"}, {"defKey": "update_user_id", "defName": "修改人id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "DFA3F03C-9DAE-4673-A71E-8DB553E8620E"}, {"defKey": "update_user_name", "defName": "修改人", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "7FDCC28E-4192-4337-94E8-96FFE1656A02"}, {"defKey": "update_dept_id", "defName": "修改部门id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "BDD8EC18-6C42-4876-9CF1-7CD388C1C520"}, {"defKey": "update_dept_name", "defName": "修改部门", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "id": "87599DF6-557D-4A5A-A032-AEA84AA1A759"}, {"defKey": "update_time", "defName": "修改时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "A78FB69A-5DA6-4F51-9EA4-778B51A27F6E"}, {"defKey": "port_id", "defName": "口岸id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "refDict": "8BD5BF63-42AF-48AD-94D0-351EB86BEC1A", "extProps": {}, "id": "D2D5F196-2E6A-4979-9DDE-B3CFF60505D7"}, {"defKey": "port_name", "defName": "口岸名称", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "2915B007-42A9-4188-AC7B-EB007C8C5DB3", "refDict": "", "extProps": {}, "id": "94A1C14B-9553-4EC3-8B53-F940B6EC3DD1"}, {"defKey": "national", "defName": "口岸国家", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "refDict": "", "extProps": {}, "id": "AFC78840-1FA9-4513-A71F-03103C141022"}, {"defKey": "flight_number", "defName": "航班号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "refDict": "", "extProps": {}, "id": "5BF03895-A74D-4697-A452-59DC42A19304"}, {"defKey": "email_type", "defName": "邮件类型", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "234455ED-4015-488A-97DC-A7F20B3D4842", "refDict": "DD615237-E4AC-4DB6-B483-69204140B254", "extProps": {}, "id": "72E2AF7D-D1B8-4C3D-9637-2070B3EDAD4B"}, {"defKey": "effective_start_date", "defName": "生效开始日期", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "refDict": "", "extProps": {}, "id": "3074B14C-26D0-4F22-A776-D7265D808041"}, {"defKey": "effective_end_date", "defName": "生效结束日期", "comment": "最新记录生效结束时间为空", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "refDict": "", "extProps": {}, "id": "D5CE50B2-82C2-4388-AB18-5AC6685F46B4"}, {"defKey": "price", "defName": "单价", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "C3B1681B-99F9-4818-9E80-DE1652A51D85", "refDict": "", "extProps": {}, "id": "2A2AB827-CB49-4BFB-801D-29A937E00C40"}], "correlations": [], "indexes": []}, {"id": "EE9AF48F-7B39-4F07-B72B-C60131F4CCDF", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "t_orders_tms", "defName": "TMS订单表", "comment": "", "properties": {"partitioned by": "(date string)", "row format delimited": "", "fields terminated by ','": "", "collection items terminated by '-'": "", "map keys terminated by ':'": "", "store as textfile;": ""}, "nameTemplate": "{defKey}[{defName}]", "notes": {}, "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": true}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": true}, {"refKey": "len", "hideInGraph": true}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id", "defName": "ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": true, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "refDict": "", "uiHint": "", "id": "1EDBCB97-AF38-4353-81B9-0335D18D5942"}, {"defKey": "revision", "defName": "乐观锁", "comment": "数据版本号", "domain": "FB37AF1B-4C9D-42E5-B53E-9FDAE58F2C48", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "D3FF4A35-3272-4852-AD88-1CF18480E3B2"}, {"defKey": "create_user_id", "defName": "创建人id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "5756A765-1E5F-454A-ACC6-00DA729846CE"}, {"defKey": "create_user_name", "defName": "创建人", "comment": "", "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "AC155225-E7D5-4763-9990-F542466FEF74"}, {"defKey": "create_dept_id", "defName": "创建部门id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "1CA4757F-5DC4-4E98-8A79-E49626070435"}, {"defKey": "create_dept_name", "defName": "创建部门", "comment": "", "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "148FC072-DE3A-46AB-817F-D036AF2D6144"}, {"defKey": "create_time", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "3210F68D-6BC3-43C6-8F65-D14CA4882DF9"}, {"defKey": "update_user_id", "defName": "修改人id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "2B682BBB-B873-49E5-AC21-2DDF742AC3B9"}, {"defKey": "update_user_name", "defName": "修改人", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "904208D2-1502-4D8A-A4A5-2BD2AC8AFB27"}, {"defKey": "update_dept_id", "defName": "修改部门id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "D1446C4D-1D1E-4ABB-88B9-D7EDDAB128D4"}, {"defKey": "update_dept_name", "defName": "修改部门", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "id": "63475206-8FB6-4CDC-A084-9F5D79B42388"}, {"defKey": "update_time", "defName": "修改时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "C546B09D-70D0-4B7A-BBAC-C16E8D6984A6"}, {"defKey": "shipper_hawbcode", "defName": "运单号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "id": "452D291E-FC5E-42DC-B46B-7E641528E3EC"}, {"defKey": "customer_code", "defName": "客户代码", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "id": "EF44D519-4A0E-44C4-9E7E-69877F3235D0"}, {"defKey": "customer_shortname", "defName": "客户简称", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "2915B007-42A9-4188-AC7B-EB007C8C5DB3", "id": "5DBF8D69-8A4C-4F88-B0C4-068BD4C90999"}, {"defKey": "customer_allname", "defName": "客户全称", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "CF8680CD-7A15-4336-ABD1-C64D6CA2FCCC", "id": "8F756458-DF92-4807-B59D-78D6729A061D"}, {"defKey": "product_cnname", "defName": "销售产品名称", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "2915B007-42A9-4188-AC7B-EB007C8C5DB3", "id": "B0371FE0-D0F8-4C4C-90EF-75432203FA6E"}, {"defKey": "country_cnname", "defName": "目的国家(中文)", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "2915B007-42A9-4188-AC7B-EB007C8C5DB3", "id": "203340F8-B40F-404D-BCE9-053062D3043D"}, {"defKey": "server_allname", "defName": "服务商中文名称", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "CF8680CD-7A15-4336-ABD1-C64D6CA2FCCC", "id": "1E97E42B-853D-495A-96E8-A37E391C055C"}, {"defKey": "server_channel_cnname", "defName": "服务渠道名称", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "CF8680CD-7A15-4336-ABD1-C64D6CA2FCCC", "id": "3C06A7AC-62D5-4910-89C3-0BD88E3E8AB4"}, {"defKey": "arrival_date", "defName": "到货时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "3CF0A0D7-13A6-45C6-89F2-22A288A0259F"}, {"defKey": "checkin_date", "defName": "签入时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "5330D8F4-32EC-492A-9BFE-D0BCF99CD385"}, {"defKey": "checkout_date", "defName": "签出时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "9631A32F-6D8F-4B2C-A432-1DA30B02BE7D"}, {"defKey": "all_invoice_cnname", "defName": "申报品名(中文汇总)", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "CF8680CD-7A15-4336-ABD1-C64D6CA2FCCC", "id": "422BC28C-A473-4F47-97BA-B484DB191A32"}, {"defKey": "pieces", "defName": "件数", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "51EF7713-57ED-417E-B09A-5D5051D6E9D6", "id": "A63D364A-5856-4B51-B492-BAA08A59C248"}, {"defKey": "checkin_grossweight", "defName": "收货实重", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "FF4459C5-6B45-4DBF-8FC0-E06239BC05B4", "id": "689801B8-E708-42BE-B075-DAA0B525A5D4"}, {"defKey": "checkin_volumeweight", "defName": "收货材积重", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "FF4459C5-6B45-4DBF-8FC0-E06239BC05B4", "id": "C7DEC13E-CDAB-4239-93A3-7E90A031B26D"}, {"defKey": "shipper_weight", "defName": "客户重量", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "FF4459C5-6B45-4DBF-8FC0-E06239BC05B4", "id": "DCE68BE4-9F67-4BA5-9B36-6C08AB145006"}, {"defKey": "shipper_chargeweight", "defName": "收货计费重", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "FF4459C5-6B45-4DBF-8FC0-E06239BC05B4", "id": "F6A840AC-DFF0-4471-8F64-2D42792A1CFB"}, {"defKey": "server_chargeweight", "defName": "服务商重量", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "FF4459C5-6B45-4DBF-8FC0-E06239BC05B4", "id": "EA7FB057-A481-4CF1-9B12-7E066ACDA174"}, {"defKey": "rmb_fee", "defName": "运费金额(RMB)", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "C3B1681B-99F9-4818-9E80-DE1652A51D85", "id": "D92511DA-5F1B-4A7A-A127-3C87D7803E88"}, {"defKey": "rmb_other_fee", "defName": "其他费用(RMB)", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "C3B1681B-99F9-4818-9E80-DE1652A51D85", "id": "49E18C46-744E-4C4B-869F-C426EDB9E933"}, {"defKey": "rmb_total_receivable_fee", "defName": "总应收金额(RMB)", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "C3B1681B-99F9-4818-9E80-DE1652A51D85", "id": "64B532AA-CDC7-475B-817F-EA801FE104BA"}], "correlations": [], "indexes": []}, {"id": "5FB960C2-A180-4CD8-8156-57641CD4AD61", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "t_order_delivery", "defName": "投递订单表", "comment": "", "properties": {"partitioned by": "(date string)", "row format delimited": "", "fields terminated by ','": "", "collection items terminated by '-'": "", "map keys terminated by ':'": "", "store as textfile;": ""}, "nameTemplate": "{defKey}[{defName}]", "notes": {}, "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": true}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": true}, {"refKey": "len", "hideInGraph": true}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id", "defName": "ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": true, "defaultValue": "", "hideInGraph": true, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "refDict": "", "uiHint": "", "id": "73DB6A31-4903-4663-B079-D0AAB0A3A053"}, {"defKey": "revision", "defName": "乐观锁", "comment": "数据版本号", "domain": "FB37AF1B-4C9D-42E5-B53E-9FDAE58F2C48", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "1D1DE6F6-8712-446A-B34F-294C61C07490"}, {"defKey": "create_user_id", "defName": "创建人id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "194F4C9E-5589-464D-BE5C-A04616A1F566"}, {"defKey": "create_user_name", "defName": "创建人", "comment": "", "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "2A8583DB-CC4C-40DE-98A7-AEA136E8F605"}, {"defKey": "create_dept_id", "defName": "创建部门id", "comment": "", "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "9DC84C67-1983-417A-9BAC-27E48FB83666"}, {"defKey": "create_dept_name", "defName": "创建部门", "comment": "", "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "uiHint": "", "id": "DF4A8639-373A-447A-8875-7F02CCB17ED2"}, {"defKey": "create_time", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "4C1CE8CB-872D-44DD-B6BD-CBEEA5EDFB3C"}, {"defKey": "update_user_id", "defName": "修改人id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "C0B482AA-ED36-4DB7-ADE1-D6C56D4A8C4A"}, {"defKey": "update_user_name", "defName": "修改人", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "9436D16A-3F13-4621-AEE5-FE313F332EDE"}, {"defKey": "update_dept_id", "defName": "修改部门id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0", "id": "611DD9EE-59CE-4D7D-982B-232A6E3E4C45"}, {"defKey": "update_dept_name", "defName": "修改部门", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "FDA237FE-B582-4DE0-89EB-9724C546642C", "id": "2F93BF10-F377-48C6-8281-231146E21D46"}, {"defKey": "update_time", "defName": "修改时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "FFD9C7A2-E3D1-4262-A7E1-8114446617CB"}, {"defKey": "tracking_no", "defName": "订单号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "1570D55B-1E80-4FF0-A9A8-F692207A9644"}, {"defKey": "received_time", "defName": "抵港时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "BDB1C6D0-C396-4EE1-8289-15E0DBFBBEFC"}, {"defKey": "fee_weight", "defName": "计费重量", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "FF4459C5-6B45-4DBF-8FC0-E06239BC05B4", "id": "B2BF65B4-4A32-4267-8A4E-AF026EFDAC08"}, {"defKey": "delivery_site_no", "defName": "投递site编号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "58BCFC15-C105-48FD-9C2E-A55DFBA5750B"}, {"defKey": "success_delivery_time", "defName": "成功投递时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "id": "40D654AE-0F56-4122-8AD4-BFBD2A4E049F"}, {"defKey": "status", "defName": "状态", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B385F062-24F5-4F2F-AB91-00A20FCC8E32", "id": "8DACBA07-9405-48A7-9FA6-53AB6AE79B16"}], "correlations": [], "indexes": []}], "views": [], "dicts": [{"defKey": "ModuleType", "defName": "模板类型", "sort": "", "intro": "", "id": "B0126C1B-839C-42C5-8FB8-F77C90B98896", "items": [{"defKey": "01", "defName": "商业出口手工", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "B9DF55AF-54BF-4A9D-A751-9B5259D16004"}, {"defKey": "02", "defName": "商业出口新一代", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "36683A0E-97C7-4841-96A1-8E3119CBA5A5"}]}, {"defKey": "Port", "defName": "口岸", "sort": "", "intro": "", "id": "8BD5BF63-42AF-48AD-94D0-351EB86BEC1A", "items": [{"defKey": "CAN", "defName": "广州", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "2323E11D-807E-47A8-BC49-D455CB8636D6"}, {"defKey": "DGG", "defName": "东莞", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "A4749113-4100-4A63-9D62-9AA975CEB8FF"}, {"defKey": "ZUH", "defName": "珠海", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "37F6CD05-EAD0-487D-8A11-BFF9E3BEC6AA"}, {"defKey": "SZX", "defName": "深圳", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "CE2DD728-1349-4766-9648-66D086356F6D"}]}, {"defKey": "EmailType", "defName": "邮件类型", "sort": "", "intro": "", "id": "DD615237-E4AC-4DB6-B483-69204140B254", "items": [{"defKey": "01", "defName": "EMS", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "092700F1-AFAA-40CE-AA73-8C929F8B468A"}, {"defKey": "02", "defName": "E邮宝", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "EF6CBB3F-EEE4-4554-81B3-0B0742E21B98"}, {"defKey": "03", "defName": "航空函件", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "B1EBB6D1-5A93-4A01-8572-365419F67CFE"}, {"defKey": "04", "defName": "包裹", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "7287F7A4-9864-444E-8B4C-D63155C06270"}, {"defKey": "05", "defName": "水陆", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "706FEE05-E3E6-4DE6-ACE0-D7720322E03C"}]}, {"defKey": "SettlementType", "defName": "结算类型", "sort": "", "intro": "", "id": "A8E47560-709C-4095-B340-CAA9373D5D8C", "items": [{"defKey": "1", "defName": "黄包", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "FED3A2B9-0CE8-4E5A-ACBA-8E7C3FF03097"}, {"defKey": "2", "defName": "蓝包", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "053CF0A2-1CCA-448B-8941-7FBCA09294EE"}]}], "viewGroups": [], "dataTypeMapping": {"referURL": "", "mappings": [{"defKey": "char", "defName": "字符", "id": "88F25C57-11EC-427B-BC13-571557245EB5", "29D1CE08-4C35-4D2D-AAA9-23D93305B52E": "CHAR", "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542": "CHAR", "BFC87171-C74F-494A-B7C2-76B9C55FACC9": "CHAR", "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022": "CHAR", "89504F5D-94BF-4C9E-8B2E-44F37305FED5": "CHAR", "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307": "CHAR", "592C7013-143D-4E7B-AF64-0D7BF1E28230": "CHAR", "77BD85E5-9D0D-4096-8427-CBA306FC9C6A": "CHAR", "11D1FB71-A587-4217-89BA-611B8A1F83E0": "STRING", "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1": "TEXT", "797A1496-D649-4261-89B4-544132EC3F36": "String", "895CFD1D-4273-4D32-A2C4-CAC70200AB5B": "String", "A2EE7B4A-CE62-4290-B00C-B26C1BF18073": "String", "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30": "string", "B91D99E0-9B7C-416C-8737-B760957DAF09": "String"}, {"defKey": "string", "defName": "字串", "id": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "29D1CE08-4C35-4D2D-AAA9-23D93305B52E": "VARCHAR", "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542": "VARCHAR2", "BFC87171-C74F-494A-B7C2-76B9C55FACC9": "VARCHAR", "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022": "VARCHAR", "89504F5D-94BF-4C9E-8B2E-44F37305FED5": "VARCHAR", "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307": "VARCHAR2", "592C7013-143D-4E7B-AF64-0D7BF1E28230": "VARCHAR", "77BD85E5-9D0D-4096-8427-CBA306FC9C6A": "VARCHAR", "11D1FB71-A587-4217-89BA-611B8A1F83E0": "STRING", "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1": "TEXT", "797A1496-D649-4261-89B4-544132EC3F36": "String", "895CFD1D-4273-4D32-A2C4-CAC70200AB5B": "String", "A2EE7B4A-CE62-4290-B00C-B26C1BF18073": "String", "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30": "string", "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2": "string", "B91D99E0-9B7C-416C-8737-B760957DAF09": "string"}, {"defKey": "double", "defName": "小数", "id": "1A0BDC09-0792-4174-9E8E-80BE8DF44B8E", "29D1CE08-4C35-4D2D-AAA9-23D93305B52E": "DECIMAL", "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542": "DECIMAL", "BFC87171-C74F-494A-B7C2-76B9C55FACC9": "DECIMAL", "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022": "NUMERIC", "89504F5D-94BF-4C9E-8B2E-44F37305FED5": "DECIMAL", "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307": "DECIMAL", "592C7013-143D-4E7B-AF64-0D7BF1E28230": "NUMERIC", "77BD85E5-9D0D-4096-8427-CBA306FC9C6A": "NUMERIC", "11D1FB71-A587-4217-89BA-611B8A1F83E0": "DOUBLE", "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1": "REAL", "797A1496-D649-4261-89B4-544132EC3F36": "Double", "895CFD1D-4273-4D32-A2C4-CAC70200AB5B": "Double", "A2EE7B4A-CE62-4290-B00C-B26C1BF18073": "Double", "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30": "decimal", "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2": "double", "B91D99E0-9B7C-416C-8737-B760957DAF09": "*float64"}, {"defKey": "int", "defName": "整数", "id": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "29D1CE08-4C35-4D2D-AAA9-23D93305B52E": "INT", "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542": "INT", "BFC87171-C74F-494A-B7C2-76B9C55FACC9": "INT", "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022": "INTEGER", "89504F5D-94BF-4C9E-8B2E-44F37305FED5": "INT", "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307": "INTEGER", "592C7013-143D-4E7B-AF64-0D7BF1E28230": "INTEGER", "77BD85E5-9D0D-4096-8427-CBA306FC9C6A": "INT4", "11D1FB71-A587-4217-89BA-611B8A1F83E0": "INT", "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1": "INTEGER", "797A1496-D649-4261-89B4-544132EC3F36": "Integer", "895CFD1D-4273-4D32-A2C4-CAC70200AB5B": "Integer", "A2EE7B4A-CE62-4290-B00C-B26C1BF18073": "Integer", "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30": "float", "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2": "int", "B91D99E0-9B7C-416C-8737-B760957DAF09": "*int"}, {"defKey": "date", "defName": "日期时间", "id": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "29D1CE08-4C35-4D2D-AAA9-23D93305B52E": "DATETIME", "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542": "DATE", "BFC87171-C74F-494A-B7C2-76B9C55FACC9": "DATETIME", "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022": "TIMESTAMP", "89504F5D-94BF-4C9E-8B2E-44F37305FED5": "DATE", "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307": "DATE", "592C7013-143D-4E7B-AF64-0D7BF1E28230": "DATE", "77BD85E5-9D0D-4096-8427-CBA306FC9C6A": "DATE", "11D1FB71-A587-4217-89BA-611B8A1F83E0": "DATETIME", "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1": "NUMERIC", "797A1496-D649-4261-89B4-544132EC3F36": "Date", "895CFD1D-4273-4D32-A2C4-CAC70200AB5B": "Date", "A2EE7B4A-CE62-4290-B00C-B26C1BF18073": "Date", "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30": "DateTime", "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2": "timestamp", "B91D99E0-9B7C-416C-8737-B760957DAF09": "*time.Time"}, {"defKey": "bytes", "defName": "二进制", "id": "D516E75B-90F5-4741-B9B3-A186A263F04C", "29D1CE08-4C35-4D2D-AAA9-23D93305B52E": "BLOB", "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542": "BLOB", "BFC87171-C74F-494A-B7C2-76B9C55FACC9": "VARBINARY", "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022": "BYTEA", "89504F5D-94BF-4C9E-8B2E-44F37305FED5": "BLOB", "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307": "BLOB", "592C7013-143D-4E7B-AF64-0D7BF1E28230": "BYTEA", "77BD85E5-9D0D-4096-8427-CBA306FC9C6A": "BYTEA", "11D1FB71-A587-4217-89BA-611B8A1F83E0": "BINARY", "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1": "NONE", "797A1496-D649-4261-89B4-544132EC3F36": "byte[]", "895CFD1D-4273-4D32-A2C4-CAC70200AB5B": "byte[]", "A2EE7B4A-CE62-4290-B00C-B26C1BF18073": "byte[]", "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30": "binary", "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2": "binary", "B91D99E0-9B7C-416C-8737-B760957DAF09": "[]byte"}, {"defKey": "largeText", "defName": "大文本", "id": "B17BDED3-085F-40E1-9019-3B79CF2BF075", "29D1CE08-4C35-4D2D-AAA9-23D93305B52E": "TEXT", "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542": "CLOB", "BFC87171-C74F-494A-B7C2-76B9C55FACC9": "TEXT", "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022": "TEXT", "89504F5D-94BF-4C9E-8B2E-44F37305FED5": "CLOB", "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307": "CLOB", "592C7013-143D-4E7B-AF64-0D7BF1E28230": "TEXT", "77BD85E5-9D0D-4096-8427-CBA306FC9C6A": "TEXT", "11D1FB71-A587-4217-89BA-611B8A1F83E0": "STRING", "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1": "TEXT", "797A1496-D649-4261-89B4-544132EC3F36": "String", "895CFD1D-4273-4D32-A2C4-CAC70200AB5B": "String", "A2EE7B4A-CE62-4290-B00C-B26C1BF18073": "String", "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30": "string", "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2": "string", "B91D99E0-9B7C-416C-8737-B760957DAF09": "string"}, {"defKey": "Date", "defName": "日期", "id": "3F2E0E81-E3F3-4CC1-A856-4C20DE671ADF", "29D1CE08-4C35-4D2D-AAA9-23D93305B52E": "Date", "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542": "Date", "BFC87171-C74F-494A-B7C2-76B9C55FACC9": "Date", "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022": "Date", "89504F5D-94BF-4C9E-8B2E-44F37305FED5": "Date", "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307": "Date", "592C7013-143D-4E7B-AF64-0D7BF1E28230": "Date", "77BD85E5-9D0D-4096-8427-CBA306FC9C6A": "Date", "11D1FB71-A587-4217-89BA-611B8A1F83E0": "Date", "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1": "Date", "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2": "Date", "797A1496-D649-4261-89B4-544132EC3F36": "Date", "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30": "Date", "895CFD1D-4273-4D32-A2C4-CAC70200AB5B": "Date", "A2EE7B4A-CE62-4290-B00C-B26C1BF18073": "Date", "B91D99E0-9B7C-416C-8737-B760957DAF09": "Date"}, {"defKey": "Time", "defName": "时间", "id": "F8C4CB1E-F5E4-4819-9F5A-BB61898EFD34", "29D1CE08-4C35-4D2D-AAA9-23D93305B52E": "Time", "A4E23CB7-BB01-4BD1-9F71-F73F3E15A542": "Time", "BFC87171-C74F-494A-B7C2-76B9C55FACC9": "Time", "DFBEC1DD-AA84-456E-BBF3-C95DD0DB2022": "Time", "89504F5D-94BF-4C9E-8B2E-44F37305FED5": "Time", "0BBCABA5-B8E4-41B0-B8E4-8F5EA6029307": "Time", "592C7013-143D-4E7B-AF64-0D7BF1E28230": "Time", "77BD85E5-9D0D-4096-8427-CBA306FC9C6A": "Time", "11D1FB71-A587-4217-89BA-611B8A1F83E0": "Time", "B363BE0B-F852-49B8-9B2E-F6D2174DEAC1": "Time", "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2": "Time", "797A1496-D649-4261-89B4-544132EC3F36": "Time", "F3AC2415-E86B-40C6-9FEB-F4B7937D2C30": "Time", "895CFD1D-4273-4D32-A2C4-CAC70200AB5B": "Time", "A2EE7B4A-CE62-4290-B00C-B26C1BF18073": "Time", "B91D99E0-9B7C-416C-8737-B760957DAF09": "Time"}]}, "domains": [{"defKey": "ID", "defName": "主键ID", "applyFor": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "len": 20, "scale": "", "uiHint": "", "id": "8FB04445-A323-4AB1-AB8C-DEE8F715B9E0"}, {"defKey": "ShortestString", "defName": "字串-最短", "applyFor": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "len": 10, "scale": "", "uiHint": "", "id": "234455ED-4015-488A-97DC-A7F20B3D4842"}, {"defKey": "ShortString", "defName": "字串-短", "applyFor": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "len": 20, "scale": "", "uiHint": "", "id": "B385F062-24F5-4F2F-AB91-00A20FCC8E32"}, {"defKey": "ShorterString", "defName": "字串-较短", "applyFor": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "len": 50, "scale": "", "uiHint": "", "id": "FDA237FE-B582-4DE0-89EB-9724C546642C"}, {"defKey": "MiddleString", "defName": "字串-中", "applyFor": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "len": 100, "scale": "", "uiHint": "", "id": "2915B007-42A9-4188-AC7B-EB007C8C5DB3"}, {"defKey": "LongerString", "defName": "字串-较长", "applyFor": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "len": 200, "scale": "", "uiHint": "", "id": "CF8680CD-7A15-4336-ABD1-C64D6CA2FCCC"}, {"defKey": "LongString", "defName": "字串-长", "applyFor": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "len": 500, "scale": "", "uiHint": "", "id": "4BC594A5-2D6C-4564-B4E2-436BE7248101"}, {"defKey": "LongestString", "defName": "字串-最长", "applyFor": "FC9790A7-36B8-4A48-8F9A-BC1042BCFE64", "len": 1000, "scale": "", "uiHint": "", "id": "A1A7CBF0-6385-4746-AB16-82E17A442846"}, {"defKey": "ShortInt", "defName": "整数-小", "applyFor": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "len": 3, "scale": "", "uiHint": "", "id": "DED983EB-AC2F-413A-8FF6-4AA8463FF88C"}, {"defKey": "MidInt", "defName": "整数-中", "applyFor": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "len": 12, "scale": "", "uiHint": "", "id": "51EF7713-57ED-417E-B09A-5D5051D6E9D6"}, {"defKey": "BigInt", "defName": "整数-大", "applyFor": "1D764C4A-6F9F-421E-B11A-6F3E23B51811", "len": 20, "scale": "", "uiHint": "", "id": "FB37AF1B-4C9D-42E5-B53E-9FDAE58F2C48"}, {"defKey": "Double", "defName": "小数", "applyFor": "1A0BDC09-0792-4174-9E8E-80BE8DF44B8E", "len": 24, "scale": 6, "uiHint": "", "id": "FF4459C5-6B45-4DBF-8FC0-E06239BC05B4"}, {"defKey": "Money", "defName": "金额", "applyFor": "1A0BDC09-0792-4174-9E8E-80BE8DF44B8E", "len": 24, "scale": 6, "uiHint": "", "id": "C3B1681B-99F9-4818-9E80-DE1652A51D85"}, {"defKey": "Radio", "defName": "率", "applyFor": "1A0BDC09-0792-4174-9E8E-80BE8DF44B8E", "len": 9, "scale": 3, "uiHint": "", "id": "265881A0-DBBF-490D-9360-62EB43E1501C"}, {"defKey": "DateTime", "defName": "日期时间", "applyFor": "89D69E81-EA34-42EE-9FA2-93B8BD27E098", "len": "", "scale": "", "uiHint": "", "id": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC"}, {"defKey": "Date", "defName": "日期", "applyFor": "3F2E0E81-E3F3-4CC1-A856-4C20DE671ADF", "len": "", "scale": "", "uiHint": "", "id": "5A7786D1-3443-4B1F-8E08-DA15C5E95F4C"}, {"defKey": "Time", "defName": "时间", "applyFor": "F8C4CB1E-F5E4-4819-9F5A-BB61898EFD34", "len": "", "scale": "", "uiHint": "", "id": "9DAAA1B0-98E4-4316-96E8-ABABBFEECB3B"}, {"defKey": "Flag", "defName": "标志", "applyFor": "88F25C57-11EC-427B-BC13-571557245EB5", "len": "1", "scale": "", "uiHint": "", "id": "BB22DFF1-9A03-4D7C-8098-48734CEE8B5B"}, {"defKey": "Text", "defName": "大文本", "applyFor": "B17BDED3-085F-40E1-9019-3B79CF2BF075", "len": "", "scale": "", "uiHint": "", "id": "E276388C-3E0D-46FD-A8E0-8BBF33D8F3D5"}], "diagrams": [{"defKey": "Group", "defName": "", "relationType": "field", "canvasData": {"cells": [{"id": "bbf4c27d-11ca-4b0b-a8ff-63623037d1c0", "shape": "erdRelation", "source": {"cell": "0d5b555d-d2ea-4fec-ad35-ee5cb846d5f7", "port": "FFB5CE74-A708-4B76-9A65-C54C9CFBEF46%out"}, "target": {"cell": "ccf63afe-9870-4c14-8137-976e9704db56", "port": "907FF023-06E8-45D0-AC89-83C8F5ABE584%out"}, "relation": "1:n", "fillColor": "#ACDAFC"}, {"id": "6a7154a9-9996-4580-b91d-8cc28162b244", "shape": "erdRelation", "source": {"cell": "0d5b555d-d2ea-4fec-ad35-ee5cb846d5f7", "port": "FFB5CE74-A708-4B76-9A65-C54C9CFBEF46%out"}, "target": {"cell": "e7db5280-6774-493b-9711-ff87020e4399", "port": "B72025C1-9330-4B64-916A-6974428A8108%in"}, "relation": "1:n", "fillColor": "#ACDAFC"}, {"id": "4ab6c062-4826-497a-b69d-364b3aa369a8", "shape": "erdRelation", "source": {"cell": "0d5b555d-d2ea-4fec-ad35-ee5cb846d5f7", "port": "FFB5CE74-A708-4B76-9A65-C54C9CFBEF46%in"}, "target": {"cell": "97acdae7-333a-483f-bdb3-9beee8e977c4", "port": "B72025C1-9330-4B64-916A-6974428A8108%out"}, "relation": "1:n", "fillColor": "#ACDAFC"}, {"id": "9da327f2-c25e-46d8-82a5-37c7ad15aa11", "shape": "erdRelation", "source": {"cell": "97acdae7-333a-483f-bdb3-9beee8e977c4", "port": "40AA05AF-0F97-4D0F-AC87-336E823C59EC%in"}, "target": {"cell": "1b827327-e586-40ac-89f3-0f45fc6916ce", "port": "B72025C1-9330-4B64-916A-6974428A8108%out"}, "relation": "1:n", "fillColor": "#ACDAFC"}, {"id": "48336da6-0d1c-4361-9f12-4e0077043561", "shape": "erdRelation", "source": {"cell": "a6b4a925-8e73-492d-aeae-4d21394f2e68", "port": "FFB5CE74-A708-4B76-9A65-C54C9CFBEF46%in"}, "target": {"cell": "017db0a0-23dc-4ccf-8b75-72026967ffbd", "port": "B72025C1-9330-4B64-916A-6974428A8108%out"}, "relation": "1:n", "fillColor": "#ACDAFC"}, {"id": "5fbb12ea-57b4-4f9e-bef6-1e81275048c6", "shape": "erdRelation", "source": {"cell": "017db0a0-23dc-4ccf-8b75-72026967ffbd", "port": "40AA05AF-0F97-4D0F-AC87-336E823C59EC%in"}, "target": {"cell": "e4de3af1-b294-42e6-b0d2-3496aafce77f", "port": "B72025C1-9330-4B64-916A-6974428A8108%out"}, "relation": "1:n", "fillColor": "#ACDAFC"}, {"id": "1b827327-e586-40ac-89f3-0f45fc6916ce", "shape": "table", "position": {"x": -792, "y": 42}, "count": 0, "originKey": "75A16593-91C0-48E2-BCB8-574B27CEACAB"}, {"id": "07ac92f8-de83-4a4f-88a4-2231ad941f36", "shape": "table", "position": {"x": -534, "y": 291.5}, "count": 0, "originKey": "4A2A31D6-F5F7-4257-AB84-FB6A5C3A5AC1"}, {"id": "193fed8e-fffa-435f-a6a7-99f7c14b8401", "shape": "table", "position": {"x": -792, "y": 299}, "count": 0, "originKey": "336583BD-8C39-4844-98D6-ED209644F3A3"}, {"id": "e4de3af1-b294-42e6-b0d2-3496aafce77f", "shape": "table", "position": {"x": -792, "y": 590}, "count": 0, "originKey": "2510901D-F14D-461A-950B-67B58C454CD7"}, {"id": "017db0a0-23dc-4ccf-8b75-72026967ffbd", "shape": "table", "position": {"x": -320, "y": 577}, "count": 0, "originKey": "DC1F6D58-C785-4D64-89EA-FA8AA9267BAB"}, {"id": "6837c61b-33e1-4e13-bd06-0d127072abdb", "shape": "table", "position": {"x": -270, "y": 400}, "count": 0, "originKey": "5C33F8CC-B1FC-4AB6-8EE2-BC0CEE343E07"}, {"id": "97acdae7-333a-483f-bdb3-9beee8e977c4", "shape": "table", "position": {"x": -281.5, "y": 42}, "count": 0, "originKey": "FF04F1FC-386B-40C7-ABEF-3AB9B548544A"}, {"id": "ccf63afe-9870-4c14-8137-976e9704db56", "shape": "table", "position": {"x": 110, "y": 42}, "count": 0, "originKey": "6DCCA81D-348C-46BF-9EAC-C708BC415ECD"}, {"id": "0d5b555d-d2ea-4fec-ad35-ee5cb846d5f7", "shape": "table", "position": {"x": 110, "y": 372}, "count": 0, "originKey": "47F82A27-1BF1-44E6-91A2-315765195250"}, {"id": "e7db5280-6774-493b-9711-ff87020e4399", "shape": "table", "position": {"x": 430, "y": 42}, "count": 0, "originKey": "990632EC-D864-4FE3-850B-CA600A8BA575"}, {"id": "a6b4a925-8e73-492d-aeae-4d21394f2e68", "shape": "table", "position": {"x": 110, "y": 577}, "count": 0, "originKey": "65F9894E-BEC5-4E5C-A099-9C9A605AA92D"}]}, "id": "D5B473B9-2AA5-4979-BA97-04921DDAE986"}], "standardFields": [{"defKey": "personInfo", "defName": "个人基本信息要素", "fields": [{"defKey": "ID_CARD_NO", "defName": "身份证号", "comment": "", "type": "VARCHAR", "len": "60", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "refDict": "", "uiHint": "", "id": "A64A91C8-A41F-4113-92FB-7563D7EF054D"}, {"defKey": "MOBILE_PHONE", "defName": "手机号", "comment": "", "type": "VARCHAR", "len": "60", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "refDict": "", "uiHint": "", "id": "479DA2AB-1974-411A-A81E-92FB939E75EB"}, {"defKey": "GENDER", "defName": "性别", "comment": "", "type": "VARCHAR", "len": "32", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "73FD2BAD-2358-4336-B96D-45DC897BD792", "refDict": "BF9E20E0-80D3-486D-BD58-5FADCF3E4A1D", "uiHint": "", "id": "48473E29-6594-4912-AADE-C8AB44FCA3E9"}, {"defKey": "BIRTH", "defName": "出生日期", "comment": "", "type": "DATETIME", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "7CFFA0D3-6A93-4DDC-BC10-DF21211064DC", "refDict": "", "uiHint": "", "id": "2BD3D2EE-2411-49A6-983D-84B81057312F"}, {"defKey": "AVATAR", "defName": "头像", "comment": "", "type": "VARCHAR", "len": "60", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "refDict": "", "uiHint": "", "id": "FDD67CEE-4B52-4BD1-A1A3-9C5EBC6037E6"}, {"defKey": "HEIGHT", "defName": "身高", "comment": "", "type": "INT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "refDict": "", "uiHint": "", "id": "CAAA0E79-41A1-4758-B481-D171168C4D68"}, {"defKey": "WEIGHT", "defName": "体重", "comment": "", "type": "INT", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "6BC8F04B-6CFA-4995-98D3-318F5CDD774E", "refDict": "", "uiHint": "", "id": "575482CE-64A6-4CB9-99DC-8E126D190AAA"}, {"defKey": "NATION", "defName": "名族", "comment": "", "type": "VARCHAR", "len": "32", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "73FD2BAD-2358-4336-B96D-45DC897BD792", "refDict": "115EDEFC-0323-410E-81AB-CCAB8879837A", "uiHint": "", "id": "15B0D75D-0B97-4985-A816-D0EAFA90446B"}, {"defKey": "POLITICAL", "defName": "政治面貌", "comment": "", "type": "VARCHAR", "len": "32", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "73FD2BAD-2358-4336-B96D-45DC897BD792", "refDict": "06EED564-BBA9-4747-8D73-AF809A330CB8", "uiHint": "", "id": "F458E86D-84D6-45A1-9DD3-51E6C8170D7F"}, {"defKey": "MARITAL", "defName": "婚姻状况", "comment": "", "type": "VARCHAR", "len": "32", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "73FD2BAD-2358-4336-B96D-45DC897BD792", "refDict": "EA1587B7-3954-437A-BFE0-FCB0453BCABA", "uiHint": "", "id": "7275E578-6893-4922-AC69-95B261BFBD61"}, {"defKey": "DOMICILE_PLACE_PROVINCE", "defName": "籍贯（省）", "comment": "", "type": "VARCHAR", "len": "60", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "refDict": "", "uiHint": "", "id": "F04BF130-3EC1-4E02-9DED-3214CA88E352"}, {"defKey": "DOMICILE_PLACE_CITY", "defName": "籍贯（市）", "comment": "", "type": "VARCHAR", "len": "32", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "73FD2BAD-2358-4336-B96D-45DC897BD792", "refDict": "", "uiHint": "", "id": "B97F5BC2-33DE-4857-9DB8-ECFD02C9040C"}, {"defKey": "DOMICILE_PLACE_ADDRESS", "defName": "户籍地址", "comment": "", "type": "VARCHAR", "len": "60", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "domain": "9092C4E0-1A54-4859-ABBB-5B62DBC27573", "refDict": "", "uiHint": "", "id": "812ADF1D-8C03-40CA-B030-E539838FB889"}], "id": "F30202B9-4B5D-4CE7-87CE-B3890C84D3F2"}], "dbConn": [{"defKey": "3A9A5AC0-35E3-45A1-B5E1-7D1D9AF926DC", "defName": "jyfx", "type": "29D1CE08-4C35-4D2D-AAA9-23D93305B52E", "properties": {"driver_class_name": "com.mysql.cj.jdbc.Driver", "url": "*******************************************************************************************************************************************", "password": "jyfx@123456", "username": "jyfx"}}]}