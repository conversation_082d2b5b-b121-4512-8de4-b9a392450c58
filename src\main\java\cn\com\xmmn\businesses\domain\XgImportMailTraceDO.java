package cn.com.xmmn.businesses.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;



/**
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-06 10:56:36
 */
@Data
@ApiModel(value = "香港轨迹表",description = "")
@TableName(value = "tb_xg_import_mail_trace")
public class XgImportMailTraceDO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//
	private String id;
	//
	private String traceNo;
	//
	private Date opTime;
	//
	private String opCode;
	//
	private String opName;
	//
	private String opOrgCode;
	//
	private String opOrgName;
	//
	private String notes;
	//
	private Date pt;

	/**
	 * 设置：
	 */
	public void setId(String id) {
		this.id = id;
	}
	/**
	 * 获取：
	 */
	public String getId() {
		return id;
	}
	/**
	 * 设置：
	 */
	public void setTraceNo(String traceNo) {
		this.traceNo = traceNo;
	}
	/**
	 * 获取：
	 */
	public String getTraceNo() {
		return traceNo;
	}
	/**
	 * 设置：
	 */
	public void setOpTime(Date opTime) {
		this.opTime = opTime;
	}
	/**
	 * 获取：
	 */
	public Date getOpTime() {
		return opTime;
	}
	/**
	 * 设置：
	 */
	public void setOpCode(String opCode) {
		this.opCode = opCode;
	}
	/**
	 * 获取：
	 */
	public String getOpCode() {
		return opCode;
	}
	/**
	 * 设置：
	 */
	public void setOpName(String opName) {
		this.opName = opName;
	}
	/**
	 * 获取：
	 */
	public String getOpName() {
		return opName;
	}
	/**
	 * 设置：
	 */
	public void setOpOrgCode(String opOrgCode) {
		this.opOrgCode = opOrgCode;
	}
	/**
	 * 获取：
	 */
	public String getOpOrgCode() {
		return opOrgCode;
	}
	/**
	 * 设置：
	 */
	public void setOpOrgName(String opOrgName) {
		this.opOrgName = opOrgName;
	}
	/**
	 * 获取：
	 */
	public String getOpOrgName() {
		return opOrgName;
	}
	/**
	 * 设置：
	 */
	public void setNotes(String notes) {
		this.notes = notes;
	}
	/**
	 * 获取：
	 */
	public String getNotes() {
		return notes;
	}
	/**
	 * 设置：
	 */
	public void setPt(Date pt) {
		this.pt = pt;
	}
	/**
	 * 获取：
	 */
	public Date getPt() {
		return pt;
	}
}
