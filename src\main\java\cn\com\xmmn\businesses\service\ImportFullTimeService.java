package cn.com.xmmn.businesses.service;

import cn.com.xmmn.businesses.domain.ImportHhjDisposeDO;
import cn.com.xmmn.businesses.domain.ImportMailDO;
import cn.com.xmmn.businesses.vo.ImportFullTimeListVO;
import cn.com.xmmn.common.utils.Query;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * 进口全程时限统计表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-20 15:02:00
 */
public interface ImportFullTimeService extends IService<ImportMailDO> {


    List<ImportFullTimeListVO> list(Map<String, Object> params);

    Integer count(Map<String, Object> params);
}
