package cn.com.xmmn.businesses.service;

import cn.com.xmmn.businesses.domain.ImportMailCargoDO;
import cn.com.xmmn.businesses.domain.ImportMailDO;
import cn.com.xmmn.businesses.domain.ImportMailReceiptDO;
import cn.com.xmmn.businesses.vo.ImportMailShouldListVO;
import cn.com.xmmn.common.utils.Query;

import java.util.List;
import java.util.Map;

/**
 * 进口邮件表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-23 17:32:54
 */
public interface ImportMailService {
	
	ImportMailDO get(String itemId);
	
	List<ImportMailCargoDO> list(Map<String, Object> map);
	
	int count(Map<String, Object> map);
	
	int save(ImportMailDO importMail);
	
	int update(ImportMailDO importMail);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);

	List<ImportMailReceiptDO> ReceiptList(Map<String, Object> map);

	int ReceiptCount(Map<String, Object> map);

	/**
	 * 应处理
	 * @param map
	 * @return
	 */
    List<ImportMailShouldListVO> getShouldList(Map<String, Object> map);

	/**
	 * 应处理 总量
	 * @param query
	 * @return
	 */
	int getShouldCount(Map<String, Object> map);
}
