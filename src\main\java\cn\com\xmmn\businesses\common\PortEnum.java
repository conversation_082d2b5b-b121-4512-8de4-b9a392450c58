package cn.com.xmmn.businesses.common;

import java.util.HashMap;
import java.util.Map;

public enum PortEnum {

    PORT_DGG("DGG","东莞","1"),
    PORT_CAN("CAN","广州","1"),
    PORT_SZX("SZX","深圳","1"),
    PORT_ZUH("ZUH","珠海","1"),
    PORT_WNZ("WNZ","温州","0"),
    PORT_HGH("HGH","杭州","0"),
    PORT_YIW("YIW","义乌","0"),
    PORT_SHA("SHA","上海","0"),
    PORT_NGB("NGB","宁波","0"),
    PORT_BJS("BJS","北京","0"),
    PORT_CGO("CGO","郑州","0"),
    PORT_NKG("NKG","南京","0"),
    PORT_SZH("SZH","苏州","0"),
    PORT_NCH("NCH","南昌","0"),
    PORT_XMN("XMN","厦门","0"),
    PORT_FOC("FOC","福州","0"),
    PORT_WUH("WUH","武汉","0"),
    PORT_SWA("SWA","汕头","0"),
    PORT_HFE("HFE","合肥","0"),
    PORT_SIA("SIA","西安","0");


    private String code;
    private String name;
    private String settlementFlag;

    private PortEnum(String code, String name, String settlementFlag){
        this.code=code;
        this.name=name;
        this.settlementFlag=settlementFlag;
    }


    /**
     * 获取编码
     *
     * @return 编码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getName() {
        return name;
    }

    /**
     * 获取数字编码
     *
     * @return 编码
     */
    public String getSettlementFlag() {
        return settlementFlag;
    }


    public static String getNameByCode(String code) {
       for(PortEnum portEnum: PortEnum.values()){
           if(code.equals(portEnum.getCode())){
               return  portEnum.getName();
           }
       }
       return null;
    }

    public static String getSettlementFlagByCode(String code) {
        for(PortEnum portEnum: PortEnum.values()){
            if(code.equals(portEnum.getCode())){
                return  portEnum.getSettlementFlag();
            }
        }
        return null;
    }

    /**
     * 获取需要的口岸列表
     * @return
     */
    public static Map<String,String> getPorts() {
        Map<String,String> portMap= new HashMap<>();
        for(PortEnum portEnum: PortEnum.values()){
            portMap.put(portEnum.getCode(),portEnum.getName());
        }
        return portMap;
    }

    /**
     * 获取需要结算的口岸列表
     * @return
     */
    public static Map<String,String> getSettlementPorts() {
        Map<String,String> settlementPortMap= new HashMap<>();
        for(PortEnum portEnum: PortEnum.values()){
            if("1".equals(portEnum.getSettlementFlag())){
                settlementPortMap.put(portEnum.getCode(),portEnum.getName());
            }
        }
        return settlementPortMap;
    }
}
