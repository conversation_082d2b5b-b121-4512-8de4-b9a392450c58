package cn.com.xmmn.businesses.controller;

import cn.com.xmmn.businesses.common.PortEnum;
import cn.com.xmmn.businesses.domain.AirMailChannelBillsFristBillDO;
import cn.com.xmmn.businesses.domain.AirMailChannelBillsFristDetailDO;
import cn.com.xmmn.businesses.domain.PortbatchDO;
import cn.com.xmmn.businesses.service.AirMailChannelBillsFristBillService;
import cn.com.xmmn.businesses.service.AirMailChannelBillsFristDetailService;
import cn.com.xmmn.businesses.service.PortbatchService;
import cn.com.xmmn.businesses.service.impl.PortbatchServiceImpl;
import cn.com.xmmn.common.utils.PageUtils;
import cn.com.xmmn.common.utils.Query;
import cn.com.xmmn.common.utils.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;

/**
 * 口岸导入批次表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-22 01:30:58
 */

@Slf4j
@Controller
@RequestMapping("/businesses/portbatch")
public class PortbatchController {
    @Autowired
    private PortbatchService portbatchService;

    @Autowired
    private PortbatchServiceImpl BatchServiceImpl;

    @Autowired
    private AirMailChannelBillsFristBillService airMailChannelBillsFristBillService;

    @Autowired
    private AirMailChannelBillsFristDetailService airMailChannelBillsFristDetailService;

    @GetMapping()
    @RequiresPermissions("businesses:portbatch:portbatch")
    String Portbatch(Model model) {

        Map<String, String> map = PortEnum.getPorts();
		/*Set<Map.Entry<String, String>> entrySet = map.entrySet();
		for (Map.Entry<String, String> entry : entrySet) {
			log.info(entry.getKey() + ":" + entry.getValue());
		}
*/
        model.addAttribute("enumType", map);
        return "businesses/portbatch/portbatch";
    }

    @ResponseBody
    @GetMapping("/list")
    @RequiresPermissions("businesses:portbatch:portbatch")
    public PageUtils list(@RequestParam Map<String, Object> params) {
        //查询列表数据
        Query query = new Query(params);
        List<PortbatchDO> portbatchList = portbatchService.list(query);
        int total = portbatchService.count(query);
        PageUtils pageUtils = new PageUtils(portbatchList, total);
        return pageUtils;
    }

    @GetMapping("/add")
    @RequiresPermissions("businesses:portbatch:add")
    String add() {
        return "businesses/portbatch/add";
    }

    @GetMapping("/edit/{id}")
    @RequiresPermissions("businesses:portbatch:edit")
    String edit(@PathVariable("id") Integer id, Model model) {
        AirMailChannelBillsFristBillDO airMailChannelBillsFristBill = new AirMailChannelBillsFristBillDO();
        airMailChannelBillsFristBill.setBatchId(id);
        List billList = airMailChannelBillsFristBillService.detail(airMailChannelBillsFristBill);
        model.addAttribute("billList", billList);
        return "businesses/portbatch/billDetail";
    }


    @GetMapping("/detail/{id}")
    @RequiresPermissions("businesses:portbatch:detail")
    String detail(@PathVariable("id") Integer id, Model model) {
        AirMailChannelBillsFristDetailDO airMailChannelBillsFristDetailDO = new AirMailChannelBillsFristDetailDO();
        airMailChannelBillsFristDetailDO.setBillsId(id);
        List detailList = airMailChannelBillsFristDetailService.detail(airMailChannelBillsFristDetailDO);
        model.addAttribute("detailList", detailList);
        return "businesses/portbatch/detail";
    }

    /**
     * 保存
     */
    @ResponseBody
    @PostMapping("/save")
    @RequiresPermissions("businesses:portbatch:add")
    public R save(PortbatchDO portbatch) {
        if (portbatchService.save(portbatch) > 0) {
            return R.ok();
        }
        return R.error();
    }

    /**
     * 修改
     */
    @ResponseBody
    @RequestMapping("/update")
    @RequiresPermissions("businesses:portbatch:edit")
    public R update(PortbatchDO portbatch) {
        portbatchService.update(portbatch);
        return R.ok();
    }

    /**
     * 删除
     */
    @PostMapping("/remove")
    @ResponseBody
    @RequiresPermissions("businesses:portbatch:remove")
    public R remove(Integer id) {
        if (portbatchService.remove(id) > 0) {
            return R.ok();
        }
        return R.error();
    }

    /**
     * 删除
     */
    @PostMapping("/batchRemove")
    @ResponseBody
    @RequiresPermissions("businesses:portbatch:batchRemove")
    public R remove(@RequestParam("ids[]") Integer[] ids) {
        portbatchService.batchRemove(ids);
        return R.ok();
    }

    //导入

    /**
     * 接受文件   解析  上传资料。
     * /admin/client/upload_excel
     */
    @PostMapping("/uploadExcel")
    public String uploadExcel(MultipartFile pdffile, String modelType, String settlementType, Model model) throws Exception {
        //判断前台传值modeltype来确认导入的表
		/*if ("1".equals(modelType)){
		//导入商业出口手工表
			int batch=BatchServiceImpl.CommercialExcel(file,modelType);
			model.addAttribute("id",batch);
		}else if ("2".equals(modelType)){
			int batch=BatchServiceImpl.NewExcel(file,modelType);
			model.addAttribute("id",batch);
		}*/
        Map<String, String> map = PortEnum.getPorts();
		/*Set<Map.Entry<String, String>> entrySet = map.entrySet();
		for (Map.Entry<String, String> entry : entrySet) {
			log.info(entry.getKey() + ":" + entry.getValue());
		}
*/
        model.addAttribute("enumType", map);
        try{
        int batch = BatchServiceImpl.NewExcel(pdffile, modelType, settlementType);
        } catch (Exception e) {
            e.printStackTrace();
            String msg = "导入失败，请检查导入模板是否有误！"; // 错误原因
            model.addAttribute("message", msg); // 错误信息
            return "businesses/portbatch/portbatch";
//            throw new Exception("请检查导入模板是否有误");
        }

//		model.addAttribute("id",batch);

        return "businesses/portbatch/portbatch";

    }

    /**
     * 下载excel模板
     */
    @GetMapping("/downloadExcel")
    @ResponseBody
    @RequiresPermissions("businesses:portbatch:downloadExcel")
    public void downloadExcel(HttpServletResponse response) throws UnsupportedEncodingException {
        BatchServiceImpl.downloadExcel(response);
    }

}
