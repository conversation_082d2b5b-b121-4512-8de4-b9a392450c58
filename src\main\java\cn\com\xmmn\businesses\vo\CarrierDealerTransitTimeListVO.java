package cn.com.xmmn.businesses.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description 承运商全程时限情况
 * <AUTHOR>
 * @Date 2024年6月14日15:18:12
 */
@Data
@HeadRowHeight(value = 30) // 头部行高
@ContentRowHeight(value = 25) // 内容行高
@ColumnWidth(value = 20) // 列宽
@HeadFontStyle(fontName = "宋体", fontHeightInPoints = 11) // 头部字体样式
public class CarrierDealerTransitTimeListVO  implements Serializable {

    @ExcelIgnore
    private String orgName;

    @ExcelIgnore
    private String orgCode;



    @ExcelProperty("承运商")
    private String carrierDealer;

    @ExcelIgnore
    private String carrierCode;

    @ExcelProperty("业务种类")
    private String product;

    @ExcelIgnore
    private String productCode;

    @ExcelProperty("寄达国家/地区名称")
    private String receiverCountryName;

    @ExcelIgnore
    private String receiverCountryCode;

    @ExcelIgnore
    private String receiverCountry;
    @ExcelIgnore
    private String oeDest;

    @ExcelProperty("寄达互换局名称")
    private String oeDestName;
    @ExcelProperty("承运商接收袋数")
    private Integer receiveNum;

    @ExcelProperty("承运商接收重量（kg）")
    private BigDecimal receiverTotalWeight;

    @ExcelProperty("平均接收至交邮时长（天）")
    private BigDecimal avgReceiveToPostTime;

    @ExcelProperty("准时运输邮袋袋数")
    private Integer onTimeNum;

    @ExcelProperty("超时运输邮袋袋数")
    private Integer overTimeNum;

    @ExcelProperty("异常袋数")
    private Integer exceptionBagNum;

    @ExcelProperty("全程时限准时率(%)")
    private BigDecimal timeLimitRate;

    @ExcelIgnore
    private Integer total;
}
