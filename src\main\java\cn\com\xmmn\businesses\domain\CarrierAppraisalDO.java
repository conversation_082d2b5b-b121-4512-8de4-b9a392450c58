package cn.com.xmmn.businesses.domain;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;



/**
 * 承运商整体情况的考评表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-26 15:59:10
 */
@Data
@HeadRowHeight(value = 30) // 头部行高
@ContentRowHeight(value = 25) // 内容行高
@ColumnWidth(value = 20) // 列宽
@HeadFontStyle(fontName = "宋体", fontHeightInPoints = 11) // 头部字体样式
@TableName("t_carrier_appraisal")
public class CarrierAppraisalDO implements Serializable {
	private static final long serialVersionUID = 1L;

	@ExcelIgnore
	private Integer id;

	@ExcelProperty("考核月份")
	private String appraisalMon;

	@ExcelProperty("承运商代码")
	private String carrierCode;

	@ExcelProperty("承运商")
	private String carrierDealer;

	@ExcelProperty("产品种类")
	private String bussType;

	@ExcelProperty("业务种类代码")
	private String productCode;

	@ExcelProperty("业务种类名称")
	private String product;

	@ExcelProperty("接收袋数")
	private BigDecimal recvNum;

	@ExcelProperty("接收重量")
	private BigDecimal recvWeight;

	@ExcelProperty("启运袋数")
	private BigDecimal depNum;

	@ExcelProperty("启运重量")
	private BigDecimal depWeight;

	@ExcelProperty("抵达袋数")
	private BigDecimal arriveNum;

	@ExcelProperty("抵达重量")
	private BigDecimal arriveWeight;

	@ExcelProperty("交邮袋数")
	private BigDecimal postNum;

	@ExcelProperty("交邮重量")
	private BigDecimal postWeight;

	@ExcelProperty("接收反馈率")
	private BigDecimal recvBackRate;

	@ExcelProperty("启运反馈率")
	private BigDecimal depBackRate;

	@ExcelProperty("抵达反馈率")
	private BigDecimal arriveBackRate;

	@ExcelProperty("交邮反馈率")
	private BigDecimal postBackRate;

	@ExcelProperty("接收及时率")
	private BigDecimal recvTimelyRate;

	@ExcelProperty("启运及时率")
	private BigDecimal depTimelyRate;

	@ExcelProperty("抵达及时率")
	private BigDecimal arriveTimelyRate;

	@ExcelProperty("交邮及时率")
	private BigDecimal postTimelyRate;

	//@ExcelProperty("创建时间")
	@ExcelIgnore
	private Date createTime;
}


