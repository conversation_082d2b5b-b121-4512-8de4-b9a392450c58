package cn.com.xmmn.common.push.postal.model;

import cn.com.xmmn.businesses.domain.FeedbackRecordsDO;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 *  邮政新一代轨迹消息体
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024年10月9日
 */
@Data
@Builder
public class PostalTraceMessageBody {

    //总包条码
    private String traceNo;

    /**
     * 扫描时间 比如接收时间，启运时间，运抵时间，交邮时间 yyyy-MM-dd hh:mm:ss
     */
    private String opTime;

    //航空公司ID（代码前2位）
    private String opOrgCode;

    //航空公司名称
    private String opOrgName;

    /**
     * 事件类型500.航空公司接收
     * 457.航空公司启运
     * 505.飞机到达进港
     * 507.送交境外航站（交邮处理）
     */
    private String opCode;

    //邮件类型(默认 3-国际邮件)
    private String traceType;
    //航班号
    private String vehicleCode;

    //操作员ID
    private String operatorNo;
    //操作员名称
    private String operatorName;
    //扫描标志(P总包 C集装箱默认P总包)
    private String containerType;
    //容器号
    private String containerNo;

    //承运商 -->承运商简码
    private String deviceType;

    //起飞时间
    private String departureDatetime;

    //承运商类型(F航空,S海运,T火车陆运,C汽车陆运,默认F)
    private String transClass;
    //操作口岸三位操作码
    private String opPortCode;
    //启运站位操作码
    private String departPort;
    //卸运站
    private String offloadPort;
    //封志条码
    private String dispBarCode;
    //1包机 2 包箱 3散仓 4 高板 5 低板 6 平高板 7 预留版型1   8预留版型2
    private String executionMode;
    //是否混板1 是 2 否
    private Integer isHybrid;
    //航司运单号
    private String airlinebillNo;


}
