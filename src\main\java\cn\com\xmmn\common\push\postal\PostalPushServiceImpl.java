package cn.com.xmmn.common.push.postal;


import cn.com.xmmn.businesses.config.PushServiceConfig;
import cn.com.xmmn.businesses.config.ServiceConfig;
import cn.com.xmmn.common.push.postal.model.PostalRequest;
import cn.com.xmmn.common.push.postal.model.PostalResponse;
import cn.com.xmmn.common.push.postal.model.PostalTraces;
import cn.com.xmmn.common.utils.MD5Utils;
import cn.com.xmmn.common.utils.StringUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.UUID;


@Slf4j
@RequiredArgsConstructor
@Service
public class PostalPushServiceImpl implements PostalPushService {

    private final static String proviceNo = "99";
    private final static String msgKind = "RCVR_JDPT_TRACE";
    private final static String batchNo = "000000000001";


    private final PushServiceConfig pushServiceConfig;



    @Override
    public PostalResponse pushMailsTrack(PostalTraces postalTraces) {

        
        String identifier = pushServiceConfig.getIdentifier();
        String url = pushServiceConfig.getUrl();
        String key = pushServiceConfig.getKey();


        PostalResponse postalResponse = new PostalResponse();
        try {
            //消息头
            PostalRequest postalRequest = PostalRequest.builder()
                    .sendID(identifier)
                    .proviceNo(proviceNo)
                    .msgKind(msgKind)
                    .serialNo(UUID.randomUUID().toString())
                    .sendDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("YYYYMMddHHmmss")))
                    .receiveID(identifier)
                    .batchNo(batchNo)
                    .dataType("1")
                    .build();

            String msgBody = JSONObject.toJSONString(postalTraces);
            postalRequest.setMsgBody(msgBody);
            String dataDigest = getDataDigest(msgBody,key);
            postalRequest.setDataDigest(dataDigest);

            //对象转map
            Map<String, Object> paramMap = BeanUtil.beanToMap(postalRequest);

            log.info("向寄递平台推送的运单轨迹信息信息url:{},map:{}", url, paramMap);
            String response = HttpUtil.post(url, paramMap);
            log.info("向寄递平台推送的运单轨迹信息响应结果:{}", response);
            if (StringUtils.isNotEmpty(response)) {
                postalResponse = JSONObject.parseObject(response, PostalResponse.class);
            }
        } catch (Exception e) {

            e.printStackTrace();
        }
        return postalResponse;
    }

    /**
     * md5加密生成加密后的签名
     *
     * @param msgBody
     * @param key 秘钥
     * @return
     * @throws Exception
     */
    private String getDataDigest(String msgBody,String key) throws Exception {
        return MD5Utils.encryptNew(msgBody, key);
    }
}
