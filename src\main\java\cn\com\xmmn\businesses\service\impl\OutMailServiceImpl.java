package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.OutMailDao;
import cn.com.xmmn.businesses.domain.OutMailDO;
import cn.com.xmmn.businesses.dt.OutMailDT;
import cn.com.xmmn.businesses.service.OutMailService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;




@Service
public class OutMailServiceImpl implements OutMailService {
	@Autowired
	private OutMailDao outMailDao;
	
	@Override
	public OutMailDO get(Integer id){
		return outMailDao.get(id);
	}
	
	@Override
	public List<OutMailDO> list(Map<String, Object> map){
		return outMailDao.list(map);
	}
	@Override
	public List<OutMailDO> hjList(Map<String, Object> map){
		return outMailDao.hjList(map);
	}
	


	@Override
	public int update(OutMailDO outMail){
		return outMailDao.update(outMail);
	}
	
	@Override
	public int remove(Integer id){
		return outMailDao.remove(id);
	}
	
	@Override
	public int batchRemove(Integer[] ids){
		return outMailDao.batchRemove(ids);
	}

	@Override
	public List<OutMailDO> detail(OutMailDT outMailDT) {
		return outMailDao.detail(outMailDT);
	}

	@Override
	public Integer detailCount(OutMailDT outMailDT) {
		return outMailDao.detailCount(outMailDT);
	}

}
