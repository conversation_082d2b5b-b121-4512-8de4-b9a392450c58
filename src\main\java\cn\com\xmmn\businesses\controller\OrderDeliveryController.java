package cn.com.xmmn.businesses.controller;

import java.math.BigDecimal;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import cn.com.xmmn.businesses.domain.OrderDeliveryDO;
import cn.com.xmmn.businesses.service.OrderDeliveryService;
import cn.com.xmmn.common.utils.PageUtils;
import cn.com.xmmn.common.utils.Query;
import cn.com.xmmn.common.utils.R;

/**
 * 投递订单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-30 20:15:07
 */

@Slf4j
@Controller
@RequestMapping("/businesses/orderDelivery")
public class OrderDeliveryController {
    @Autowired
    private OrderDeliveryService orderDeliveryService;

    @GetMapping()
    @RequiresPermissions("businesses:orderDelivery:orderDelivery")
    String OrderDelivery() {
        return "businesses/orderDelivery/orderDelivery";
    }

    @ResponseBody
    @GetMapping("/list")
    @RequiresPermissions("businesses:orderDelivery:orderDelivery")
    public PageUtils list(@RequestParam Map<String, Object> params) {
        //查询列表数据
        Query query = new Query(params);
        List<OrderDeliveryDO> orderDeliveryList = orderDeliveryService.list(query);
        int total = orderDeliveryService.count(query);
        PageUtils pageUtils = new PageUtils(orderDeliveryList, total);
        return pageUtils;
    }

    @ResponseBody
    @PostMapping("/totalCount")
    @RequiresPermissions("businesses:orderDelivery:orderDelivery")
    public Map<String, Object> totalCount(String dateStart, String dateEnd) {
        Map<String, Object> data = new HashMap<>();
        Map<String, Object> query = new HashMap<>();
        query.put("dateStart", dateStart);
        query.put("dateEnd", dateEnd);
        //新增合计值
        Map<String, Object> countMap = orderDeliveryService.countList(query);
        BigDecimal weightCount = new BigDecimal(0);
        //新增重量合计
        if (countMap != null) {
            BigDecimal r1 = (BigDecimal) countMap.get("weights");
            weightCount = r1.setScale(3, BigDecimal.ROUND_HALF_UP);
            log.info("=========总重量=======================" + weightCount);
        }
        data.put("weightCount", weightCount);
        return data;
    }

    @GetMapping("/add")
    @RequiresPermissions("businesses:orderDelivery:add")
    String add() {
        return "businesses/orderDelivery/add";
    }

    @GetMapping("/edit/{id}")
    @RequiresPermissions("businesses:orderDelivery:edit")
    String edit(@PathVariable("id") Integer id, Model model) {
        OrderDeliveryDO orderDelivery = orderDeliveryService.get(id);
        model.addAttribute("orderDelivery", orderDelivery);
        return "businesses/orderDelivery/edit";
    }

    /**
     * 保存
     */
    @ResponseBody
    @PostMapping("/save")
    @RequiresPermissions("businesses:orderDelivery:add")
    public R save(OrderDeliveryDO orderDelivery) {
        if (orderDeliveryService.save(orderDelivery) > 0) {
            return R.ok();
        }
        return R.error();
    }

    /**
     * 修改
     */
    @ResponseBody
    @RequestMapping("/update")
    @RequiresPermissions("businesses:orderDelivery:edit")
    public R update(OrderDeliveryDO orderDelivery) {
        orderDeliveryService.update(orderDelivery);
        return R.ok();
    }

    /**
     * 删除
     */
    @PostMapping("/remove")
    @ResponseBody
    @RequiresPermissions("businesses:orderDelivery:remove")
    public R remove(Integer id) {
        if (orderDeliveryService.remove(id) > 0) {
            return R.ok();
        }
        return R.error();
    }

    /**
     * 删除
     */
    @PostMapping("/batchRemove")
    @ResponseBody
    @RequiresPermissions("businesses:orderDelivery:batchRemove")
    public R remove(@RequestParam("ids[]") Integer[] ids) {
        orderDeliveryService.batchRemove(ids);
        return R.ok();
    }

}
