package cn.com.xmmn.businesses.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import cn.com.xmmn.businesses.dao.OrderDeliveryDao;
import cn.com.xmmn.businesses.domain.OrderDeliveryDO;
import cn.com.xmmn.businesses.service.OrderDeliveryService;



@Service
public class OrderDeliveryServiceImpl implements OrderDeliveryService {
	@Autowired
	private OrderDeliveryDao orderDeliveryDao;
	
	@Override
	public OrderDeliveryDO get(Integer id){
		return orderDeliveryDao.get(id);
	}
	
	@Override
	public List<OrderDeliveryDO> list(Map<String, Object> map){
		return orderDeliveryDao.list(map);
	}

	@Override
	public Map<String, Object> countList(Map<String, Object> map) {
		return orderDeliveryDao.countList(map);
	}

	@Override
	public int count(Map<String, Object> map){
		return orderDeliveryDao.count(map);
	}
	
	@Override
	public int save(OrderDeliveryDO orderDelivery){
		return orderDeliveryDao.save(orderDelivery);
	}
	
	@Override
	public int update(OrderDeliveryDO orderDelivery){
		return orderDeliveryDao.update(orderDelivery);
	}
	
	@Override
	public int remove(Integer id){
		return orderDeliveryDao.remove(id);
	}
	
	@Override
	public int batchRemove(Integer[] ids){
		return orderDeliveryDao.batchRemove(ids);
	}


	@Override
	//查询hongkong
	public List<Map<String,Object>> hongkongList(Map<String,Object> map){
		return orderDeliveryDao.hongkongList(map);
	}

	@Override
	//查询环比升降
	public List<Map<String,Object>> huanbiList(Map<String,Object> map){
		return orderDeliveryDao.huanbiList(map);
	}

	@Override
	//查询妥投率-饼图
	public List<Map<String,Object>> tuotou(Map<String,Object> map){
		return orderDeliveryDao.tuotou(map);
	}

	@Override
	//查询妥投率-折线图
	public List<Map<String,Object>> tuotoulv(Map<String,Object> map){
		return orderDeliveryDao.tuotoulv(map);
	}

	@Override
	//查询某个日期是第几周
	public int week(String dateStr){
		return orderDeliveryDao.week(dateStr);
	}
	
}
