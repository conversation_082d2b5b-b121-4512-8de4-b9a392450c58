package cn.com.xmmn.businesses.vo;

import cn.com.xmmn.businesses.domain.ImportMailDO;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

/**
 * 进口全程时限统计表VO
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-20 15:02:00
 */
@Data
public class ImportFullTimeListVO  extends ImportMailDO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 发运-接收（天数）
     */
    @ExcelProperty("发运-接收（天数）")
    private Double dayShipmentToReceipt;

    /**
     * 开拆-封发（天数）
     */
    @ExcelProperty("开拆-封发（天数）")
    private Double dayOpenToSeal;


    /**
     * 封发-发运（天数）
     */
    @ExcelProperty("封发-发运（天数）")
    private Double daySealToShipment;

    /**
     * 发运-投递（天数）
     */
    @ExcelProperty("发运-投递（天数）")
    private Double dayShipmentToDelivery;

    /**
     * 全程时限（接收-投递）逾限天数
     */
    @ExcelProperty("全程时限（接收-投递）逾限天数")
    private Double totalDays;

    //超时预警(5天）
    @ExcelProperty("超时预警(5天）")
    private String status;
}
