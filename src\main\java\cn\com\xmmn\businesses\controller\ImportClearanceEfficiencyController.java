package cn.com.xmmn.businesses.controller;

import cn.com.xmmn.businesses.domain.ImportClearanceEfficiencyDO;
import cn.com.xmmn.businesses.service.ImportClearanceEfficiencyService;
import cn.com.xmmn.common.utils.ApiResponseData;
import cn.com.xmmn.common.utils.PageUtils;
import cn.com.xmmn.common.utils.Query;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;

/**
 * @Description 进口通关效率
 * <AUTHOR>
 * @Date 2024/5/24 15:26
 */
@Controller
@RequestMapping("/import/clearance/efficiency")
@Slf4j
public class ImportClearanceEfficiencyController {


    @Autowired
    private ImportClearanceEfficiencyService importClearanceEfficiencyService;


    @GetMapping()
    @RequiresPermissions("import:clearanceEfficiency")
    String ImportMail() {
         System.out.println("进口通关效率跳转");
        return "import/clearance/efficiency";

    }


    @ResponseBody
    @GetMapping("/list")
    @RequiresPermissions("import:clearanceEfficiency")
    public  PageUtils list(@RequestParam Map<String, Object> params) {
        //查询列表数据
        Query query = new Query(params);
        log.info("进口通关效率列表参数:{}" + query);
        Page<ImportClearanceEfficiencyDO> importClearanceEfficiencyDOPage = importClearanceEfficiencyService.pageImport(query);
        PageUtils pageUtils = new PageUtils(importClearanceEfficiencyDOPage.getRecords(),(int)importClearanceEfficiencyDOPage.getTotal());
        return pageUtils;
    }

    /**
     * 获取占比信息
     *
     * @return R
     **/
    @ResponseBody
    @GetMapping("/getProportion")
    @RequiresPermissions("import:clearanceEfficiency")
    public ApiResponseData getProportion(@RequestParam Map<String, Object> params) {
        return ApiResponseData.ok(importClearanceEfficiencyService.getProportion(params));
    }


    /**
     * 导出数据
     *
     * @param map
     * @param response 入参
     * @return void
     **/
    @RequestMapping(value = "/exportExcel", method = RequestMethod.GET)
    public void exportExcel(@RequestParam Map<String, Object> map, HttpServletResponse response) throws IOException {
        List<ImportClearanceEfficiencyDO> exportList = importClearanceEfficiencyService.list(map);//账单

        //导出
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");
        // 文件名
        String fileName = map.get("dateStart") + "进口通关效率表" + ".xlsx";
        try {
            fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");

        try {
            //导出
            EasyExcel.write(response.getOutputStream(), ImportClearanceEfficiencyDO.class).sheet("进口通关效率表").doWrite(exportList);
        } catch (Exception e) {
            log.info("excel文档导出错误-异常信息：" + e);
            e.printStackTrace();
        }

    }
}
