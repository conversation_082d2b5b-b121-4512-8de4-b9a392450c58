package cn.com.xmmn.businesses.controller;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import cn.com.xmmn.businesses.domain.OrdersImportDO;
import cn.com.xmmn.businesses.service.OrdersImportService;
import cn.com.xmmn.common.utils.PageUtils;
import cn.com.xmmn.common.utils.Query;
import cn.com.xmmn.common.utils.R;

/**
 * 进口订单表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-30 12:26:11
 */
 
@Controller
@RequestMapping("/businesses/ordersImport")
@Slf4j
public class OrdersImportController {
	@Autowired
	private OrdersImportService ordersImportService;
	
	@GetMapping()
	@RequiresPermissions("businesses:ordersImport:ordersImport")
	String OrdersImport(){
	    return "businesses/ordersImport/ordersImport";
	}
	
	@ResponseBody
	@GetMapping("/list")
	@RequiresPermissions("businesses:ordersImport:ordersImport")
	public PageUtils list(@RequestParam Map<String, Object> params){
		//查询列表数据
        Query query = new Query(params);
		List<OrdersImportDO> ordersImportList = ordersImportService.list(query);
		int total = ordersImportService.count(query);
		PageUtils pageUtils = new PageUtils(ordersImportList, total);
		return pageUtils;
	}

	@ResponseBody
	@PostMapping("/totalCount")
	@RequiresPermissions("businesses:ordersImport:ordersImport")
	public Map<String,Object> totalCount(String dateStart,String dateEnd){
		Map<String,Object> data = new HashMap<>();
		Map<String,Object> queryMap = new HashMap<>();
		queryMap.put("dateStart",dateStart);
		queryMap.put("dateEnd",dateEnd);
		//新增合计值
		Map<String, Object> countMap = ordersImportService.countList(queryMap);
		BigDecimal weightCount = new BigDecimal(0);
		BigDecimal moneyCount = new BigDecimal(0);
		//新增重量合计
		if (countMap!=null){
			BigDecimal r1 = (BigDecimal) countMap.get("weights");
			weightCount = r1.setScale(3, BigDecimal.ROUND_HALF_UP);
			log.info("=========总重量======================="+weightCount);
			BigDecimal r2 = (BigDecimal) countMap.get("moneys");
			moneyCount = r2.setScale(3, BigDecimal.ROUND_HALF_UP);
			log.info("==========总价格======================"+moneyCount);
		}
		data.put("weights",weightCount);
		data.put("moneys",moneyCount);
		return data;
	}

	@GetMapping("/add")
	@RequiresPermissions("businesses:ordersImport:add")
	String add(){
	    return "businesses/ordersImport/add";
	}

	@GetMapping("/edit/{id}")
	@RequiresPermissions("businesses:ordersImport:edit")
	String edit(@PathVariable("id") Integer id,Model model){
		OrdersImportDO ordersImport = ordersImportService.get(id);
		model.addAttribute("ordersImport", ordersImport);
	    return "businesses/ordersImport/edit";
	}
	
	/**
	 * 保存
	 */
	@ResponseBody
	@PostMapping("/save")
	@RequiresPermissions("businesses:ordersImport:add")
	public R save( OrdersImportDO ordersImport){
		if(ordersImportService.save(ordersImport)>0){
			return R.ok();
		}
		return R.error();
	}
	/**
	 * 修改
	 */
	@ResponseBody
	@RequestMapping("/update")
	@RequiresPermissions("businesses:ordersImport:edit")
	public R update( OrdersImportDO ordersImport){
		ordersImportService.update(ordersImport);
		return R.ok();
	}
	
	/**
	 * 删除
	 */
	@PostMapping( "/remove")
	@ResponseBody
	@RequiresPermissions("businesses:ordersImport:remove")
	public R remove( Integer id){
		if(ordersImportService.remove(id)>0){
		return R.ok();
		}
		return R.error();
	}
	
	/**
	 * 删除
	 */
	@PostMapping( "/batchRemove")
	@ResponseBody
	@RequiresPermissions("businesses:ordersImport:batchRemove")
	public R remove(@RequestParam("ids[]") Integer[] ids){
		ordersImportService.batchRemove(ids);
		return R.ok();
	}
	
}
