package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.ImportHhjDisposeDao;
import cn.com.xmmn.businesses.domain.ImportClearanceEfficiencyDO;
import cn.com.xmmn.businesses.domain.ImportHhjDisposeDO;
import cn.com.xmmn.businesses.service.ImportHhjDisposeService;
import cn.com.xmmn.businesses.strategy.ImportHhjDisposeExportStrategy;
import cn.com.xmmn.businesses.strategy.ImportHhjDisposeExportStrategyFactory;
import cn.com.xmmn.common.utils.Convert;
import cn.com.xmmn.common.utils.StringUtils;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;


@Service
public class ImportHhjDisposeServiceImpl extends ServiceImpl<ImportHhjDisposeDao, ImportHhjDisposeDO> implements ImportHhjDisposeService {


    @Autowired
    private ImportHhjDisposeDao importHhjDisposeDao;
    @Autowired
    private ImportHhjDisposeExportStrategyFactory disposeExportStrategyFactory;

    @Override
    public List<ImportHhjDisposeDO> list(Map<String, Object> query) {

//        List<ImportHhjDisposeDO> list = null;
//        QueryWrapper queryWrapper = getQueryWrapper(query);
//
//        Integer size = ObjectUtil.isNull(query.get("limit")) ? null : Convert.toInt(query.get("limit"));
//        //偏移量,从哪一条开始 -- MyBatis-Plus 的分页中不需要 offset 参数，因为分页插件会自动处理
//        //Integer offsetObj = ObjectUtil.isNull(params.get("offset")) ? 0 : Convert.toInt(params.get("offset"));
//        //当前页数
//        Integer current = ObjectUtil.isNull(query.get("page")) ? null : Convert.toInt(query.get("page"));
//        if (null == size && current == null) {
//            importHhjDisposeDao.selectList(queryWrapper);
//        } else {
//            Page page = importHhjDisposeDao.selectPage(new Page(current, size), queryWrapper);
//            list = page.getRecords();
//        }
        return importHhjDisposeDao.list(query);
    }

    @Override
    public int count(Map<String, Object> query) {
        return importHhjDisposeDao.count(query).intValue();
    }

    @Override
    public List<ImportHhjDisposeDO> listGrouped(Map<String, Object> query) {
        QueryWrapper queryWrapper = getQueryWrapper(query);
        //分组字段
        queryWrapper.groupBy("org_no", "province_code");

        // 指定统计字段
        queryWrapper.select(
                "org_no",
                "province_code",
                "SUM(js_sum) as jsSum",    // 接收量总和
                "SUM(wjs_sum) as wjsSum", // 未接收量总和
                "SUM(ycl_sum) as yclSum", // 应处理量总和
                "SUM(kc_sum) as kcSum",   // 开拆量总和
                "AVG(kc_rate) as kcRate"  // 开拆率平均值

        );
        return importHhjDisposeDao.selectList(queryWrapper);
    }

    @Override
    public int groupCount(Map<String, Object> query) {
        return 0;
    }


    // 根据前端传递的类型选择不同的导出策略
    @Override
    public void handleDetailsExport(Map<String, Object> map, HttpServletResponse response) {
        //导出类型
        String exportType= Convert.toStr(map.get("exportType"));
        //设置策略
        ImportHhjDisposeExportStrategy exportStrategy = disposeExportStrategyFactory.getExportTypeHandleStrategy(exportType);
        //执行策略
        exportStrategy.exportExcel(map,response);
    }

    private QueryWrapper getQueryWrapper(Map<String, Object> query) {


        String orgNo = Convert.toStr(query.get("orgNo"));
        String ptTimeStart = Convert.toStr(query.get("ptTimeStart"));
        String ptTimeEnd = Convert.toStr(query.get("ptTimeEnd"));
        QueryWrapper<ImportHhjDisposeDO> queryWrapper = new QueryWrapper<ImportHhjDisposeDO>();
        if (StringUtils.isNotEmpty(orgNo)) {
            queryWrapper.eq("org_no", orgNo);
        }
        if (StringUtils.isNotEmpty(ptTimeStart) && StringUtils.isNotEmpty(ptTimeEnd)) {
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            LocalDate startDate = LocalDate.parse(ptTimeStart, dateFormatter);
            LocalDate endDate = LocalDate.parse(ptTimeEnd, dateFormatter);

            LocalDateTime startDateTime = startDate.atStartOfDay();
            LocalDateTime endDateTime = endDate.atTime(23, 59, 59);
            queryWrapper.between("pt_time", startDateTime, endDateTime);
        }
        //orgNo provinceCode orgNo
        return queryWrapper;
    }
}
