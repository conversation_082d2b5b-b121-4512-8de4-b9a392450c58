package cn.com.xmmn.common.enums;

import java.util.Arrays;

/**
 * 导出名称
 */
public enum ImportHhjDisposeExportNameEnum {

    YCLHANDLED("yclHandled", "应处理信息"),
    kchandled("kcHandled", "开拆信息"),
    rqkchandled("rqkcHandled", "逾期开拆信息"),
    WKCHANDLED("wkcHandled", "未开拆信息"),
    DTGHANDLED("dtgHandled", "待通关信息"),
    YFFHANDLED("yffHandled", "已封发信息"),
    YQFFHANDLED("yqffHandled", "逾期封发信息"),
    WFFHANDLED("wffHandled", "未封发信息"),
    FYHANDLED("fyHandled", "已发运信息"),
    RQSXNHANDLED("rqSxnHandled", "逾期发运信息"),
    RQFYHANDLED("rqfyHandled", "逾期发运信息"),
    //(邮件)封发-发运
    WFYHANDLED("wfyHandled", "未发运信息"),
     //发运-投递
     WTTFYHANDLED("wttfyHandled", "未发运信息"),
    NEXTSUMHANDLED("nextSumHandled", "下一环节接收信息"),
    NEXTNOSUMHANDLED("nextNoSumHandled", "下一环节未接收信息"),
    ;
    private String type;
    private String name;

    ImportHhjDisposeExportNameEnum(String type, String name) {

        this.type = type;
        this.name = name;
    }


    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    /**
     * 根据类型获取名称
     * @param type
     * @return
     */
    public  static String getName(String type) {
        return Arrays.stream(ImportHhjDisposeExportNameEnum.values())
                .filter(enumValue -> enumValue.getType().equals(type))
                .map(ImportHhjDisposeExportNameEnum::getName)
                .findFirst()
                .orElse(null); // 如果没有匹配项，返回null
    }
}
