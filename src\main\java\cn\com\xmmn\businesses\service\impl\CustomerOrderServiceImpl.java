package cn.com.xmmn.businesses.service.impl;


import cn.com.xmmn.businesses.dao.CustomerOrderDao;
import cn.com.xmmn.businesses.dao.CustomerOrderDeclareDao;
import cn.com.xmmn.businesses.domain.CustomerOrderDO;
import cn.com.xmmn.businesses.domain.CustomerOrderDeclareDO;
import cn.com.xmmn.businesses.domain.HkCarrierDealerDO;
import cn.com.xmmn.businesses.dt.api.*;
import cn.com.xmmn.businesses.service.CustomerOrderDeclareService;
import cn.com.xmmn.businesses.service.CustomerOrderService;
import cn.com.xmmn.businesses.utils.AESUtils;
import cn.com.xmmn.common.exception.BusinessException;
import cn.com.xmmn.common.utils.DateUtils;
import cn.com.xmmn.common.utils.EncryptUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;


@Service
@Slf4j
@RequiredArgsConstructor
public class CustomerOrderServiceImpl implements CustomerOrderService {


    private final CustomerOrderDao customerOrderDao;

    private final CustomerOrderDeclareDao customerOrderDeclareDao;

    @Override
    public CustomerOrderDO get(Integer id) {
        return customerOrderDao.get(id);
    }

    @Override
    public List<CustomerOrderDO> list(Map<String, Object> map) {
        return customerOrderDao.list(map);
    }

    @Override
    public int count(Map<String, Object> map) {
        return customerOrderDao.count(map);
    }

    @Override
    public int save(CustomerOrderDO customerOrder) {
        return customerOrderDao.save(customerOrder);
    }

    @Override
    public int update(CustomerOrderDO customerOrder) {
        return customerOrderDao.update(customerOrder);
    }

    @Override
    public int remove(Integer id) {
        return customerOrderDao.remove(id);
    }

    @Override
    public int batchRemove(Integer[] ids) {
        return customerOrderDao.batchRemove(ids);
    }


    @Override
    @Transactional(rollbackFor =Exception.class)
    public void receiveCustomerOrder(String encryptedData) {
        try {
            //解密
          //  String carrierDealerJson = AESUtils.decryptByBytes(encryptedData);
            String carrierDealerJson= EncryptUtils.aesSha1prngDecrypt(encryptedData);
            log.info("客户订单解析出:{}", carrierDealerJson);

            //解析json
            ReceiveMailOrderListDT receiveMailOrderListDT1 = JSON.parseObject(carrierDealerJson, ReceiveMailOrderListDT.class);
            if (null != receiveMailOrderListDT1) {
                //订单列表
                List<ReceiveMailOrderDT> orderList = receiveMailOrderListDT1.getOrderList();
                orderList.forEach(receiveMailOrderDT -> {

                    CustomerOrderDO customerOrderDT = new CustomerOrderDO();

                    //特殊处理字符串转为时间格式
                    String scanTime = receiveMailOrderDT.getScanTime();
                    customerOrderDT.setScanTime(DateUtils.convertStringToDate(scanTime, "yyyy-MM-dd HH:mm:ss"));

                    //拷贝客户订单信息
                    BeanUtils.copyProperties(receiveMailOrderDT, customerOrderDT);
                    //创建客户订单信息
                    customerOrderDao.save(customerOrderDT);

                    Integer customerOrderId = customerOrderDT.getId();

                    //申报信息
                    ReceiveMailOrderCustomDT orderCustoms = receiveMailOrderDT.getOrderCustoms();

                    String customsType = orderCustoms.getCustomsType();
                    String currency = orderCustoms.getCurrency();
                    List<ReceiveMailDeclarationDT> customsItemList = orderCustoms.getCustomsItemList();

                    customsItemList.forEach(receiveMailDeclarationDT -> {
                        CustomerOrderDeclareDO customerOrderDeclareDT = new CustomerOrderDeclareDO();
                        customerOrderDeclareDT.setCustomsType(customsType);
                        customerOrderDeclareDT.setCurrency(currency);
                        //拷贝客户订单信息
                        BeanUtils.copyProperties(receiveMailDeclarationDT, customerOrderDeclareDT);
                        customerOrderDeclareDT.setCustometOrderId(customerOrderId);
                        customerOrderDeclareDao.save(customerOrderDeclareDT);
                    });

                });
            } else {
                throw new BusinessException("data数据为空");
            }
        } catch (Exception e) {
            throw new BusinessException(500, "处理客户订单信息时发生错误: " + e.getMessage());
        }

    }

}
