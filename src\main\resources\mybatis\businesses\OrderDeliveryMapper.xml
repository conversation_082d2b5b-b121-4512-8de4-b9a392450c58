<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.xmmn.businesses.dao.OrderDeliveryDao">

    <select id="get" resultType="cn.com.xmmn.businesses.domain.OrderDeliveryDO">
        select `id`,
               `revision`,
               `create_user_id`,
               `create_user_name`,
               `create_dept_id`,
               `create_dept_name`,
               `create_time`,
               `update_user_id`,
               `update_user_name`,
               `update_dept_id`,
               `update_dept_name`,
               `update_time`,
               `tracking_no`,
               `export_port_code`,
               `export_port_name`,
               `address_type`,
               `received_time`,
               `fee_weight`,
               `delivery_site_no`,
               `success_delivery_time`,
               `status`
        from t_order_delivery
        where id = #{value}
    </select>

    <select id="list" resultType="cn.com.xmmn.businesses.domain.OrderDeliveryDO">
        select
        `id`,`revision`,`create_user_id`,`create_user_name`,`create_dept_id`,`create_dept_name`,`create_time`,`update_user_id`,`update_user_name`,`update_dept_id`,`update_dept_name`,`update_time`,`tracking_no`,`received_time`,`fee_weight`,`delivery_site_no`,`success_delivery_time`,`status`
        from t_order_delivery
        <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="revision != null and revision != ''">and revision = #{revision}</if>
            <if test="createUserId != null and createUserId != ''">and create_user_id = #{createUserId}</if>
            <if test="createUserName != null and createUserName != ''">and create_user_name = #{createUserName}</if>
            <if test="createDeptId != null and createDeptId != ''">and create_dept_id = #{createDeptId}</if>
            <if test="createDeptName != null and createDeptName != ''">and create_dept_name = #{createDeptName}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateUserId != null and updateUserId != ''">and update_user_id = #{updateUserId}</if>
            <if test="updateUserName != null and updateUserName != ''">and update_user_name = #{updateUserName}</if>
            <if test="updateDeptId != null and updateDeptId != ''">and update_dept_id = #{updateDeptId}</if>
            <if test="updateDeptName != null and updateDeptName != ''">and update_dept_name = #{updateDeptName}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="trackingNo != null and trackingNo != ''">and tracking_no = #{trackingNo}</if>
            <if test="exportPortCode != null and exportPortCode != ''">and export_port_code = #{exportPortCode}</if>
            <if test="exportPortCodeName != null and exportPortCodeName != ''">and export_port_name =
                #{exportPortCodeName}
            </if>
            <if test="addressType != null and addressType != ''">and address_type = #{addressType}</if>
            <if test="dateStart != null and dateStart != ''">and received_time >= #{dateStart}</if>
            <if test="dateEnd != null and dateEnd != ''">and received_time &lt; date_add(#{dateEnd}, INTERVAL 1
                day)
            </if>
            <if test="feeWeight != null and feeWeight != ''">and fee_weight = #{feeWeight}</if>
            <if test="deliverySiteNo != null and deliverySiteNo != ''">and delivery_site_no = #{deliverySiteNo}</if>
            <if test="successDeliveryTime != null and successDeliveryTime != ''">and success_delivery_time =
                #{successDeliveryTime}
            </if>
            <if test="status != null and status != ''">and status = #{status}</if>
        </where>
        <choose>
            <when test="sort != null and sort.trim() != ''">
                 order by id desc
            </when>
            <otherwise>
                order by id desc
            </otherwise>
        </choose>
        <if test="offset != null and limit != null">
            limit #{offset}, #{limit}
        </if>
    </select>

    <select id="countList" resultType="java.util.Map">
        SELECT SUM( fee_weight ) as weights
        FROM t_order_delivery
        <where>
            <if test="dateStart != null and dateStart != ''">and received_time >= #{dateStart}</if>
            <if test="dateEnd != null and dateEnd != ''">and received_time &lt; date_add(#{dateEnd}, INTERVAL 1
                day)
            </if>
        </where>
    </select>


    <select id="count" resultType="int">
        select count(*) from t_order_delivery
        <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="revision != null and revision != ''">and revision = #{revision}</if>
            <if test="createUserId != null and createUserId != ''">and create_user_id = #{createUserId}</if>
            <if test="createUserName != null and createUserName != ''">and create_user_name = #{createUserName}</if>
            <if test="createDeptId != null and createDeptId != ''">and create_dept_id = #{createDeptId}</if>
            <if test="createDeptName != null and createDeptName != ''">and create_dept_name = #{createDeptName}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateUserId != null and updateUserId != ''">and update_user_id = #{updateUserId}</if>
            <if test="updateUserName != null and updateUserName != ''">and update_user_name = #{updateUserName}</if>
            <if test="updateDeptId != null and updateDeptId != ''">and update_dept_id = #{updateDeptId}</if>
            <if test="updateDeptName != null and updateDeptName != ''">and update_dept_name = #{updateDeptName}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="trackingNo != null and trackingNo != ''">and tracking_no = #{trackingNo}</if>
            <if test="dateStart != null and dateStart != ''">and received_time >= #{dateStart}</if>
            <if test="dateEnd != null and dateEnd != ''">and received_time &lt; date_add(#{dateEnd}, INTERVAL 1
                day)
            </if>
            <if test="feeWeight != null and feeWeight != ''">and fee_weight = #{feeWeight}</if>
            <if test="feeWeight != null and feeWeight != ''">and fee_weight = #{feeWeight}</if>
            <if test="deliverySiteNo != null and deliverySiteNo != ''">and delivery_site_no = #{deliverySiteNo}</if>
            <if test="successDeliveryTime != null and successDeliveryTime != ''">and success_delivery_time =
                #{successDeliveryTime}
            </if>
            <if test="status != null and status != ''">and status = #{status}</if>
        </where>
    </select>

    <insert id="save" parameterType="cn.com.xmmn.businesses.domain.OrderDeliveryDO" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_order_delivery
        (`revision`,
         `create_user_id`,
         `create_user_name`,
         `create_dept_id`,
         `create_dept_name`,
         `create_time`,
         `update_user_id`,
         `update_user_name`,
         `update_dept_id`,
         `update_dept_name`,
         `update_time`,
         `tracking_no`,
         `export_port_code`,
         `export_port_name`,
         `address_type`,
         `received_time`,
         `fee_weight`,
         `delivery_site_no`,
         `success_delivery_time`,
         `status`)
        values (#{revision},
                #{createUserId},
                #{createUserName},
                #{createDeptId},
                #{createDeptName},
                #{createTime},
                #{updateUserId},
                #{updateUserName},
                #{updateDeptId},
                #{updateDeptName},
                #{updateTime},
                #{trackingNo},
                #{exportPortCode},
                #{exportPortCodeName},
                #{addressType},
                #{receivedTime},
                #{feeWeight},
                #{deliverySiteNo},
                #{successDeliveryTime},
                #{status}) ON DUPLICATE KEY
        UPDATE update_time=now(),fee_weight = #{feeWeight},delivery_site_no = #{deliverySiteNo},success_delivery_time = #{successDeliveryTime},status = #{status}
    </insert>

    <update id="update" parameterType="cn.com.xmmn.businesses.domain.OrderDeliveryDO">
        update t_order_delivery
        <set>
            <if test="revision != null">`revision` = #{revision},</if>
            <if test="createUserId != null">`create_user_id` = #{createUserId},</if>
            <if test="createUserName != null">`create_user_name` = #{createUserName},</if>
            <if test="createDeptId != null">`create_dept_id` = #{createDeptId},</if>
            <if test="createDeptName != null">`create_dept_name` = #{createDeptName},</if>
            <if test="createTime != null">`create_time` = #{createTime},</if>
            <if test="updateUserId != null">`update_user_id` = #{updateUserId},</if>
            <if test="updateUserName != null">`update_user_name` = #{updateUserName},</if>
            <if test="updateDeptId != null">`update_dept_id` = #{updateDeptId},</if>
            <if test="updateDeptName != null">`update_dept_name` = #{updateDeptName},</if>
            <if test="updateTime != null">`update_time` = #{updateTime},</if>
            <if test="trackingNo != null">`tracking_no` = #{trackingNo},</if>
            <if test="receivedTime != null">`received_time` = #{receivedTime},</if>
            <if test="feeWeight != null">`fee_weight` = #{feeWeight},</if>
            <if test="deliverySiteNo != null">`delivery_site_no` = #{deliverySiteNo},</if>
            <if test="successDeliveryTime != null">`success_delivery_time` = #{successDeliveryTime},</if>
            <if test="status != null">`status` = #{status}</if>
        </set>
        where id = #{id}
    </update>

    <delete id="remove">
        delete
        from t_order_delivery
        where id = #{value}
    </delete>

    <delete id="batchRemove">
        delete from t_order_delivery where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <!--查询投递业务量变化报表-->
    <select id="hongkongList" resultType="java.util.LinkedHashMap" parameterType="Map">
        SELECT IFNULL(c.portName,'') portName
        <foreach item="item" index="index" collection="title">
            ,sum(case when c.ll=#{item} then c.num else 0 end) as #{item}
        </foreach>
        from (SELECT export_port_name portName, date_format(received_time ,'%Y-%m' ) ll,count(1) num
        FROM t_order_delivery
        <where>
            received_time >= #{startDate}
            and received_time &lt; #{endDateStr}
        </where>
        group by portName ,ll
        order by portName) c group by c.portName
    </select>

    <!--查询投递业务量变化报表右侧的环比-->
    <select id="huanbiList" resultType="java.util.LinkedHashMap" parameterType="Map">
        SELECT IFNULL(c.portName,'') portName
        <foreach item="item" index="index" collection="huanbi">
            ,sum(case when c.ll=#{item} then c.num else 0 end) as #{item}
        </foreach>
        FROM
        (select '总计' portName, date_format( received_time, '%Y-%m' ) ll,count(id) num from t_order_delivery where
        received_time >= #{huanbiStart}
        and received_time &lt; #{endDateStr}
        group by ll) c
        GROUP BY
        c.portName
        union
        SELECT c.portName
        <foreach item="item" index="index" collection="huanbi">
            ,sum(case when c.ll=#{item} then c.num else 0 end) as #{item}
        </foreach>
        from (SELECT export_port_name portName, date_format(received_time ,'%Y-%m' ) ll,count(1) num
        FROM t_order_delivery
        group by portName ,ll
        order by portName) c group by c.portName
    </select>


    <!--统计每月投递失败件数，按投递失败原因分类-->
    <select id="tuotou" resultType="java.util.LinkedHashMap" parameterType="Map">
        select IFNULL(status,'')  name, count(id) value
        from t_order_delivery
        where date_format( received_time
            , '%Y-%m' ) = #{startDate}
          and status != '已投递'
        group by status
    </select>


    <!--折线图-统计每月妥投率-->
    <select id="tuotoulv" resultType="java.util.LinkedHashMap" parameterType="Map">
        SELECT
        A.WEEK week,
        IFNULL( B.rate, '100.00' ) rate
        FROM
        ( SELECT '第1周' AS WEEK UNION all SELECT '第2周' AS WEEK UNION all SELECT '第3周' AS WEEK UNION all SELECT '第4周' AS
        WEEK
        <if test="weeks == 5">UNION all SELECT '第5周' AS WEEK</if>
        <if test="weeks == 6">UNION all SELECT '第5周' AS WEEK UNION all SELECT '第6周' AS WEEK</if>
        ) A
        LEFT JOIN (
        SELECT
        CASE
        c.weeks
        WHEN '0' THEN
        '第1周'
        WHEN '1' THEN
        '第2周'
        WHEN '2' THEN
        '第3周'
        WHEN '3' THEN
        '第4周'
        WHEN '4' THEN
        '第5周' ELSE '第6周'
        END WEEK,
        c.count rate
        FROM
        (
        SELECT
        ( DATE_FORMAT( received_time, '%u' ) - ( SELECT WEEK ( #{monthFristDay}, 1 ) ) ) weeks,
        round( count( STATUS = '已投递' OR NULL ) / count( 1 ) * 100, 2 ) count
        FROM
        t_order_delivery
        WHERE
        date_format(received_time ,'%Y-%m' ) = #{monthStr}
        GROUP BY
        weeks
        ) c
        ) B ON A.WEEK = B.WEEK order by week;
    </select>

    <!--传日期，查询是第几周-->
    <select id="week" resultType="Integer">
        select DATE_FORMAT(#{dateStr}, '%u')
        from dual;
    </select>
</mapper>