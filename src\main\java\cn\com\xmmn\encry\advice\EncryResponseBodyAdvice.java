//package cn.com.xmmn.encry.advice;
//
//import cn.com.xmmn.businesses.utils.AESUtils;
//import com.alibaba.fastjson.JSON;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.core.MethodParameter;
//import org.springframework.http.MediaType;
//import org.springframework.http.converter.HttpMessageConverter;
//import org.springframework.http.server.ServerHttpRequest;
//import org.springframework.http.server.ServerHttpResponse;
//import org.springframework.http.server.ServletServerHttpRequest;
//import org.springframework.stereotype.Component;
//import org.springframework.web.bind.annotation.ControllerAdvice;
//import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;
//
//import javax.servlet.http.HttpServletRequest;
//
///**
// * 加密
// * <AUTHOR>
// * @email <EMAIL>
// * @date 2024年6月14日15:15:15
// */
//@Component
//@ControllerAdvice(basePackages = "cn.com.xmmn.businesses.controller.api")
//@Slf4j
//public class EncryResponseBodyAdvice implements ResponseBodyAdvice<Object> {
//    @Override
//    public boolean supports(MethodParameter methodParameter, Class<? extends HttpMessageConverter<?>> aClass) {
//        return true;
//    }
//    @Override
//    public Object beforeBodyWrite(Object o, MethodParameter methodParameter, MediaType mediaType, Class<? extends HttpMessageConverter<?>> aClass, ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse) {
//        //通过 ServerHttpRequest的实现类ServletServerHttpRequest 获得HttpServletRequest
//        ServletServerHttpRequest sshr = (ServletServerHttpRequest) serverHttpRequest;
//        //此处获取到request 是为了取到在拦截器里面设置的一个对象 是我项目需要,可以忽略
//        HttpServletRequest request = sshr.getServletRequest();
//
//        String returnStr = "";
//
//        try {
//            //添加encry header，告诉前端数据已加密
//            serverHttpResponse.getHeaders().add("encry", "true");
//            String srcData = JSON.toJSONString(o);
//            //加密
//            returnStr = AESUtils.encryptToBytes(srcData);
//            log.info("接口={},原始数据={},加密后数据={}", request.getRequestURI(), srcData, returnStr);
//
//        } catch (Exception e) {
//            log.error("异常！", e);
//        }
//        return returnStr;
//    }
//}
