package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.XgXydBillDetailDao;
import cn.com.xmmn.businesses.domain.XgXydBillDetailDO;
import cn.com.xmmn.businesses.service.XgXydBillDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;
@Service
public class XgXydBillDetailServiceImpl extends ServiceImpl<XgXydBillDetailDao, XgXydBillDetailDO> implements XgXydBillDetailService {
    @Override
    public void save(List<XgXydBillDetailDO> xgXydBillDetailDOs) {
        saveBatch(xgXydBillDetailDOs);
    }
}
