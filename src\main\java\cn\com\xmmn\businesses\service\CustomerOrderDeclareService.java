package cn.com.xmmn.businesses.service;

import cn.com.xmmn.businesses.domain.CustomerOrderDeclareDO;

import java.util.List;
import java.util.Map;

/**
 * 客户订单申报表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-26 17:12:05
 */
public interface CustomerOrderDeclareService {
	
	CustomerOrderDeclareDO get(Integer id);
	
	List<CustomerOrderDeclareDO> list(Map<String, Object> map);
	
	int count(Map<String, Object> map);
	
	int save(CustomerOrderDeclareDO customerOrderDeclare);
	
	int update(CustomerOrderDeclareDO customerOrderDeclare);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);
}
