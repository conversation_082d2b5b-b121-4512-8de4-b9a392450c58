package cn.com.xmmn.businesses.domain;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * pdf账单数据记录
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-22 16:04:23
 */
@Data
public class PdfBillDO implements Serializable {

	private static final long serialVersionUID = 1L;

	//出发地：香港
	private String originAddress;

	//航班号：CX056
	private String flightNO;

	//发运日期
	private Date departureDate;

	//发运月份 YYYYMM
	private String departureMonth;

	//目的地
	private String destinationAddress;

	//路由：来源口岸-出发地-目的地：SZX-HKG-NRT
	private String mailRoute;

	//邮件类型:EMS
	private String mailType;

	//总包号
	private String packageNo;

	//原寄局:CNSZXD
	private String originOffice;

	//口岸 原寄局:CNSZXD 中的 SZX
	private String port;

	//寄达局
	private String destinationOffice;

	//重量
	private Double weight;

	//袋号
	private Integer bagNo;

/////////////////////////////汇总字段
	//袋数
	private Integer bagCount;

	//结算单价
	private Double unitPrice;

	//结算金额
	private Double amount;

}
