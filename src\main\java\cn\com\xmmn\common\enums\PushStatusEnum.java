package cn.com.xmmn.common.enums;
/**
 *
 *  推送状态枚举
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024年10月9日10:04:19
 */
public enum PushStatusEnum {

    UNPUSHED(0, "未推送"),
    PUSH_SUCCESS(1, "推送成功"),
    PUSH_FAILED(2, "推送失败");

    private final int value;
    private final String description;

    PushStatusEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static PushStatusEnum fromValue(int value) {
        for (PushStatusEnum status : PushStatusEnum.values()) {
            if (status.getValue() == value) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid push status value: " + value);
    }
}
