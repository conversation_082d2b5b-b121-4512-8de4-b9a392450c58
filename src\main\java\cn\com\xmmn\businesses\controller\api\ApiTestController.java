package cn.com.xmmn.businesses.controller.api;


import cn.com.xmmn.businesses.config.DecryptKeyConfig;
import cn.com.xmmn.businesses.dt.api.DateRequest;
import cn.com.xmmn.businesses.utils.AESUtils;
import cn.com.xmmn.common.utils.ApiResponseData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 对外接口测试类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024年6月14日15:15:15
 */
@Slf4j
@RestController
public class ApiTestController {

    private String authorizationHeader = "rwerwe234234234";

    @Autowired
    private DecryptKeyConfig decryptKeyConfig;


    @GetMapping("/list")
    public ApiResponseData get(@RequestBody DateRequest request) {
        try {
            String decryptedData = AESUtils.decryptByBytes(request.getEncryptedData());
            Map<String, String> map = new HashMap<>();
            map.put("decryptedData", decryptedData);
            return ApiResponseData.ok(map);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ApiResponseData.ok();
    }


    @RequestMapping(value = "/api/test/getRequestData")
    public ApiResponseData getRequestData(@RequestBody DateRequest encryptedData) throws Exception {
        log.info("controller接收的参数object={}", encryptedData.toString());
        String dealData = AESUtils.decryptByBytes(encryptedData.getEncryptedData());
        return ApiResponseData.ok(dealData);
    }

    /**
     * 响应数据 加密
     */
    @RequestMapping(value = "/api/test/sendResponseEncryData")
    public ApiResponseData sendResponseEncryData() {
        Map<String, Object> map = new HashMap<>();
        map.put("name", "test");
        map.put("encry", true);
        return ApiResponseData.ok();
    }

    public static void main(String[] args) throws Exception {
        String contront = "VgrWiz9+nh+WNjs4KoMVXOI6K5lm79Ew1M/ch8RLZlPdNZh2gZg8OstPdtYDhV7vpwpaUlmGhnEU0cP5eYjSg0Y6CYcdaI1RSCSLap/DiNt0Pmd4nb+FT7hrKr9l61WDK5DhUykTsMxOyM0c+NF7hwQncAXL6kd/RkUxEjBLYvcOk7no6jqkVhIgM5RkVc3jqW/trpobAi98n/t+Whht73gmB8LBLl40ouiCR0jQ4mESG+ohReMCv4Iq6xGv6MK48WUISN3O/ODlWZC+V9apInQ3YZftDXlgM/xweX1vDcg=";
        String dealData = AESUtils.decryptByBytes(contront);
        System.out.println(dealData);
    }
}
