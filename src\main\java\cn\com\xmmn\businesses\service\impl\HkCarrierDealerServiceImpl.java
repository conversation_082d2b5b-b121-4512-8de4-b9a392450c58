package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.config.DecryptKeyConfig;
import cn.com.xmmn.businesses.dao.HkCarrierDealerDao;
import cn.com.xmmn.businesses.domain.HkCarrierDealerDO;
import cn.com.xmmn.businesses.dt.api.ReceiveMailbagDT;
import cn.com.xmmn.businesses.service.HkCarrierDealerService;
import cn.com.xmmn.businesses.utils.AESUtils;
import cn.com.xmmn.common.exception.BusinessException;
import cn.com.xmmn.common.utils.DateUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
@AllArgsConstructor
@Slf4j
public class HkCarrierDealerServiceImpl implements HkCarrierDealerService {

    private final HkCarrierDealerDao hkCarrierDealerDao;



    @Override
    public HkCarrierDealerDO get(Integer id) {
        return hkCarrierDealerDao.get(id);
    }

    @Override
    public List<HkCarrierDealerDO> list(Map<String, Object> map) {
        return hkCarrierDealerDao.list(map);
    }

    @Override
    public int count(Map<String, Object> map) {
        return hkCarrierDealerDao.count(map);
    }

    @Override
    public int save(HkCarrierDealerDO hkCarrierDealer) {
        return hkCarrierDealerDao.save(hkCarrierDealer);
    }

    @Override
    public int update(HkCarrierDealerDO hkCarrierDealer) {
        return hkCarrierDealerDao.update(hkCarrierDealer);
    }

    @Override
    public int remove(Integer id) {
        return hkCarrierDealerDao.remove(id);
    }

    @Override
    public int batchRemove(Integer[] ids) {
        return hkCarrierDealerDao.batchRemove(ids);
    }


    @Override
    public void receiveMailbag(String encryptedData) {
        try {
            //解密
            String carrierDealerJson = AESUtils.decryptByBytes(encryptedData);
            log.info("接收邮袋解析出:{}", carrierDealerJson);

            //解析json
            JSONObject jsonObject = JSONObject.parseObject(carrierDealerJson);
            Object data = jsonObject.get("data");
            if (null != data) {
                JSONArray dataArray = jsonObject.getJSONArray("data");

                 // 使用 FastJSON 的批量转换功能
                List<ReceiveMailbagDT> receiveMailbagDTS = dataArray.toJavaList(ReceiveMailbagDT.class);
                receiveMailbagDTS.forEach(receiveMailbagDT -> {
                    //CN38号
                    String billNo = receiveMailbagDT.getDeliveryBillNo();
                    //邮袋号
                    String bagCode = receiveMailbagDT.getBagBarcode();
                    //起飞时间
                    String departureDateTime = receiveMailbagDT.getDepartureDatetime();
                    //容器号
                    String containerNo = receiveMailbagDT.getContainerNo();
                    //接收时间
                    String receiveDatetime = receiveMailbagDT.getReceiveDatetime();
                    //承运商名称
                    String carrierName = receiveMailbagDT.getCarrieName();


                    HkCarrierDealerDO carrierDealerDO=new HkCarrierDealerDO();
                    Map<String, Object> map=new HashMap<>();
                    carrierDealerDO.setBagBarcode(bagCode);
                    carrierDealerDO.setDeliverBillNo(billNo);
                    carrierDealerDO.setContainerNumbers(containerNo);
                    carrierDealerDO.setDepartureDatetime(DateUtils.convertStringToDate(departureDateTime,"yyyyMMddHHmmss"));
                    carrierDealerDO.setReceiceDatetime(DateUtils.convertStringToDate(receiveDatetime,"yyyyMMddHHmmss"));
                    carrierDealerDO.setCarrierName(carrierName);
                    carrierDealerDO.setCreateTime(new Date());

                    map.put("bagBarcode", bagCode);

                    //2025-06-23 增加根据总包号判断，如果有，则更新，没有记录，则新写入
                    if(hkCarrierDealerDao.count(map)>0){
                        hkCarrierDealerDao.update(carrierDealerDO);
                    }else{
                        hkCarrierDealerDao.save(carrierDealerDO);
                    }


                });
            } else {
                throw new BusinessException("data数据为空");
            }
        } catch (Exception e) {
            throw new BusinessException(500, "处理邮件收寄信息时发生错误: " + e.getMessage());
        }

    }

    public static void main(String[] args) throws Exception {
        String encryptedData="0A+667raYzhB5TUqBFB";
        String carrierDealerJson = AESUtils.decryptByBytes(encryptedData);
        log.info("接收邮袋解析出:{}", carrierDealerJson);
    }
}
