package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.ImportMailDao;
import cn.com.xmmn.businesses.domain.ImportMailCargoDO;
import cn.com.xmmn.businesses.domain.ImportMailDO;
import cn.com.xmmn.businesses.domain.ImportMailReceiptDO;
import cn.com.xmmn.businesses.service.ImportMailService;
import cn.com.xmmn.businesses.vo.ImportMailShouldListVO;
import cn.com.xmmn.common.utils.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;



@Service
public class ImportMailServiceImpl implements ImportMailService {
	@Autowired
	private ImportMailDao importMailDao;
	
	@Override
	public ImportMailDO get(String itemId){
		return importMailDao.get(itemId);
	}
	
	@Override
	public List<ImportMailCargoDO> list(Map<String, Object> map){
		return importMailDao.list(map);
	}
	
	@Override
	public int count(Map<String, Object> map){
		return importMailDao.count(map);
	}
	
	@Override
	public int save(ImportMailDO importMail){
		return importMailDao.save(importMail);
	}
	
	@Override
	public int update(ImportMailDO importMail){
		return importMailDao.update(importMail);
	}
	
	@Override
	public int remove(Integer id){
		return importMailDao.remove(id);
	}
	
	@Override
	public int batchRemove(Integer[] ids){
		return importMailDao.batchRemove(ids);
	}

	@Override
	public List<ImportMailReceiptDO> ReceiptList(Map<String, Object> map) {
		return importMailDao.ReceiptList(map);
	}

	@Override
	public int ReceiptCount(Map<String, Object> map) {
		return importMailDao.ReceiptCount(map);
	}

	@Override
	public List<ImportMailShouldListVO> getShouldList(Map<String, Object> map) {

		return importMailDao.getShouldList(map);
	}

	@Override
	public int getShouldCount(Map<String, Object> map) {
		return importMailDao.getShouldCount(map);
	}

}
