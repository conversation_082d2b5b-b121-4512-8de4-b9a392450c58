package cn.com.xmmn.common.handler;

import cn.com.xmmn.common.config.Constant;
import cn.com.xmmn.common.domain.LogDO;
import cn.com.xmmn.common.exception.BusinessException;
import cn.com.xmmn.common.service.LogService;
import cn.com.xmmn.common.utils.ApiResponseData;
import cn.com.xmmn.common.utils.HttpServletUtils;
import cn.com.xmmn.common.utils.R;
import cn.com.xmmn.common.utils.ShiroUtils;
import cn.com.xmmn.system.domain.UserDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.AuthorizationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * 全局自定义处理异常
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024年7月25日09:44:35
 */

@RestControllerAdvice
public class GlobalExceptionHandler {
    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    @Autowired
    private LogService logService;


    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ApiResponseData> handleBusinessException(BusinessException ex) {
        ApiResponseData responseData = new ApiResponseData(ex.getCode(), ex.getMessage());
        return new ResponseEntity<>(responseData, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(AuthorizationException.class)
    public Object handleAuthorizationException(AuthorizationException e, HttpServletRequest request) {
        log.error(e.getMessage(), e);
        if (HttpServletUtils.jsAjax(request)) {
            return R.error(403, "未授权");
        }
        return new ModelAndView("error/403");
    }

    @ExceptionHandler(Exception.class)
    public Object handleException(Exception e, HttpServletRequest request) {
        LogDO logDO = new LogDO();
        logDO.setGmtCreate(new Date());
        logDO.setOperation(Constant.LOG_ERROR);
        logDO.setMethod(request.getRequestURL().toString());
        logDO.setParams(e.toString().length() > 2000 ? e.toString().substring(0, 2000) : e.toString()); // 截取超长
        UserDO current = ShiroUtils.getUser();
        if (current != null) {
            logDO.setUserId(current.getUserId());
            logDO.setUsername(current.getUsername());
        }
        logService.save(logDO);

        log.error("处理请求时发生错误: ", e);
        if (HttpServletUtils.jsAjax(request)) {
            return new ResponseEntity<>(new ApiResponseData(500, "服务器内部错误: " + e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
        return new ModelAndView("error/500");
    }
}
