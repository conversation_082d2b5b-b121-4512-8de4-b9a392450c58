package cn.com.xmmn.businesses.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description 承运商邮袋启运列表
 * <AUTHOR>
 * @Date 2024/6/4 16:47
 */
@Data
@HeadRowHeight(value = 30) // 头部行高
@ContentRowHeight(value = 25) // 内容行高
@ColumnWidth(value = 20) // 列宽
@HeadFontStyle(fontName = "宋体", fontHeightInPoints = 11) // 头部字体样式
public class CarrierDealerDepartListVo implements Serializable {
    @ExcelIgnore
    private String orgName;

    @ExcelIgnore
    private String orgCode;



    @ExcelProperty("承运商")
    private String carrierDealer;

    @ExcelIgnore
    private String carrierCode;

    @ExcelProperty("业务种类")
    private String product;

    @ExcelIgnore
    private String productCode;

    @ExcelProperty("寄达国家/地区名称")
    private String receiverCountryName;

    @ExcelIgnore
    private String receiverCountryCode;

    @ExcelIgnore
    private String oeDest;

    @ExcelProperty("寄达互换局名称")
    private String oeDestName;
    @ExcelProperty("承运商已接收袋数")
    private Integer receiveNum;

    @ExcelProperty("承运商已启运袋数")
    private Integer departNum;

    @ExcelProperty("正常启运邮袋（2小时内，含12小时）")
    private Integer normalDepartNum;

    @ExcelProperty("超时启运邮袋（12至24小时）")
    private Integer overtime12to24DepartNum;

    @ExcelProperty("超时启运邮袋（24小时以上）")
    private Integer overtime24PlusDepartNum;

    @ExcelProperty("承运商未启运袋数")
    private Integer notDepartNum;

    @ExcelProperty("异常袋数")
    private Integer exceptionBagNum;

    @ExcelIgnore
    private Integer total;
}
