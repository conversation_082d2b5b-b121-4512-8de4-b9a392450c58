<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.xmmn.businesses.dao.BatchDao">

	<select id="get" resultType="cn.com.xmmn.businesses.domain.BatchDO">
		select `id`,`revision`,`create_user_id`,`create_user_name`,`create_dept_id`,`create_dept_name`,`create_time`,`update_user_id`,`update_user_name`,`update_dept_id`,`update_dept_name`,`update_time`,`module_type`,`num` from t_batch where id = #{value}
	</select>

	<select id="list" resultType="cn.com.xmmn.businesses.domain.BatchDO">
		select `id`,`revision`,`create_user_id`,`create_user_name`,`create_dept_id`,`create_dept_name`,`create_time`,`update_user_id`,`update_user_name`,`update_dept_id`,`update_dept_name`,`update_time`,`module_type`,`num` from t_batch
        <where>  
		  		  <if test="id != null and id != ''"> and id = #{id} </if>
		  		  <if test="revision != null and revision != ''"> and revision = #{revision} </if>
		  		  <if test="createUserId != null and createUserId != ''"> and create_user_id = #{createUserId} </if>
		  		  <if test="createUserName != null and createUserName != ''"> and create_user_name = #{createUserName} </if>
		  		  <if test="createDeptId != null and createDeptId != ''"> and create_dept_id = #{createDeptId} </if>
		  		  <if test="createDeptName != null and createDeptName != ''"> and create_dept_name = #{createDeptName} </if>
		  		  <if test="createTime != null and createTime != ''"> and create_time = #{createTime} </if>
		  		  <if test="updateUserId != null and updateUserId != ''"> and update_user_id = #{updateUserId} </if>
		  		  <if test="updateUserName != null and updateUserName != ''"> and update_user_name = #{updateUserName} </if>
		  		  <if test="updateDeptId != null and updateDeptId != ''"> and update_dept_id = #{updateDeptId} </if>
		  		  <if test="updateDeptName != null and updateDeptName != ''"> and update_dept_name = #{updateDeptName} </if>
		  		  <if test="updateTime != null and updateTime != ''"> and update_time = #{updateTime} </if>
		  		  <if test="moduleType != null and moduleType != ''"> and module_type = #{moduleType} </if>
		  		  <if test="num != null and num != ''"> and num = #{num} </if>
		  		</where>
        <choose>
            <when test="sort != null and sort.trim() != ''">
                order by id desc
            </when>
			<otherwise>
                order by id desc
			</otherwise>
        </choose>
		<if test="offset != null and limit != null">
			limit #{offset}, #{limit}
		</if>
	</select>
	
 	<select id="count" resultType="int">
		select count(*) from t_batch
		 <where>  
		  		  <if test="id != null and id != ''"> and id = #{id} </if>
		  		  <if test="revision != null and revision != ''"> and revision = #{revision} </if>
		  		  <if test="createUserId != null and createUserId != ''"> and create_user_id = #{createUserId} </if>
		  		  <if test="createUserName != null and createUserName != ''"> and create_user_name = #{createUserName} </if>
		  		  <if test="createDeptId != null and createDeptId != ''"> and create_dept_id = #{createDeptId} </if>
		  		  <if test="createDeptName != null and createDeptName != ''"> and create_dept_name = #{createDeptName} </if>
		  		  <if test="createTime != null and createTime != ''"> and create_time = #{createTime} </if>
		  		  <if test="updateUserId != null and updateUserId != ''"> and update_user_id = #{updateUserId} </if>
		  		  <if test="updateUserName != null and updateUserName != ''"> and update_user_name = #{updateUserName} </if>
		  		  <if test="updateDeptId != null and updateDeptId != ''"> and update_dept_id = #{updateDeptId} </if>
		  		  <if test="updateDeptName != null and updateDeptName != ''"> and update_dept_name = #{updateDeptName} </if>
		  		  <if test="updateTime != null and updateTime != ''"> and update_time = #{updateTime} </if>
		  		  <if test="moduleType != null and moduleType != ''"> and module_type = #{moduleType} </if>
		  		  <if test="num != null and num != ''"> and num = #{num} </if>
		  		</where>
	</select>

	<insert id="save" parameterType="cn.com.xmmn.businesses.domain.BatchDO" useGeneratedKeys="true" keyProperty="id">
		insert into t_batch
		(
		     `id`,
			`revision`, 
			`create_user_id`, 
			`create_user_name`, 
			`create_dept_id`, 
			`create_dept_name`, 
			`create_time`, 
			`update_user_id`, 
			`update_user_name`, 
			`update_dept_id`, 
			`update_dept_name`, 
			`update_time`, 
			`module_type`, 
			`num`
		)
		values
		(
		    #{id},
		    #{revision},
			#{createUserId}, 
			#{createUserName}, 
			#{createDeptId}, 
			#{createDeptName}, 
			#{createTime}, 
			#{updateUserId}, 
			#{updateUserName}, 
			#{updateDeptId}, 
			#{updateDeptName}, 
			#{updateTime}, 
			#{moduleType}, 
			#{num}
		)
	</insert>

	 
	<update id="update" parameterType="cn.com.xmmn.businesses.domain.BatchDO">
		update t_batch 
		<set>
			<if test="revision != null">`revision` = #{revision}, </if>
			<if test="createUserId != null">`create_user_id` = #{createUserId}, </if>
			<if test="createUserName != null">`create_user_name` = #{createUserName}, </if>
			<if test="createDeptId != null">`create_dept_id` = #{createDeptId}, </if>
			<if test="createDeptName != null">`create_dept_name` = #{createDeptName}, </if>
			<if test="createTime != null">`create_time` = #{createTime}, </if>
			<if test="updateUserId != null">`update_user_id` = #{updateUserId}, </if>
			<if test="updateUserName != null">`update_user_name` = #{updateUserName}, </if>
			<if test="updateDeptId != null">`update_dept_id` = #{updateDeptId}, </if>
			<if test="updateDeptName != null">`update_dept_name` = #{updateDeptName}, </if>
			<if test="updateTime != null">`update_time` = #{updateTime}, </if>
			<if test="moduleType != null">`module_type` = #{moduleType}, </if>
			<if test="num != null">`num` = #{num}</if>
		</set>
		where id = #{id}
	</update>
	
	<delete id="remove">
		delete a,b from t_batch a left JOIN t_commercial_export_new b on a.id=b.batch_id where a.id = #{value}
	</delete>
	
	<delete id="batchRemove">
		delete a,b from t_batch a left JOIN t_commercial_export_new b on a.id=b.batch_id where a.id in
		<foreach item="id" collection="array" open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>

</mapper>