package cn.com.xmmn.businesses.service;

import cn.com.xmmn.businesses.domain.ImportHhjDisposeDO;
import cn.com.xmmn.common.utils.Query;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 进口时限统计表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-20 15:02:00
 */
public interface ImportHhjDisposeService extends IService<ImportHhjDisposeDO> {


    List<ImportHhjDisposeDO> list(Map<String,Object> query);


    int count(Map<String,Object> query);

    List<ImportHhjDisposeDO> listGrouped(Map<String,Object> query);

    int groupCount(Map<String,Object> query);

    /**
     * 导出详情
     * @param map
     */
    void handleDetailsExport(Map<String, Object> map, HttpServletResponse response);
}
