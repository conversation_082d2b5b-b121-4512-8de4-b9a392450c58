package cn.com.xmmn.businesses.dt.api;

import lombok.Data;

import java.io.Serializable;

/**
 * 接收客户申报详情
 */
@Data
public class ReceiveMailDeclarationDT implements Serializable {

    /**
     * 申报英文名称
     */
    private String descriptionEn;

    /**
     * 申报中文名称
     *
     */
    private String descriptionCn;


    /**
     * 申报数量
     */
    private Integer quantity;

    /**
     * 申报重量
     */
    private String unitWeight;

    /**
     * 申报价值
     */
    private String unitValue;

    /**
     * 申报海关编码
     */
    private String hsCode;

    /**
     * 申报税号
     */
    private String taxCode;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 规格
     */
    private String specifications;

    /**
     * 原产国
     */
    private String originCountryCode;
}
