package cn.com.xmmn.businesses.service;

import cn.com.xmmn.businesses.domain.OrdersImportDO;

import java.util.List;
import java.util.Map;

/**
 * 进口订单表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-30 12:26:11
 */
public interface OrdersImportService {
	
	OrdersImportDO get(Integer id);
	
	List<OrdersImportDO> list(Map<String, Object> map);

	Map<String, Object> countList(Map<String, Object> map);

	int count(Map<String, Object> map);
	
	int save(OrdersImportDO ordersImport);
	
	int update(OrdersImportDO ordersImport);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);

	List<Map<String,Object>> countShipway(Map<String,Object> map);

	List<Map<String,Object>> huanbiShipway(Map<String,Object> map);

	List<Map<String,Object>> oneShipway(Map<String,Object> map);

	List<Map<String,Object>> countCustomer(Map<String,Object> map);

	List<Map<String,Object>> huanbiCustomer(Map<String,Object> map);

	List<Map<String,Object>> oneCustomer(Map<String,Object> map);



}
