package cn.com.xmmn.businesses.task;

import cn.com.xmmn.businesses.service.InterfaceDeliveryService;
import cn.com.xmmn.businesses.service.InterfaceImportService;
import cn.com.xmmn.common.utils.GetTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @author: zxh
 * @since: 2023-01-29 14:42
 * @description:
 */
@Component
@Slf4j
public class InterfaceImportJob  implements Job {
    @Autowired
    InterfaceImportService interfaceImportService;

    /**
     * 单线程收发
     */
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.info("警告！执行线程！！");
    }

    @Scheduled(cron = "0 30 1 * * ?")//每天1:30
    private void getOrderTasks() {
        log.info("======进口系统订单获取接口执行开始时间" + new Date());
        //TODO:接口调用
        interfaceImportService.getOrderImport(GetTimeUtils.getYesterdayStartTime(),GetTimeUtils.getYesterdayEndTime());
        log.info("======进口系统订单获取接口结束时间" + new Date());
    }
}
