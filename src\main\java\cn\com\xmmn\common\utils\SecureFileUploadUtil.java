package cn.com.xmmn.common.utils;

import org.springframework.web.multipart.MultipartFile;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 安全的文件上传工具类
 * 集中处理文件上传的安全逻辑，防止路径遍历攻击
 */
public class SecureFileUploadUtil {

    // 默认允许的文件类型
    private static final Set<String> DEFAULT_ALLOWED_EXTENSIONS = new HashSet<>(
            Arrays.asList("jpg", "jpeg", "png", "gif", "pdf", "doc", "docx", "xls", "xlsx", "txt")
    );

    // 默认最大文件大小（10MB）
    private static final long DEFAULT_MAX_SIZE = 10 * 1024 * 1024;

    /**
     * 安全地上传文件
     *
     * @param file 上传的文件
     * @param uploadDir 上传目录
     * @return 上传后的文件名（相对路径）
     * @throws SecurityException 安全检查失败时抛出
     * @throws IOException IO异常
     * @throws Exception 其他异常
     */
    public static String uploadFileSafely(MultipartFile file, String uploadDir) throws SecurityException, IOException, Exception {
        return uploadFileSafely(file, uploadDir, DEFAULT_ALLOWED_EXTENSIONS, DEFAULT_MAX_SIZE);
    }

    /**
     * 安全地上传文件（带自定义参数）
     *
     * @param file 上传的文件
     * @param uploadDir 上传目录
     * @param allowedExtensions 允许的文件扩展名
     * @param maxSize 最大文件大小
     * @return 上传后的文件名（相对路径）
     * @throws SecurityException 安全检查失败时抛出
     * @throws IOException IO异常
     * @throws Exception 其他异常
     */
    public static String uploadFileSafely(MultipartFile file, String uploadDir, 
                                         Set<String> allowedExtensions, long maxSize) 
                                         throws SecurityException, IOException, Exception {
        // 文件为空检查
        if (file == null || file.isEmpty()) {
            throw new SecurityException("上传文件不能为空");
        }
        
        // 文件大小检查
        if (file.getSize() > maxSize) {
            throw new SecurityException("上传文件大小不能超过" + (maxSize / 1024 / 1024) + "MB");
        }
        
        String originalFilename = file.getOriginalFilename();
        
        // 文件名为空检查
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            throw new SecurityException("文件名不能为空");
        }
        
        // 防止路径遍历攻击 - 只获取文件名部分，忽略任何路径信息
        String safeFilename = new File(originalFilename).getName();
        
        // 文件类型安全检查
        if (!isAllowedFileType(safeFilename, allowedExtensions)) {
            throw new SecurityException("不支持的文件类型");
        }
        
        // 使用UUID重命名文件，确保文件名安全
        String fileName = FileUtil.renameToUUID(safeFilename);
        
        // 验证最终文件路径的安全性
        String absolutePath = uploadDir + fileName;
        
        // 规范化路径并检查是否在允许的目录内
        File targetFile = new File(absolutePath).getCanonicalFile();
        File uploadDirectory = new File(uploadDir).getCanonicalFile();
        
        // 检查目标文件是否在上传目录内
        if (!targetFile.getPath().startsWith(uploadDirectory.getPath())) {
            throw new SecurityException("检测到路径遍历攻击尝试");
        }
        
        // 确保上传目录存在
        if (!uploadDirectory.exists() && !uploadDirectory.mkdirs()) {
            throw new IOException("无法创建上传目录");
        }
        
        // 保存文件到服务器
        FileUtil.uploadFile(file.getBytes(), uploadDir, fileName);
        
        return fileName;
    }
    
    /**
     * 检查文件类型是否允许上传
     * 
     * @param fileName 文件名
     * @param allowedExtensions 允许的扩展名集合
     * @return 是否允许
     */
    private static boolean isAllowedFileType(String fileName, Set<String> allowedExtensions) {
        // 获取文件扩展名
        int lastDotIndex = fileName.lastIndexOf(".");
        if (lastDotIndex < 0) {
            return false; // 没有扩展名的文件不允许
        }
        
        String extension = fileName.substring(lastDotIndex + 1).toLowerCase();
        return allowedExtensions.contains(extension);
    }
} 