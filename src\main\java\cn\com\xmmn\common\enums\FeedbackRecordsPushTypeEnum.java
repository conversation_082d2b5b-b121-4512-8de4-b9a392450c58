package cn.com.xmmn.common.enums;



/**
 *
 * 信息反馈推送类型
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024年10月9日10:04:19
 */

public enum FeedbackRecordsPushTypeEnum {

    RECEIC(1,"接收"),
    DEPARTURE(2,"启运"),
    DELIVERY(3,"运抵"),
    POST(4,"交邮"),;

    private Integer value;
    private String name;


    FeedbackRecordsPushTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    // 根据枚举值返回相应的枚举实例
    public static FeedbackRecordsPushTypeEnum fromValue(Integer value) {
        for (FeedbackRecordsPushTypeEnum type : FeedbackRecordsPushTypeEnum.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的枚举值: " + value);  // 若传入的值无对应枚举，抛出异常
    }
}
