package cn.com.xmmn.businesses.dao;

import cn.com.xmmn.businesses.domain.BatchDO;

import java.util.List;
import java.util.Map;

import cn.com.xmmn.businesses.domain.CommercialDO;
import cn.com.xmmn.businesses.domain.NewDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 导入批次表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-06 13:07:47
 */
@Mapper
public interface BatchDao {

	//BatchDO get(Integer id);
	
	List<BatchDO> list(Map<String,Object> map);
	
	int count(Map<String,Object> map);
	
	int save(BatchDO batch);
	
	int update(BatchDO batch);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);
	int exportCommercial(CommercialDO batch);

	int exportNew(NewDO batch);
//调用t_commercial_export_manual表
    List detailsCommercial(CommercialDO batch);
//调用t_commercial_export_new表
	List newCommercial(NewDO batch);


//	<!--查询商业导入每月件数报表-->
	List<Map<String,Object>> commercialList(Map<String,Object> map);
}
