package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.XgImportMailCargoDao;
import cn.com.xmmn.businesses.domain.XgImportMailCargoDO;
import cn.com.xmmn.businesses.service.XgImportMailCargoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class XgImportMailCargoServiceImpl extends ServiceImpl<XgImportMailCargoDao, XgImportMailCargoDO> implements XgImportMailCargoService {


    @Override
    public void save(List<XgImportMailCargoDO> xgImportMailCargoList) {
        saveBatch(xgImportMailCargoList);
    }
}
