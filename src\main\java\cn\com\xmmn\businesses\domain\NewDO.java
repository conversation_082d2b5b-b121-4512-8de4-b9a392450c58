package cn.com.xmmn.businesses.domain;
/*
* 商业出口新一代
* */
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
@Data
public class NewDO implements Serializable {
    private static final long serialVersionUID = 1L;
    //批次id
    private Integer id;
    //乐观锁;数据版本号
    private Integer revision;
    //创建人id
    private Integer createUserId;
    //创建人
    private String createUserName;
    //创建部门id
    private Integer createDeptId;
    //创建部门
    private String createDeptName;
    //创建时间
    private Date createTime;
    //修改人id
    private Integer updateUserId;
    //修改人
    private String updataUserName;
    //修改部门id
    private Integer updataDeptId;
    //修改部门
    private String updataDeptName;
    //修改时间
    private Date updateTime;

    private String mainNo;

    private String sub;

    private String type;
    //收寄时间
    private Date postingDatetime;
    //邮件号
    private String itemNo;
    //寄达国代码
    private  String dest;
    //寄达国名称
    private String destinationName;

    //重量
    private double weight;

    private String walkInPickup;
    private String arPod;
    private String kpAba;
    private String csgnNo;
    private String pp;
    private String  ps;
    private String mailSubClass;
    private BigDecimal postage;
    private BigDecimal sumInsured;
    private BigDecimal insurPremium;
    private BigDecimal deliveryPremium;
    private BigDecimal netAmount;
    private String deliveryType;
    private Date inputDatetime;
    private String acceptanceOffice;

    //批次id
    private Integer batchId;
}
