package cn.com.xmmn.businesses.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 承运商邮袋接收明细列表
 * <AUTHOR>
 * @Date 2024/6/4 16:47
 */
@Data
@HeadRowHeight(value = 30) //头部行高
@ContentRowHeight(value = 25) //内容行高
@ColumnWidth(value = 20) //列宽
@HeadFontStyle(fontName = "宋体",fontHeightInPoints = 11)
public class OutMailDetailListVo implements Serializable {


    @ExcelProperty(value = "邮件号", index = 0)
    private String itemId;

    @ExcelProperty(value = "收寄省份", index = 1)
    private String cltProvinceName;


    @ExcelProperty(value = "收寄市", index = 2)
    private String cltCityName;

    @ExcelProperty(value = "出口互换局", index = 3)
    private String opOrgName;

    @ExcelProperty(value = "到达出口互换局时间emb", index = 4,format = "yyyy-MM-dd HH:mm:ss")
    private Date opTime;

    @ExcelProperty(value = "出口邮件直封封发emc(最新)", index = 5,format = "yyyy-MM-dd HH:mm:ss")
    private Date oeBillStartTime;

    @ExcelProperty(value = "交换站", index = 6)
    private String orgName;

    @ExcelProperty(value = "交换站解车时间(最新)", index = 7,format = "yyyy-MM-dd HH:mm:ss")
    private Date unloadOpTime;

    @ExcelProperty(value = "邮袋条码", index = 8)
    private String oeBagBarcode;


    @ExcelProperty(value = "计划航班", index = 9)
    private String flightNumber;


    @ExcelProperty(value = "实际航班", index = 10)
    private String newFlightNo;

    @ExcelProperty(value = "寄达国家/地区", index = 11)
    private String receiverCountryName;

    @ExcelProperty(value = "寄达互换局代码", index = 12)
    private String oeDest;


    @ExcelProperty(value = "寄达互换局名称", index = 13)
    private String oeDestName;

    @ExcelProperty(value = "交航时间", index = 14,format = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

    @ExcelProperty(value = "封发-交航（天）", index = 15)
    private String fenFaJiaoHang;


}
