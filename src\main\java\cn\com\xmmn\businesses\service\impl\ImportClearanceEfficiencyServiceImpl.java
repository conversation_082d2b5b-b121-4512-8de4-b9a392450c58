package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.ImportClearanceEfficiencyDao;
import cn.com.xmmn.businesses.domain.ImportClearanceEfficiencyDO;
import cn.com.xmmn.businesses.domain.ImportClearanceEfficiencyProportionDO;
import cn.com.xmmn.businesses.service.ImportClearanceEfficiencyService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

@Service
public class ImportClearanceEfficiencyServiceImpl implements ImportClearanceEfficiencyService {

    @Autowired
    private ImportClearanceEfficiencyDao importClearanceEfficiencyDao;

    @Override
    public List<ImportClearanceEfficiencyDO> list(Map<String, Object> map) {
        QueryWrapper queryWrapper = getQueryWrapper(map);


        return importClearanceEfficiencyDao.selectList(queryWrapper);
    }


    @Override
    public Page<ImportClearanceEfficiencyDO> pageImport(Map<String, Object> map) {
        // 默认分页参数
        final int defaultLimit = 10;
        final int defaultPage = 1;

        // 获取分页参数
        int limit = map.getOrDefault("limit", defaultLimit) instanceof Number ? ((Number) map.get("limit")).intValue() : defaultLimit;
        int page = map.getOrDefault("page", defaultPage) instanceof Number ? ((Number) map.get("page")).intValue() : defaultPage;

        // 检查分页参数的合理性
        if (limit <= 0) {
            limit = defaultLimit;
        }
        if (page <= 0) {
            page = defaultPage;
        }

        QueryWrapper queryWrapper = getQueryWrapper(map);

        Page page1=new Page(page,limit);
        page1.addOrder(OrderItem.asc("id"));

        Page<ImportClearanceEfficiencyDO> pageImport = importClearanceEfficiencyDao.selectPage(page1, queryWrapper);

        return pageImport;
    }

    @Override
    public int count(Map<String, Object> map) {
        QueryWrapper queryWrapper = getQueryWrapper(map);
        return importClearanceEfficiencyDao.selectCount(queryWrapper).intValue();
    }

    @Override
    public ImportClearanceEfficiencyProportionDO getProportion(Map<String, Object> map) {
        Object dateStart = map.get("dateStart");
        Object dateEnd = map.get("dateEnd");
        if (!ObjectUtils.isEmpty(dateStart) && !ObjectUtils.isEmpty(dateEnd)) {
             //todo先过滤时间
            String startDate = dateStart + " 00:00:00";
            String endDate = dateEnd + " 23:59:59";
            map.put("startDate", startDate);
            map.put("endDate", endDate);
        }

        return importClearanceEfficiencyDao.getProportion(map);
    }

    /**
     * 分页信息的QueryWrapper
     *
     * @param map 入参
     * @return QueryWrapper
     **/
    private QueryWrapper getQueryWrapper(Map<String, Object> map) {
        QueryWrapper<ImportClearanceEfficiencyDO> queryWrapper = new QueryWrapper<ImportClearanceEfficiencyDO>();
        // 分组条件，item_id 的条件
        queryWrapper.and(wrapper -> wrapper
                .likeRight("item_id", "EX").likeLeft("item_id", "HK")
                .or()
                .likeRight("item_id", "CD").likeLeft("item_id", "HK")
        );

        //香港形成总包时间开始和结束
        Object dateStart = map.get("dateStart");
        Object dateEnd = map.get("dateEnd");
        if (!ObjectUtils.isEmpty(dateStart) && !ObjectUtils.isEmpty(dateEnd)) {
            String startDate = dateStart + " 00:00:00";
            String endDate = dateEnd + " 23:59:59";
            queryWrapper.between("contract_time", startDate, endDate);
        }
        return queryWrapper;
    }
}
