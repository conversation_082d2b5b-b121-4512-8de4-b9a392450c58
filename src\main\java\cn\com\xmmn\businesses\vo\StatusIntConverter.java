package cn.com.xmmn.businesses.vo;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import io.swagger.models.auth.In;
import lombok.Data;

/**
 *   导出INT状态转化
 */
@Data
public class StatusIntConverter  implements Converter<Integer> {


    @Override
    public Class supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Integer convertToJavaData(CellData cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        // 获取Excel单元格的内容并转换为 Integer 类型
        String cellValue = cellData.getStringValue();
        if ("是".equals(cellValue)) {
            return 1; // 如果Excel中的内容是"是"，转换为 1
        } else if ("否".equals(cellValue)) {
            return 0; // 如果Excel中的内容是"否"，转换为 0
        }
        throw new IllegalArgumentException("Unexpected value: " + cellValue); // 处理意外的值
    }

    @Override
    public CellData convertToExcelData(Integer value, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (value == null) {
            return new CellData(""); // 如果值为空，返回空字符串
        } else if (value == 1) {
            return new CellData("是"); // 如果Java中的值是 1，转换为 Excel 中的"是"
        } else if (value == 0) {
            return new CellData("否"); // 如果Java中的值是 0，转换为 Excel 中的"否"
        }
        throw new IllegalArgumentException("Unexpected value: " + value); // 处理意外的值
    }
}
