package cn.com.xmmn.businesses.domain;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 进口邮件表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-24 15:06:38
 */
@TableName(value = "t_import_mail")
@HeadRowHeight(value = 30) //头部行高
@ContentRowHeight(value = 25) //内容行高
@ColumnWidth(value = 20) //列宽
@HeadFontStyle(fontName = "宋体",fontHeightInPoints = 11)
public class ImportClearanceEfficiencyDO implements Serializable {
    private static final long serialVersionUID = 1L;


    //ID;批次号
    @ExcelIgnore
    private Integer id;
    //邮件号码
    @ExcelProperty("邮件号码")
    private String itemId;

    //申报企业
    @ExcelIgnore
    private String declareComp;

    //收寄日期
    @ExcelIgnore
    private Date opTime;

    //申报类别
    @ExcelIgnore
    private String custDeclareClassCode;

    //发运海关时间
    @ExcelIgnore
    private Date cusTime;

    //原寄局
    @ExcelIgnore
    private String originOrgName;

    //寄达局
    @ExcelIgnore
    private String destOrgName;

    //清关口岸
    @ExcelIgnore
    private String custCode;

    //寄件人
    @ExcelIgnore
    private String senderName;

    //寄件人电话
    @ExcelIgnore
    private String senderPhone;

    //寄件人地址
    @ExcelIgnore
    private String senderAddr;

    //收件人
    @ExcelIgnore
    private String recName;

    //收件人电话
    @ExcelIgnore
    private String recipientAddress;

    //收件人地址
    @ExcelIgnore
    private String recAddress;

    //重量
    @ExcelIgnore
    private BigDecimal totalWeight;

    //邮费
    @ExcelIgnore
    private BigDecimal postage;

    //内件总价
    @ExcelIgnore
    private BigDecimal declareTotalValue;

    //税费
    @ExcelIgnore
    private BigDecimal taxTotalValue;

    //备注
    @ExcelIgnore
    private String remarkTxt;

    //回执时间
    @ExcelIgnore
    private Date replyTime;

    //回执说明
    @ExcelIgnore
    private String replyNotes;

    //回执状态
    @ExcelIgnore
    private String replyState;

    //业务时间
    @ExcelIgnore
    private Date pt;

    //形成总包时间
    @ExcelProperty("形成总包时间")
    private Date contractTime;

    //放行时间
    @ExcelProperty("放行时间")
    private Date releaseTime;

    //征税时间
    @ExcelProperty("征税时间")
    private Date taxTime;

    //查验时间
    @ExcelProperty("查验时间")
    private Date checkTime;

    //补充申报时间
    @ExcelProperty("补充申报时间")
    private Date declarationTime;

    //货物报关时间
    @ExcelProperty("货物报关时间")
    private Date customsTime;

    //人工审单时间
    @ExcelProperty("人工审单时间")
    private Date reviewTime;

    //乐观锁;数据版本号
    @ExcelIgnore
    private Integer revision;

    //创建人id
    @ExcelIgnore
    private Integer createUserId;

    //创建部门id
    @ExcelIgnore
    private Integer createDeptId;

    //创建人
    @ExcelIgnore
    private String createUserName;

    //创建部门
    @ExcelIgnore
    private String createDeptName;

    //创建时间
    @ExcelIgnore
    private Date createTime;

    //修改人id
    @ExcelIgnore
    private Integer updateUserId;

    //修改人
    @ExcelIgnore
    private String updateUserName;

    //修改部门id
    @ExcelIgnore
    private Integer updateDeptId;

    //修改部门
    @ExcelIgnore
    private String updateDeptName;

    //修改时间
    @ExcelIgnore
    private Date updateTime;

    /**
     * 设置：ID;批次号
     */
    public void setId(Integer id) {
        this.id = id;
    }
    /**
     * 获取：ID;批次号
     */
    public Integer getId() {
        return id;
    }
    /**
     * 设置：邮件号码
     */
    public void setItemId(String itemId) {
        this.itemId = itemId;
    }
    /**
     * 获取：邮件号码
     */
    public String getItemId() {
        return itemId;
    }
    /**
     * 设置：申报企业
     */
    public void setDeclareComp(String declareComp) {
        this.declareComp = declareComp;
    }
    /**
     * 获取：申报企业
     */
    public String getDeclareComp() {
        return declareComp;
    }
    /**
     * 设置：收寄日期
     */
    public void setOpTime(Date opTime) {
        this.opTime = opTime;
    }
    /**
     * 获取：收寄日期
     */
    public Date getOpTime() {
        return opTime;
    }
    /**
     * 设置：申报类别
     */
    public void setCustDeclareClassCode(String custDeclareClassCode) {
        this.custDeclareClassCode = custDeclareClassCode;
    }
    /**
     * 获取：申报类别
     */
    public String getCustDeclareClassCode() {
        return custDeclareClassCode;
    }
    /**
     * 设置：发运海关时间
     */
    public void setCusTime(Date cusTime) {
        this.cusTime = cusTime;
    }
    /**
     * 获取：发运海关时间
     */
    public Date getCusTime() {
        return cusTime;
    }
    /**
     * 设置：原寄局
     */
    public void setOriginOrgName(String originOrgName) {
        this.originOrgName = originOrgName;
    }
    /**
     * 获取：原寄局
     */
    public String getOriginOrgName() {
        return originOrgName;
    }
    /**
     * 设置：寄达局
     */
    public void setDestOrgName(String destOrgName) {
        this.destOrgName = destOrgName;
    }
    /**
     * 获取：寄达局
     */
    public String getDestOrgName() {
        return destOrgName;
    }
    /**
     * 设置：清关口岸
     */
    public void setCustCode(String custCode) {
        this.custCode = custCode;
    }
    /**
     * 获取：清关口岸
     */
    public String getCustCode() {
        return custCode;
    }
    /**
     * 设置：寄件人
     */
    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }
    /**
     * 获取：寄件人
     */
    public String getSenderName() {
        return senderName;
    }
    /**
     * 设置：寄件人电话
     */
    public void setSenderPhone(String senderPhone) {
        this.senderPhone = senderPhone;
    }
    /**
     * 获取：寄件人电话
     */
    public String getSenderPhone() {
        return senderPhone;
    }
    /**
     * 设置：寄件人地址
     */
    public void setSenderAddr(String senderAddr) {
        this.senderAddr = senderAddr;
    }
    /**
     * 获取：寄件人地址
     */
    public String getSenderAddr() {
        return senderAddr;
    }
    /**
     * 设置：收件人
     */
    public void setRecName(String recName) {
        this.recName = recName;
    }
    /**
     * 获取：收件人
     */
    public String getRecName() {
        return recName;
    }
    /**
     * 设置：收件人电话
     */
    public void setRecipientAddress(String recipientAddress) {
        this.recipientAddress = recipientAddress;
    }
    /**
     * 获取：收件人电话
     */
    public String getRecipientAddress() {
        return recipientAddress;
    }
    /**
     * 设置：收件人地址
     */
    public void setRecAddress(String recAddress) {
        this.recAddress = recAddress;
    }
    /**
     * 获取：收件人地址
     */
    public String getRecAddress() {
        return recAddress;
    }
    /**
     * 设置：重量
     */
    public void setTotalWeight(BigDecimal totalWeight) {
        this.totalWeight = totalWeight;
    }
    /**
     * 获取：重量
     */
    public BigDecimal getTotalWeight() {
        return totalWeight;
    }
    /**
     * 设置：邮费
     */
    public void setPostage(BigDecimal postage) {
        this.postage = postage;
    }
    /**
     * 获取：邮费
     */
    public BigDecimal getPostage() {
        return postage;
    }
    /**
     * 设置：内件总价
     */
    public void setDeclareTotalValue(BigDecimal declareTotalValue) {
        this.declareTotalValue = declareTotalValue;
    }
    /**
     * 获取：内件总价
     */
    public BigDecimal getDeclareTotalValue() {
        return declareTotalValue;
    }
    /**
     * 设置：税费
     */
    public void setTaxTotalValue(BigDecimal taxTotalValue) {
        this.taxTotalValue = taxTotalValue;
    }
    /**
     * 获取：税费
     */
    public BigDecimal getTaxTotalValue() {
        return taxTotalValue;
    }
    /**
     * 设置：备注
     */
    public void setRemarkTxt(String remarkTxt) {
        this.remarkTxt = remarkTxt;
    }
    /**
     * 获取：备注
     */
    public String getRemarkTxt() {
        return remarkTxt;
    }
    /**
     * 设置：回执时间
     */
    public void setReplyTime(Date replyTime) {
        this.replyTime = replyTime;
    }
    /**
     * 获取：回执时间
     */
    public Date getReplyTime() {
        return replyTime;
    }
    /**
     * 设置：回执说明
     */
    public void setReplyNotes(String replyNotes) {
        this.replyNotes = replyNotes;
    }
    /**
     * 获取：回执说明
     */
    public String getReplyNotes() {
        return replyNotes;
    }
    /**
     * 设置：回执状态
     */
    public void setReplyState(String replyState) {
        this.replyState = replyState;
    }
    /**
     * 获取：回执状态
     */
    public String getReplyState() {
        return replyState;
    }
    /**
     * 设置：业务时间
     */
    public void setPt(Date pt) {
        this.pt = pt;
    }
    /**
     * 获取：业务时间
     */
    public Date getPt() {
        return pt;
    }
    /**
     * 设置：形成总包时间
     */
    public void setContractTime(Date contractTime) {
        this.contractTime = contractTime;
    }
    /**
     * 获取：形成总包时间
     */
    public Date getContractTime() {
        return contractTime;
    }
    /**
     * 设置：放行时间
     */
    public void setReleaseTime(Date releaseTime) {
        this.releaseTime = releaseTime;
    }
    /**
     * 获取：放行时间
     */
    public Date getReleaseTime() {
        return releaseTime;
    }
    /**
     * 设置：征税时间
     */
    public void setTaxTime(Date taxTime) {
        this.taxTime = taxTime;
    }
    /**
     * 获取：征税时间
     */
    public Date getTaxTime() {
        return taxTime;
    }
    /**
     * 设置：查验时间
     */
    public void setCheckTime(Date checkTime) {
        this.checkTime = checkTime;
    }
    /**
     * 获取：查验时间
     */
    public Date getCheckTime() {
        return checkTime;
    }
    /**
     * 设置：补充申报时间
     */
    public void setDeclarationTime(Date declarationTime) {
        this.declarationTime = declarationTime;
    }
    /**
     * 获取：补充申报时间
     */
    public Date getDeclarationTime() {
        return declarationTime;
    }
    /**
     * 设置：货物报关时间
     */
    public void setCustomsTime(Date customsTime) {
        this.customsTime = customsTime;
    }
    /**
     * 获取：货物报关时间
     */
    public Date getCustomsTime() {
        return customsTime;
    }
    /**
     * 设置：人工审单时间
     */
    public void setReviewTime(Date reviewTime) {
        this.reviewTime = reviewTime;
    }
    /**
     * 获取：人工审单时间
     */
    public Date getReviewTime() {
        return reviewTime;
    }
    /**
     * 设置：乐观锁;数据版本号
     */
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
    /**
     * 获取：乐观锁;数据版本号
     */
    public Integer getRevision() {
        return revision;
    }
    /**
     * 设置：创建人id
     */
    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }
    /**
     * 获取：创建人id
     */
    public Integer getCreateUserId() {
        return createUserId;
    }
    /**
     * 设置：创建部门id
     */
    public void setCreateDeptId(Integer createDeptId) {
        this.createDeptId = createDeptId;
    }
    /**
     * 获取：创建部门id
     */
    public Integer getCreateDeptId() {
        return createDeptId;
    }
    /**
     * 设置：创建人
     */
    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }
    /**
     * 获取：创建人
     */
    public String getCreateUserName() {
        return createUserName;
    }
    /**
     * 设置：创建部门
     */
    public void setCreateDeptName(String createDeptName) {
        this.createDeptName = createDeptName;
    }
    /**
     * 获取：创建部门
     */
    public String getCreateDeptName() {
        return createDeptName;
    }
    /**
     * 设置：创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    /**
     * 获取：创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }
    /**
     * 设置：修改人id
     */
    public void setUpdateUserId(Integer updateUserId) {
        this.updateUserId = updateUserId;
    }
    /**
     * 获取：修改人id
     */
    public Integer getUpdateUserId() {
        return updateUserId;
    }
    /**
     * 设置：修改人
     */
    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }
    /**
     * 获取：修改人
     */
    public String getUpdateUserName() {
        return updateUserName;
    }
    /**
     * 设置：修改部门id
     */
    public void setUpdateDeptId(Integer updateDeptId) {
        this.updateDeptId = updateDeptId;
    }
    /**
     * 获取：修改部门id
     */
    public Integer getUpdateDeptId() {
        return updateDeptId;
    }
    /**
     * 设置：修改部门
     */
    public void setUpdateDeptName(String updateDeptName) {
        this.updateDeptName = updateDeptName;
    }
    /**
     * 获取：修改部门
     */
    public String getUpdateDeptName() {
        return updateDeptName;
    }
    /**
     * 设置：修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    /**
     * 获取：修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }
}
