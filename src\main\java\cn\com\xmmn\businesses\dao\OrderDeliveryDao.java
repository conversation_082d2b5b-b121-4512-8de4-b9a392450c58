package cn.com.xmmn.businesses.dao;

import cn.com.xmmn.businesses.domain.OrderDeliveryDO;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

/**
 * 投递订单表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-30 20:15:07
 */
@Mapper
public interface OrderDeliveryDao {

	OrderDeliveryDO get(Integer id);
	
	List<OrderDeliveryDO> list(Map<String,Object> map);

	Map<String, Object> countList(Map<String, Object> map);
	
	int count(Map<String,Object> map);
	
	int save(OrderDeliveryDO orderDelivery);
	
	int update(OrderDeliveryDO orderDelivery);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);

	//查询hongkong
//	List<String> hongkongList(Map<String,Object> map);
	List<Map<String,Object>> hongkongList(Map<String,Object> map);

	//查询环比升降
	List<Map<String,Object>> huanbiList(Map<String,Object> map);

	//查询妥投率-饼图
	List<Map<String,Object>> tuotou(Map<String,Object> map);

	//查询妥投率-折线图
	List<Map<String,Object>> tuotoulv(Map<String,Object> map);

	//查询某个日期是第几周
	int week(String dateStr);
}
