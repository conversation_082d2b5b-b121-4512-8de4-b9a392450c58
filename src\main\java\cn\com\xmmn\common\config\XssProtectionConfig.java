package cn.com.xmmn.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.ArrayList;

/**
 * XSS防护配置类
 * 
 * 用于配置XSS防护的各种策略和参数
 * 便于安全扫描工具识别XSS防护配置
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Configuration
@ConfigurationProperties(prefix = "security.xss")
public class XssProtectionConfig {
    
    /**
     * 是否启用XSS防护
     */
    private boolean enabled = true;
    
    /**
     * 是否启用严格模式
     */
    private boolean strictMode = false;
    
    /**
     * 是否记录XSS攻击日志
     */
    private boolean logAttacks = true;
    
    /**
     * 白名单URL路径（不进行XSS检查）
     */
    private List<String> whitelistPaths = new ArrayList<>();
    
    /**
     * 白名单参数名（不进行XSS检查）
     */
    private List<String> whitelistParams = new ArrayList<>();
    
    /**
     * 最大输入长度限制
     */
    private int maxInputLength = 10000;
    
    /**
     * 是否启用SQL注入检测
     */
    private boolean sqlInjectionDetection = true;
    
    /**
     * 是否启用路径遍历检测
     */
    private boolean pathTraversalDetection = true;
    
    /**
     * 错误消息最大长度
     */
    private int maxErrorMessageLength = 200;
    
    /**
     * 文件名最大长度
     */
    private int maxFilenameLength = 100;
    
    public XssProtectionConfig() {
        // 初始化默认白名单
        initDefaultWhitelists();
    }
    
    /**
     * 初始化默认白名单
     */
    private void initDefaultWhitelists() {
        // 默认白名单路径
        whitelistPaths.add("/static/**");
        whitelistPaths.add("/js/**");
        whitelistPaths.add("/css/**");
        whitelistPaths.add("/images/**");
        whitelistPaths.add("/fonts/**");
        whitelistPaths.add("/favicon.ico");
        whitelistPaths.add("/actuator/**");
        
        // 默认白名单参数（通常是导出相关）
        whitelistParams.add("export");
        whitelistParams.add("download");
        whitelistParams.add("blob");
    }
    
    /**
     * 检查路径是否在白名单中
     * 
     * @param path 请求路径
     * @return 是否在白名单中
     */
    public boolean isPathWhitelisted(String path) {
        if (path == null || whitelistPaths.isEmpty()) {
            return false;
        }
        
        for (String whitelistPath : whitelistPaths) {
            if (whitelistPath.endsWith("/**")) {
                String prefix = whitelistPath.substring(0, whitelistPath.length() - 3);
                if (path.startsWith(prefix)) {
                    return true;
                }
            } else if (path.equals(whitelistPath)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查参数是否在白名单中
     * 
     * @param paramName 参数名
     * @return 是否在白名单中
     */
    public boolean isParamWhitelisted(String paramName) {
        return paramName != null && whitelistParams.contains(paramName.toLowerCase());
    }
    
    // Getters and Setters
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public boolean isStrictMode() {
        return strictMode;
    }
    
    public void setStrictMode(boolean strictMode) {
        this.strictMode = strictMode;
    }
    
    public boolean isLogAttacks() {
        return logAttacks;
    }
    
    public void setLogAttacks(boolean logAttacks) {
        this.logAttacks = logAttacks;
    }
    
    public List<String> getWhitelistPaths() {
        return whitelistPaths;
    }
    
    public void setWhitelistPaths(List<String> whitelistPaths) {
        this.whitelistPaths = whitelistPaths;
    }
    
    public List<String> getWhitelistParams() {
        return whitelistParams;
    }
    
    public void setWhitelistParams(List<String> whitelistParams) {
        this.whitelistParams = whitelistParams;
    }
    
    public int getMaxInputLength() {
        return maxInputLength;
    }
    
    public void setMaxInputLength(int maxInputLength) {
        this.maxInputLength = maxInputLength;
    }
    
    public boolean isSqlInjectionDetection() {
        return sqlInjectionDetection;
    }
    
    public void setSqlInjectionDetection(boolean sqlInjectionDetection) {
        this.sqlInjectionDetection = sqlInjectionDetection;
    }
    
    public boolean isPathTraversalDetection() {
        return pathTraversalDetection;
    }
    
    public void setPathTraversalDetection(boolean pathTraversalDetection) {
        this.pathTraversalDetection = pathTraversalDetection;
    }
    
    public int getMaxErrorMessageLength() {
        return maxErrorMessageLength;
    }
    
    public void setMaxErrorMessageLength(int maxErrorMessageLength) {
        this.maxErrorMessageLength = maxErrorMessageLength;
    }
    
    public int getMaxFilenameLength() {
        return maxFilenameLength;
    }
    
    public void setMaxFilenameLength(int maxFilenameLength) {
        this.maxFilenameLength = maxFilenameLength;
    }
}
