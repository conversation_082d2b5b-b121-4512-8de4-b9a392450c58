<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.xmmn.businesses.dao.CustomerOrderDeclareDao">

	<select id="get" resultType="cn.com.xmmn.businesses.domain.CustomerOrderDeclareDO">
		select `id`,`customet_order_id`,`currency`,`customs_type`,`description_en`,`description_cn`,`quantity`,`unit_weight`,`unit_value`,`hs_code`,`tax_code`,`brand`,`specifications`,`origin_country_code` from t_customer_order_declare where id = #{value}
	</select>

	<select id="list" resultType="cn.com.xmmn.businesses.domain.CustomerOrderDeclareDO">
		select `id`,`customet_order_id`,`currency`,`customs_type`,`description_en`,`description_cn`,`quantity`,`unit_weight`,`unit_value`,`hs_code`,`tax_code`,`brand`,`specifications`,`origin_country_code` from t_customer_order_declare
        <where>  
		  		  <if test="id != null and id != ''"> and id = #{id} </if>
		  		  <if test="custometOrderId != null and custometOrderId != ''"> and customet_order_id = #{custometOrderId} </if>
		  		  <if test="currency != null and currency != ''"> and currency = #{currency} </if>
		  		  <if test="customsType != null and customsType != ''"> and customs_type = #{customsType} </if>
		  		  <if test="descriptionEn != null and descriptionEn != ''"> and description_en = #{descriptionEn} </if>
		  		  <if test="descriptionCn != null and descriptionCn != ''"> and description_cn = #{descriptionCn} </if>
		  		  <if test="quantity != null and quantity != ''"> and quantity = #{quantity} </if>
		  		  <if test="unitWeight != null and unitWeight != ''"> and unit_weight = #{unitWeight} </if>
		  		  <if test="unitValue != null and unitValue != ''"> and unit_value = #{unitValue} </if>
		  		  <if test="hsCode != null and hsCode != ''"> and hs_code = #{hsCode} </if>
		  		  <if test="taxCode != null and taxCode != ''"> and tax_code = #{taxCode} </if>
		  		  <if test="brand != null and brand != ''"> and brand = #{brand} </if>
		  		  <if test="specifications != null and specifications != ''"> and specifications = #{specifications} </if>
		  		  <if test="originCountryCode != null and originCountryCode != ''"> and origin_country_code = #{originCountryCode} </if>
		  		</where>
        <choose>
            <when test="sort != null and sort.trim() != ''">
                order by id desc
            </when>
			<otherwise>
                order by id desc
			</otherwise>
        </choose>
		<if test="offset != null and limit != null">
			limit #{offset}, #{limit}
		</if>
	</select>
	
 	<select id="count" resultType="int">
		select count(*) from t_customer_order_declare
		 <where>  
		  		  <if test="id != null and id != ''"> and id = #{id} </if>
		  		  <if test="custometOrderId != null and custometOrderId != ''"> and customet_order_id = #{custometOrderId} </if>
		  		  <if test="currency != null and currency != ''"> and currency = #{currency} </if>
		  		  <if test="customsType != null and customsType != ''"> and customs_type = #{customsType} </if>
		  		  <if test="descriptionEn != null and descriptionEn != ''"> and description_en = #{descriptionEn} </if>
		  		  <if test="descriptionCn != null and descriptionCn != ''"> and description_cn = #{descriptionCn} </if>
		  		  <if test="quantity != null and quantity != ''"> and quantity = #{quantity} </if>
		  		  <if test="unitWeight != null and unitWeight != ''"> and unit_weight = #{unitWeight} </if>
		  		  <if test="unitValue != null and unitValue != ''"> and unit_value = #{unitValue} </if>
		  		  <if test="hsCode != null and hsCode != ''"> and hs_code = #{hsCode} </if>
		  		  <if test="taxCode != null and taxCode != ''"> and tax_code = #{taxCode} </if>
		  		  <if test="brand != null and brand != ''"> and brand = #{brand} </if>
		  		  <if test="specifications != null and specifications != ''"> and specifications = #{specifications} </if>
		  		  <if test="originCountryCode != null and originCountryCode != ''"> and origin_country_code = #{originCountryCode} </if>
		  		</where>
	</select>
	 
	<insert id="save" parameterType="cn.com.xmmn.businesses.domain.CustomerOrderDeclareDO">
		insert into t_customer_order_declare
		(
			`id`, 
			`customet_order_id`, 
			`currency`, 
			`customs_type`, 
			`description_en`, 
			`description_cn`, 
			`quantity`, 
			`unit_weight`, 
			`unit_value`, 
			`hs_code`, 
			`tax_code`, 
			`brand`, 
			`specifications`, 
			`origin_country_code`
		)
		values
		(
			#{id}, 
			#{custometOrderId}, 
			#{currency}, 
			#{customsType}, 
			#{descriptionEn}, 
			#{descriptionCn}, 
			#{quantity}, 
			#{unitWeight}, 
			#{unitValue}, 
			#{hsCode}, 
			#{taxCode}, 
			#{brand}, 
			#{specifications}, 
			#{originCountryCode}
		)
	</insert>
	 
	<update id="update" parameterType="cn.com.xmmn.businesses.domain.CustomerOrderDeclareDO">
		update t_customer_order_declare 
		<set>
			<if test="custometOrderId != null">`customet_order_id` = #{custometOrderId}, </if>
			<if test="currency != null">`currency` = #{currency}, </if>
			<if test="customsType != null">`customs_type` = #{customsType}, </if>
			<if test="descriptionEn != null">`description_en` = #{descriptionEn}, </if>
			<if test="descriptionCn != null">`description_cn` = #{descriptionCn}, </if>
			<if test="quantity != null">`quantity` = #{quantity}, </if>
			<if test="unitWeight != null">`unit_weight` = #{unitWeight}, </if>
			<if test="unitValue != null">`unit_value` = #{unitValue}, </if>
			<if test="hsCode != null">`hs_code` = #{hsCode}, </if>
			<if test="taxCode != null">`tax_code` = #{taxCode}, </if>
			<if test="brand != null">`brand` = #{brand}, </if>
			<if test="specifications != null">`specifications` = #{specifications}, </if>
			<if test="originCountryCode != null">`origin_country_code` = #{originCountryCode}</if>
		</set>
		where id = #{id}
	</update>
	
	<delete id="remove">
		delete from t_customer_order_declare where id = #{value}
	</delete>
	
	<delete id="batchRemove">
		delete from t_customer_order_declare where id in 
		<foreach item="id" collection="array" open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>

</mapper>