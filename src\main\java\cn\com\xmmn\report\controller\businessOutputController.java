package cn.com.xmmn.report.controller;

import cn.com.xmmn.businesses.service.BatchService;
import cn.com.xmmn.businesses.service.OrdersTmsService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * TMS商业出口报表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-30 20:15:07
 */
@Slf4j
@Controller
@RequestMapping("/report/businessOutputReport")
public class businessOutputController {

    @Autowired
    private OrdersTmsService ordersTmsService;


    @GetMapping()
    @RequiresPermissions("report:businessOutputReport")
    String init(Model model) {
        //设置初始时间  初始时间为三个月前的第一天，比如今天3月22日，则开始时间设为2023-01，结束时间为2023-03
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DATE, 1);// 设为当前月的1号
        calendar.add(Calendar.MONTH, -2);// 0表示当前月，-2就是当前月-2
        Date d = calendar.getTime();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String startDate = sdf.format(d);
        String endDate = sdf.format(new Date());
        //柱形图时间
        model.addAttribute("startDate", startDate);
        model.addAttribute("endDate", endDate);
        return "report/tmsReport";
    }

    //    按口岸统计图
    @ResponseBody
    @PostMapping("/list")
    @RequiresPermissions("report:businessOutputReport")
    public Map<String, Object> portList(String startDate, String endDate) {
        Map<String, Object> data = new HashMap<String, Object>();

        /**********A1.柱形图数据 件数************/
        String endDateStr = endDate + "-32";//加30天
        List<String> title = getTitle(startDate, endDate);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("endDateStr", endDateStr);
        map.put("endDate", endDate);
        map.put("startDate", startDate);
        map.put("title", title);
        List<Map<String, Object>> totalCountList = ordersTmsService.totalCount(map);
        totalCountList.remove(0);//sql循环拼接union会多拼接一个

        List totalCount = new ArrayList();
        if(totalCountList.size()>0){
            for(int i = 0;i< totalCountList.size();i++){
                totalCount.add(totalCountList.get(i).get("num"));
            }
        }

        String totalCountresult = JSON.toJSONString(totalCountList).toString();
        log.info("返回结果：" + totalCountresult);
//        title.add(0, "product");//Echart显示表头的时候需要用到
//        log.info("title" + title.toString());
//        model.addAttribute("yuefen", title);
        data.put("totalCountresult", totalCount);

        /**********A2.折线图数据：收入************/
        List<Map<String, Object>> totalAmountList = ordersTmsService.totalAmount(map);
        totalAmountList.remove(0);//sql循环拼接union会多拼接一个

        List totalAmount = new ArrayList();
        if(totalAmountList.size()>0){
            for(int i = 0;i< totalAmountList.size();i++){
                totalAmount.add(totalAmountList.get(i).get("amount"));
            }
        }

        String totalAmountResult = JSON.toJSONString(totalAmountList).toString();
        log.info("返回结果：" + totalAmountResult);
        data.put("totalAmountResult", totalAmount);


        /**********A3柱形图右侧的环比数据************/
        List<String> huanbi = getHuanbiMonth(endDate);//若 end = '2022-11',则返回[2022-10, 2022-11],比较环比升降用的
        map.put("huanbi", huanbi);
        map.put("huanbiStart", huanbi.get(0));
        List<Map<String, Object>> huanbiList = ordersTmsService.huanbiList(map);
        data.put("huanbi", huanbiList);


        /**********A2.数据：重量************/
        List<Map<String, Object>> totalWeightList = ordersTmsService.totalWeight(map);
        totalWeightList.remove(0);//sql循环拼接union会多拼接一个

        List totalWeight = new ArrayList();
        if(totalWeightList.size()>0){
            for(int i = 0;i< totalWeightList.size();i++){
                totalWeight.add(totalWeightList.get(i).get("weight"));
            }
        }

        String totalWeightresult = JSON.toJSONString(totalWeightList).toString();
        log.info("返回结果：" + totalWeightresult);
        data.put("totalWeightresult", totalWeight);

        data.put("title", title);
        data.put("status", "success");
        return data;

    }


    //    商业出口按照客户、渠道，分组
    @ResponseBody
    @PostMapping("/percent")
    @RequiresPermissions("report:businessOutputReport")
    public Map<String, Object> percent(String endDate) {
        Map<String, Object> data = new HashMap<String, Object>();

        /**********A1.按客户分类饼图数据***********/
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("endDate", endDate);
        map.put("groupName", "customer_allname");//按客户统计
        List<Map<String, Object>> custList = ordersTmsService.percent(map);
        String custResult = JSON.toJSONString(custList).toString();
        log.info("按客户分类饼图数据：" + custResult);
        data.put("custResult", custResult);

        /**********A2.按渠道分类饼图数据************/
        map.put("groupName", "server_channel_cnname");//按渠道统计
        List<Map<String, Object>> channelList = ordersTmsService.percent(map);
        String channelResult = JSON.toJSONString(channelList).toString();
        log.info("按渠道分类饼图数据：" + channelResult);
        data.put("channelResult", channelResult);

        data.put("status", "success");
        return data;

    }


    //获取sql需要分组的参数
    public List<String> getTitle(String begin, String end) {
        //若 begin = '2022-11',end = '2023-03',则返回[2022-11, 2022-12, 2023-01, 2023-02, 2023-03]
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        List<String> list = new ArrayList<String>();
        /*list.add("product"); //Echart显示表头的时候需要用到*/
        Date d1;
        Date d2;
        try {
            d1 = new SimpleDateFormat("yyyy-MM").parse(begin);
            d2 = new SimpleDateFormat("yyy-MM").parse(end);//定义结束日期可以去当前月也可以手动写日期。
            Calendar dd = Calendar.getInstance();//定义日期实例
            dd.setTime(d1);//设置日期起始时间
            while (dd.getTime().before(d2)) {//判断是否到结束日期
                SimpleDateFormat sdf = new SimpleDateFormat("yyy-MM");
                String str = sdf.format(dd.getTime());
                dd.add(Calendar.MONTH, 1);//进行当前日期月份加1
                list.add(str);
            }
            list.add(end);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return list;

    }

    //使用Calendar的set和add方法，从下个月的第一天计算得到当前月的最后一天,并得到最后一天是28/29/30/31
    public String getLastDayOfMonth(String dateStr) {//参数2023-03，返回结果31

        String year = dateStr.substring(0,4);
        String month = dateStr.substring(5,7);
        Calendar cal = Calendar.getInstance();
        //年
        cal.set(Calendar.YEAR, Integer.parseInt(year));
        //月，因为Calendar里的月是从0开始，所以要-1
        cal.set(Calendar.MONTH, Integer.parseInt(month) - 1);
        //日，设为一号
        cal.set(Calendar.DATE, 1);
        //月份加一，得到下个月的一号
        cal.add(Calendar.MONTH,1);
        //下一个月减一为本月最后一天
        cal.add(Calendar.DATE, -1);
        String monthEnd = String.valueOf(cal.get(Calendar.DAY_OF_MONTH));//获得月末是几号
//        System.out.println(year+month+",获得本月月末:" + monthEnd);
        return monthEnd;
    }


    // 使用参数月份,得到上一个月及参数月，计算环比用
    public List<String> getHuanbiMonth(String end) {
        //若 end = '2022-11',则返回[2022-10, 2022-11]
        List<String> list = new ArrayList<String>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        try {
            date = sdf.parse(end + "-" + "01");
        } catch (ParseException e) {
            e.printStackTrace();
        }

        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MONTH, -1);
        String lastDate = "";
        if(c.get(Calendar.MONTH) <9) {//做这判断是因为，如果上一个月是10/11/12 则会变成2023-010/011/012
            lastDate = c.get(Calendar.YEAR) + "-0"
                    + (c.get(Calendar.MONTH) + 1);
        }else{
            lastDate = c.get(Calendar.YEAR) + "-"
                    + (c.get(Calendar.MONTH) + 1);
        }

        list.add(lastDate);
        list.add(end);
        return list;
    }
}
