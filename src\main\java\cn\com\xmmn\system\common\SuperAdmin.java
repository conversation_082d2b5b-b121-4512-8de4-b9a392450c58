package cn.com.xmmn.system.common;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @author: zxh
 * @since: 2022-10-24 20:36
 * @description:
 */
public final class SuperAdmin {
    private static final Long userId = 1L;
    private static final Long userRoleId = 1L;
    private static final List<String> privilegeMenu = new ArrayList<>(Arrays.asList("1","49","77","84","91","93","97"));

    public static Long getUserId() {
        return userId;
    }

    public static Long getUserRoleId() {
        return userRoleId;
    }

    public static List<String> getPrivilegeMenu() {
        return privilegeMenu;
    }
}
