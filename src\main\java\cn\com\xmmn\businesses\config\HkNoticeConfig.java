package cn.com.xmmn.businesses.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 香港邮政成交通知单配置类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-31
 */
@Data
@Component
@ConfigurationProperties(prefix = "hk-notice")
public class HkNoticeConfig {
    
    /**
     * 香港邮政API接口地址
     */
    private String apiUrl;
    
    /**
     * 加密密钥
     */
    private String secretKey;
    
    /**
     * 请求超时时间（毫秒）
     */
    private Integer timeout = 30000;
    
    /**
     * 是否启用SSL验证
     */
    private Boolean sslEnabled = true;
}
