package cn.com.xmmn.businesses.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 第三方接口配置类
 * 支持多个第三方系统的配置管理
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-31
 */
@Data
@Component
@ConfigurationProperties(prefix = "third-party")
public class ThirdPartyConfig {

    /**
     * 香港邮政系统配置
     */
    private HongKongPost hongKongPost = new HongKongPost();

    /**
     * 海关系统配置（示例）
     */
    private Customs customs = new Customs();

    /**
     * 银行支付系统配置（示例）
     */
    private BankPayment bankPayment = new BankPayment();

    @Data
    public static class HongKongPost {
        /**
         * API基础地址
         */
        private String baseUrl;

        /**
         * 密钥
         */
        private String secretKey;

        /**
         * 超时时间（毫秒）
         */
        private Integer timeout = 30000;

        /**
         * 是否启用SSL
         */
        private Boolean sslEnabled = true;

        /**
         * 成交运能通知单API路径
         */
        private String capacityNoticeApiPath = "/api/requestBill";
    }

    @Data
    public static class Customs {
        private String baseUrl;
        private String secretKey;
        private Integer timeout = 30000;
        private Boolean sslEnabled = true;
        // 可以添加海关系统特有的配置
    }

    @Data
    public static class BankPayment {
        private String baseUrl;
        private String secretKey;
        private Integer timeout = 30000;
        private Boolean sslEnabled = true;
        // 可以添加银行支付系统特有的配置
    }
}
