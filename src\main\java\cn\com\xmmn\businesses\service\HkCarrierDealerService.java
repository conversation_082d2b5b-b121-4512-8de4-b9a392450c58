package cn.com.xmmn.businesses.service;

import cn.com.xmmn.businesses.domain.HkCarrierDealerDO;

import java.util.List;
import java.util.Map;

/**
 * 香港邮政邮袋接收接口表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-25 09:53:59
 */
public interface HkCarrierDealerService {
	
	HkCarrierDealerDO get(Integer id);
	
	List<HkCarrierDealerDO> list(Map<String, Object> map);
	
	int count(Map<String, Object> map);
	
	int save(HkCarrierDealerDO hkCarrierDealer);
	
	int update(HkCarrierDealerDO hkCarrierDealer);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);

	/**
	 * 接收对外的加密邮袋信息
	 * @param encryptedData
	 */
	void receiveMailbag(String encryptedData);
}
