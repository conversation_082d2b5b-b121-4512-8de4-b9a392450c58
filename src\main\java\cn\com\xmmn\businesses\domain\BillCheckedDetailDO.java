package cn.com.xmmn.businesses.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 账单核对结果表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-08 21:58:47
 */
public class BillCheckedDetailDO implements Serializable {
	private static final long serialVersionUID = 1L;
	private List<CheckXydCarrierDO> rows;
	//ID
	private Integer id;
	//乐观锁;数据版本号
	private Integer revision;
	//创建人id
	private Integer createUserId;
	//创建人
	private String createUserName;
	//创建部门id
	private Integer createDeptId;
	//创建部门
	private String createDeptName;
	//创建时间
	private Date createTime;
	//修改人id
	private Integer updateUserId;
	//修改人
	private String updateUserName;
	//修改部门id
	private Integer updateDeptId;
	//修改部门
	private String updateDeptName;
	//修改时间
	private Date updateTime;
	//邮件种类
	private String mailType;
	//承运人名称
	private String carrierName;
	//Barcode
	private String barcode;
	//CN38时间
	private Date cn38time;
	//接收扫描时间
	private Date scanTime;
	//启运地点
	private String startPlace;
	//启运时间
	private Date startTime;
	//中转到达地点
	private String transArrivePlace;
	//中转到达时间
	private Date transArriveTime;
	//中转启运时间
	private Date transStartTime;
	//到达地点
	private String arrivePlace;
	//目的地到达时间
	private Date arriveTime;
	//目的地交邮时间
	private Date holdTime;
	//收费路由
	private String tollRoute;
	//航班
	private String flightNum;
	//重量
	private BigDecimal weight;
	//费率
	private BigDecimal feeRate;
	//金额
	private BigDecimal amount;
	//币种
	private String currency;
	//账务时期
	private String accountPeriod;
	//账单编号
	private String billNo;
	//备注
	private String remark;
	//运能编码
	private String capacityCode;
	//箱板类型
	private String caseType;
	//集装器号（板号）
	private String packNum;
	//批次id
	private Integer batchId;
	//核对时间
	private Date checkTime;
	//核对人id
	private String checkPersonId;
	//核对人姓名
	private String checkPersonName;

	public List<CheckXydCarrierDO> getRows() {
		return rows;
	}

	public void setRows(List<CheckXydCarrierDO> rows) {
		this.rows = rows;
	}

	/**
	 * 设置：ID
	 */
	public void setId(Integer id) {
		this.id = id;
	}
	/**
	 * 获取：ID
	 */
	public Integer getId() {
		return id;
	}
	/**
	 * 设置：乐观锁;数据版本号
	 */
	public void setRevision(Integer revision) {
		this.revision = revision;
	}
	/**
	 * 获取：乐观锁;数据版本号
	 */
	public Integer getRevision() {
		return revision;
	}
	/**
	 * 设置：创建人id
	 */
	public void setCreateUserId(Integer createUserId) {
		this.createUserId = createUserId;
	}
	/**
	 * 获取：创建人id
	 */
	public Integer getCreateUserId() {
		return createUserId;
	}
	/**
	 * 设置：创建人
	 */
	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}
	/**
	 * 获取：创建人
	 */
	public String getCreateUserName() {
		return createUserName;
	}
	/**
	 * 设置：创建部门id
	 */
	public void setCreateDeptId(Integer createDeptId) {
		this.createDeptId = createDeptId;
	}
	/**
	 * 获取：创建部门id
	 */
	public Integer getCreateDeptId() {
		return createDeptId;
	}
	/**
	 * 设置：创建部门
	 */
	public void setCreateDeptName(String createDeptName) {
		this.createDeptName = createDeptName;
	}
	/**
	 * 获取：创建部门
	 */
	public String getCreateDeptName() {
		return createDeptName;
	}
	/**
	 * 设置：创建时间
	 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	/**
	 * 获取：创建时间
	 */
	public Date getCreateTime() {
		return createTime;
	}
	/**
	 * 设置：修改人id
	 */
	public void setUpdateUserId(Integer updateUserId) {
		this.updateUserId = updateUserId;
	}
	/**
	 * 获取：修改人id
	 */
	public Integer getUpdateUserId() {
		return updateUserId;
	}
	/**
	 * 设置：修改人
	 */
	public void setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
	}
	/**
	 * 获取：修改人
	 */
	public String getUpdateUserName() {
		return updateUserName;
	}
	/**
	 * 设置：修改部门id
	 */
	public void setUpdateDeptId(Integer updateDeptId) {
		this.updateDeptId = updateDeptId;
	}
	/**
	 * 获取：修改部门id
	 */
	public Integer getUpdateDeptId() {
		return updateDeptId;
	}
	/**
	 * 设置：修改部门
	 */
	public void setUpdateDeptName(String updateDeptName) {
		this.updateDeptName = updateDeptName;
	}
	/**
	 * 获取：修改部门
	 */
	public String getUpdateDeptName() {
		return updateDeptName;
	}
	/**
	 * 设置：修改时间
	 */
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	/**
	 * 获取：修改时间
	 */
	public Date getUpdateTime() {
		return updateTime;
	}
	/**
	 * 设置：邮件种类
	 */
	public void setMailType(String mailType) {
		this.mailType = mailType;
	}
	/**
	 * 获取：邮件种类
	 */
	public String getMailType() {
		return mailType;
	}
	/**
	 * 设置：承运人名称
	 */
	public void setCarrierName(String carrierName) {
		this.carrierName = carrierName;
	}
	/**
	 * 获取：承运人名称
	 */
	public String getCarrierName() {
		return carrierName;
	}
	/**
	 * 设置：Barcode
	 */
	public void setBarcode(String barcode) {
		this.barcode = barcode;
	}
	/**
	 * 获取：Barcode
	 */
	public String getBarcode() {
		return barcode;
	}
	/**
	 * 设置：CN38时间
	 */
	public void setCn38time(Date cn38time) {
		this.cn38time = cn38time;
	}
	/**
	 * 获取：CN38时间
	 */
	public Date getCn38time() {
		return cn38time;
	}
	/**
	 * 设置：接收扫描时间
	 */
	public void setScanTime(Date scanTime) {
		this.scanTime = scanTime;
	}
	/**
	 * 获取：接收扫描时间
	 */
	public Date getScanTime() {
		return scanTime;
	}
	/**
	 * 设置：启运地点
	 */
	public void setStartPlace(String startPlace) {
		this.startPlace = startPlace;
	}
	/**
	 * 获取：启运地点
	 */
	public String getStartPlace() {
		return startPlace;
	}
	/**
	 * 设置：启运时间
	 */
	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}
	/**
	 * 获取：启运时间
	 */
	public Date getStartTime() {
		return startTime;
	}
	/**
	 * 设置：中转到达地点
	 */
	public void setTransArrivePlace(String transArrivePlace) {
		this.transArrivePlace = transArrivePlace;
	}
	/**
	 * 获取：中转到达地点
	 */
	public String getTransArrivePlace() {
		return transArrivePlace;
	}
	/**
	 * 设置：中转到达时间
	 */
	public void setTransArriveTime(Date transArriveTime) {
		this.transArriveTime = transArriveTime;
	}
	/**
	 * 获取：中转到达时间
	 */
	public Date getTransArriveTime() {
		return transArriveTime;
	}
	/**
	 * 设置：中转启运时间
	 */
	public void setTransStartTime(Date transStartTime) {
		this.transStartTime = transStartTime;
	}
	/**
	 * 获取：中转启运时间
	 */
	public Date getTransStartTime() {
		return transStartTime;
	}
	/**
	 * 设置：到达地点
	 */
	public void setArrivePlace(String arrivePlace) {
		this.arrivePlace = arrivePlace;
	}
	/**
	 * 获取：到达地点
	 */
	public String getArrivePlace() {
		return arrivePlace;
	}
	/**
	 * 设置：目的地到达时间
	 */
	public void setArriveTime(Date arriveTime) {
		this.arriveTime = arriveTime;
	}
	/**
	 * 获取：目的地到达时间
	 */
	public Date getArriveTime() {
		return arriveTime;
	}
	/**
	 * 设置：目的地交邮时间
	 */
	public void setHoldTime(Date holdTime) {
		this.holdTime = holdTime;
	}
	/**
	 * 获取：目的地交邮时间
	 */
	public Date getHoldTime() {
		return holdTime;
	}
	/**
	 * 设置：收费路由
	 */
	public void setTollRoute(String tollRoute) {
		this.tollRoute = tollRoute;
	}
	/**
	 * 获取：收费路由
	 */
	public String getTollRoute() {
		return tollRoute;
	}
	/**
	 * 设置：航班
	 */
	public void setFlightNum(String flightNum) {
		this.flightNum = flightNum;
	}
	/**
	 * 获取：航班
	 */
	public String getFlightNum() {
		return flightNum;
	}
	/**
	 * 设置：重量
	 */
	public void setWeight(BigDecimal weight) {
		this.weight = weight;
	}
	/**
	 * 获取：重量
	 */
	public BigDecimal getWeight() {
		return weight;
	}
	/**
	 * 设置：费率
	 */
	public void setFeeRate(BigDecimal feeRate) {
		this.feeRate = feeRate;
	}
	/**
	 * 获取：费率
	 */
	public BigDecimal getFeeRate() {
		return feeRate;
	}
	/**
	 * 设置：金额
	 */
	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}
	/**
	 * 获取：金额
	 */
	public BigDecimal getAmount() {
		return amount;
	}
	/**
	 * 设置：币种
	 */
	public void setCurrency(String currency) {
		this.currency = currency;
	}
	/**
	 * 获取：币种
	 */
	public String getCurrency() {
		return currency;
	}
	/**
	 * 设置：账务时期
	 */
	public void setAccountPeriod(String accountPeriod) {
		this.accountPeriod = accountPeriod;
	}
	/**
	 * 获取：账务时期
	 */
	public String getAccountPeriod() {
		return accountPeriod;
	}
	/**
	 * 设置：账单编号
	 */
	public void setBillNo(String billNo) {
		this.billNo = billNo;
	}
	/**
	 * 获取：账单编号
	 */
	public String getBillNo() {
		return billNo;
	}
	/**
	 * 设置：备注
	 */
	public void setRemark(String remark) {
		this.remark = remark;
	}
	/**
	 * 获取：备注
	 */
	public String getRemark() {
		return remark;
	}
	/**
	 * 设置：运能编码
	 */
	public void setCapacityCode(String capacityCode) {
		this.capacityCode = capacityCode;
	}
	/**
	 * 获取：运能编码
	 */
	public String getCapacityCode() {
		return capacityCode;
	}
	/**
	 * 设置：箱板类型
	 */
	public void setCaseType(String caseType) {
		this.caseType = caseType;
	}
	/**
	 * 获取：箱板类型
	 */
	public String getCaseType() {
		return caseType;
	}
	/**
	 * 设置：集装器号（板号）
	 */
	public void setPackNum(String packNum) {
		this.packNum = packNum;
	}
	/**
	 * 获取：集装器号（板号）
	 */
	public String getPackNum() {
		return packNum;
	}
	/**
	 * 设置：批次id
	 */
	public void setBatchId(Integer batchId) {
		this.batchId = batchId;
	}
	/**
	 * 获取：批次id
	 */
	public Integer getBatchId() {
		return batchId;
	}
	/**
	 * 设置：核对时间
	 */
	public void setCheckTime(Date checkTime) {
		this.checkTime = checkTime;
	}
	/**
	 * 获取：核对时间
	 */
	public Date getCheckTime() {
		return checkTime;
	}
	/**
	 * 设置：核对人id
	 */
	public void setCheckPersonId(String checkPersonId) {
		this.checkPersonId = checkPersonId;
	}
	/**
	 * 获取：核对人id
	 */
	public String getCheckPersonId() {
		return checkPersonId;
	}
	/**
	 * 设置：核对人姓名
	 */
	public void setCheckPersonName(String checkPersonName) {
		this.checkPersonName = checkPersonName;
	}
	/**
	 * 获取：核对人姓名
	 */
	public String getCheckPersonName() {
		return checkPersonName;
	}
}
