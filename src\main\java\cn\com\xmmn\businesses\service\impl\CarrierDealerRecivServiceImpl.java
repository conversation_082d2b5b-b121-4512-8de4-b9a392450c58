package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.CarrierDealerRecivDao;
import cn.com.xmmn.businesses.domain.CarrierDealerRecivDO;
import cn.com.xmmn.businesses.dt.CarrierDealerRecivDetailDT;
import cn.com.xmmn.businesses.service.CarrierDealerRecivService;
import cn.com.xmmn.businesses.vo.CarrierDealerRecivListVo;
import cn.com.xmmn.common.utils.Query;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Service
public class CarrierDealerRecivServiceImpl  extends ServiceImpl<CarrierDealerRecivDao,CarrierDealerRecivDO> implements CarrierDealerRecivService {

    @Autowired
    private CarrierDealerRecivDao carrierDealerRecivDao;

    @Override
    public List<CarrierDealerRecivListVo> group(Map<String,Object> query) {


        return carrierDealerRecivDao.group(query);
    }

    @Override
    public List<CarrierDealerRecivListVo>  groupCount(Map<String,Object> query) {
        return carrierDealerRecivDao.groupCount(query);
    }


    @Override
    public List<CarrierDealerRecivDO> detail(CarrierDealerRecivDetailDT dealerRecivDetailDT) {
        return carrierDealerRecivDao.detail(dealerRecivDetailDT);
    }

    @Override
    public Integer detailCount(CarrierDealerRecivDetailDT dealerRecivDetailDT) {
        return carrierDealerRecivDao.detailCount(dealerRecivDetailDT);
    }
}
