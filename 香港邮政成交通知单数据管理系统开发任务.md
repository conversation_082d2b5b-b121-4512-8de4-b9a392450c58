# 上下文
文件名：香港邮政成交通知单数据管理系统开发任务.md
创建于：2025-07-31
创建者：AI Assistant
关联协议：RIPER-5 + Multidimensional + Agent Protocol 

# 任务描述
为香港邮政成交通知单数据管理系统开发完整的后端功能，包括：
1. 数据库表创建（t_hk_notice_aword）
2. 完整的MVC架构开发（Entity、Mapper、Service、Controller）
3. API接口开发（手动调用接口和定时任务接口）
4. 第三方接口集成和数据解密处理
5. 定时任务实现（每月1号自动获取上个月数据）

# 项目概述
基于Spring Boot框架的经营分析数据管理系统，需要集成香港邮政的成交通知单数据。系统采用MyBatis作为ORM框架，使用MySQL数据库，支持定时任务和手动API调用两种数据获取方式。

---
*以下部分由 AI 在协议执行过程中维护*
---

# 分析 (由 RESEARCH 模式填充)
## 现有代码结构分析
- **项目架构**：标准的Spring Boot + MyBatis架构
- **包结构**：cn.com.xmmn.businesses下分为controller、service、dao、domain等层次
- **解密工具**：发现两种解密方式，推荐使用EncryptUtils.aesSha1prngDecrypt()
- **HTTP工具**：现有HttpClientUtil类可用于第三方接口调用
- **定时任务**：使用@Scheduled注解实现，参考InterfaceImportJob
- **API控制器**：位于controller/api目录下，遵循RESTful设计

## 关键技术组件
- **实体类**：使用@Data注解，实现Serializable接口
- **DAO接口**：使用@Mapper注解，标准CRUD方法
- **Service层**：接口+实现类模式
- **MyBatis映射**：XML配置文件，支持动态SQL
- **日期处理**：需要实现yyyyMMdd到datetime的转换

# 提议的解决方案 (由 INNOVATE 模式填充)
## 解密方案
选择EncryptUtils.aesSha1prngDecrypt()方法，与现有CustomerOrderService保持一致。

## 架构设计
采用标准MVC三层架构：
1. **Controller层**：HkNoticeAwordController（API接口）
2. **Service层**：HkNoticeAwordService接口 + HkNoticeAwordServiceImpl实现
3. **DAO层**：HkNoticeAwordDao接口 + MyBatis XML映射
4. **Domain层**：HkNoticeAwordDO实体类

## API设计
1. **手动调用接口**：POST /api/manual/requestBill
2. **定时任务**：每月1号1:30执行，获取上个月数据
3. **共享业务逻辑**：统一的数据处理和存储逻辑

## 数据处理流程
1. 参数验证 → 2. 第三方接口调用 → 3. 数据解密 → 4. JSON解析 → 5. 字段映射 → 6. 日期转换 → 7. 数据库存储

# 实施计划 (由 PLAN 模式生成)
## 详细开发步骤

### 第一阶段：基础组件开发
1. **创建实体类**：HkNoticeAwordDO.java
   - 文件路径：src/main/java/cn/com/xmmn/businesses/domain/HkNoticeAwordDO.java
   - 包含所有数据库字段映射
   - 使用@Data注解和Serializable接口

2. **创建DAO接口**：HkNoticeAwordDao.java
   - 文件路径：src/main/java/cn/com/xmmn/businesses/dao/HkNoticeAwordDao.java
   - 标准CRUD方法：get、list、count、save、update、remove、batchRemove
   - 使用@Mapper注解

3. **创建MyBatis映射文件**：HkNoticeAwordMapper.xml
   - 文件路径：src/main/resources/mybatis/businesses/HkNoticeAwordMapper.xml
   - 实现所有DAO方法的SQL映射
   - 支持动态查询条件

### 第二阶段：业务逻辑开发
4. **创建Service接口**：HkNoticeAwordService.java
   - 文件路径：src/main/java/cn/com/xmmn/businesses/service/HkNoticeAwordService.java
   - 标准CRUD方法 + 业务方法

5. **创建Service实现类**：HkNoticeAwordServiceImpl.java
   - 文件路径：src/main/java/cn/com/xmmn/businesses/service/impl/HkNoticeAwordServiceImpl.java
   - 实现第三方接口调用逻辑
   - 实现数据解密和转换逻辑

### 第三阶段：API接口开发
6. **创建请求DTO**：HkNoticeAwordRequestDT.java
   - 文件路径：src/main/java/cn/com/xmmn/businesses/dt/api/HkNoticeAwordRequestDT.java
   - 包含yearMonthStr字段

7. **创建响应DTO**：HkNoticeAwordResponseDT.java
   - 文件路径：src/main/java/cn/com/xmmn/businesses/dt/api/HkNoticeAwordResponseDT.java
   - 映射第三方接口响应数据

8. **创建API控制器**：HkNoticeAwordController.java
   - 文件路径：src/main/java/cn/com/xmmn/businesses/controller/api/HkNoticeAwordController.java
   - 实现手动调用接口：POST /api/manual/requestBill

### 第四阶段：定时任务开发
9. **创建定时任务类**：HkNoticeAwordJob.java
   - 文件路径：src/main/java/cn/com/xmmn/businesses/task/HkNoticeAwordJob.java
   - 每月1号1:30自动执行
   - 获取上个月数据

### 第五阶段：配置和测试
10. **更新配置文件**：添加第三方接口配置
11. **创建单元测试**：验证各层功能
12. **集成测试**：端到端功能验证

## 实施检查清单：
1. 创建HkNoticeAwordDO实体类
2. 创建HkNoticeAwordDao接口
3. 创建HkNoticeAwordMapper.xml映射文件
4. 创建HkNoticeAwordService接口
5. 创建HkNoticeAwordServiceImpl实现类
6. 创建HkNoticeAwordRequestDT请求DTO
7. 创建HkNoticeAwordResponseDT响应DTO
8. 创建HkNoticeAwordController API控制器
9. 创建HkNoticeAwordJob定时任务类
10. 更新InterfaceConfig配置类
11. 创建单元测试类
12. 进行集成测试验证

# 当前执行步骤 (由 EXECUTE 模式在开始执行某步骤时更新)
> 正在执行: "1. 创建HkNoticeAwordDO实体类"

# 任务进度 (由 EXECUTE 模式在每步完成后追加)
*   2025-07-31
    *   步骤：1. 创建HkNoticeAwordDO实体类
    *   修改：创建文件 src/main/java/cn/com/xmmn/businesses/domain/HkNoticeAwordDO.java
    *   更改摘要：创建香港成交通知单实体类，包含所有数据库字段映射，使用BigDecimal处理价格字段
    *   原因：执行计划步骤 1
    *   阻碍：无
    *   用户确认状态：待确认

# 最终审查 (由 REVIEW 模式填充)
*待完成*
