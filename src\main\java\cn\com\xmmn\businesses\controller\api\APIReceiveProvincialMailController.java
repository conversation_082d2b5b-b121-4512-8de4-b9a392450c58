package cn.com.xmmn.businesses.controller.api;

import cn.com.xmmn.businesses.domain.*;
import cn.com.xmmn.businesses.service.*;
import cn.com.xmmn.common.utils.ApiResponseData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 接收省签邮件信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024年12月27日09:44:35
 */
@Slf4j
@RestController
@RequestMapping("/api/receive")
@RequiredArgsConstructor
public class APIReceiveProvincialMailController {

    //接口秘钥
    private static final String ENCRYPTION_KEY = "chinacourierhkchinacourierhk";

    @Autowired
    private XgCarrierDealerRecivService xgCarrierDealerRecivService;

    @Autowired
    private XgOutMailService xgOutMailService;

    @Autowired
    private XgImportMailService xgImportMailService;

    @Autowired
    private XgImportMailCargoService xgImportMailCargoService;
    @Autowired
    private XgImportMailTraceService xgImportMailTraceService;

    @Autowired
    private XgImportMailReceiptService xgImportMailReceiptService;
    @Autowired
    private XgXydBillDetailService xgXydBillDetailService;

    /**
     * 接收香港出口邮袋
     *
     * @param request         请求对象
     * @param dealerRecivList 包含多个 XgCarrierDealerRecivDO 对象的列表
     * @return ApiResponseData 返回响应数据
     */
    @PostMapping("/exportPouch")
    public ApiResponseData exportPouch(HttpServletRequest request, @RequestBody List<XgCarrierDealerRecivDO> dealerRecivList) {
        ApiResponseData apiResponseData = new ApiResponseData();
        String authenticate = request.getHeader("authenticate");
        int code = 200;
        String msg = "Ok";
        if (!ENCRYPTION_KEY.equals(authenticate)) {
            throw new RuntimeException("授权码错误!");
        }
        try {
            xgCarrierDealerRecivService.save(dealerRecivList);
        } catch (Exception e) {
            log.info("报错" + e);
            code = 500;
            msg = e.getMessage();
        }
        apiResponseData.setCode(code);
        apiResponseData.setMsg(msg);
        return apiResponseData;
    }

    /**
     * 接收香港出口邮件
     *
     * @param request         请求对象
     * @param xgOutMailDOList 包含多个 XgCarrierDealerRecivDO 对象的列表
     * @return ApiResponseData 返回响应数据
     */
    @PostMapping("/exportMail")
    public ApiResponseData exportMail(HttpServletRequest request, @RequestBody List<XgOutMailDO> xgOutMailDOList) {

        String authenticate = request.getHeader("authenticate");
        int code = 200;
        String msg = "Ok";
        if (!ENCRYPTION_KEY.equals(authenticate)) {
            throw new RuntimeException("授权码错误!");
        }
        try {
            xgOutMailService.save(xgOutMailDOList);
        } catch (Exception e) {
            log.info("报错" + e);
            code = 500;
            msg = e.getMessage();
        }
        ApiResponseData apiResponseData = new ApiResponseData();
        apiResponseData.setCode(code);
        apiResponseData.setMsg(msg);
        return apiResponseData;
    }

    /**
     * 接收香港进口邮件
     *
     * @param request       请求对象
     * @param xgOutMailList 包含多个 XgCarrierDealerRecivDO 对象的列表
     * @return ApiResponseData 返回响应数据
     */
    @PostMapping("/importMail")
    public ApiResponseData importMail(HttpServletRequest request, @RequestBody List<XgImportMailDO> xgOutMailList) {

        String authenticate = request.getHeader("authenticate");
        int code = 200;
        String msg = "Ok";
        if (!ENCRYPTION_KEY.equals(authenticate)) {
            throw new RuntimeException("授权码错误!");
        }
        try {
            xgImportMailService.save(xgOutMailList);
        } catch (Exception e) {
            log.info("报错" + e);
            code = 500;
            msg = e.getMessage();
        }
        ApiResponseData apiResponseData = new ApiResponseData();
        apiResponseData.setCode(code);
        apiResponseData.setMsg(msg);
        return apiResponseData;
    }

    /**
     * 接收香港进口内件邮件
     *
     * @param request        请求对象
     * @param xgOutMailListt 包含多个 XgCarrierDealerRecivDO 对象的列表
     * @return ApiResponseData 返回响应数据
     */
    @PostMapping("/importMailCargo")
    public ApiResponseData importMailCargo(HttpServletRequest request, @RequestBody List<XgImportMailCargoDO> xgOutMailListt) {

        String authenticate = request.getHeader("authenticate");
        int code = 200;
        String msg = "Ok";
        if (!ENCRYPTION_KEY.equals(authenticate)) {
            throw new RuntimeException("授权码错误!");
        }
        try {
            xgImportMailCargoService.save(xgOutMailListt);
        } catch (Exception e) {
            log.info("报错" + e);
            code = 500;
            msg = e.getMessage();
        }
        ApiResponseData apiResponseData = new ApiResponseData();
        apiResponseData.setCode(code);
        apiResponseData.setMsg(msg);
        return apiResponseData;
    }

    /**
     * 接收香港轨迹
     *
     * @param request               请求对象
     * @param xgImportMailTraceList 包含多个 XgCarrierDealerRecivDO 对象的列表
     * @return ApiResponseData 返回响应数据
     */
    @PostMapping("/importMailTrace")
    public ApiResponseData importMailTrace(HttpServletRequest request, @RequestBody List<XgImportMailTraceDO> xgImportMailTraceList) {

        String authenticate = request.getHeader("authenticate");
        int code = 200;
        String msg = "Ok";
        if (!ENCRYPTION_KEY.equals(authenticate)) {
            throw new RuntimeException("授权码错误!");
        }
        try {
            xgImportMailTraceService.save(xgImportMailTraceList);
        } catch (Exception e) {
            log.info("报错" + e);
            code = 500;
            msg = e.getMessage();
        }
        ApiResponseData apiResponseData = new ApiResponseData();
        apiResponseData.setCode(code);
        apiResponseData.setMsg(msg);
        return apiResponseData;
    }

    /**
     * 接收香港回执接口
     *
     * @param request                 请求对象
     * @param xgImportMailReceiptList 包含多个 XgCarrierDealerRecivDO 对象的列表
     * @return ApiResponseData 返回响应数据
     */
    @PostMapping("/importMailReceipt")
    public ApiResponseData importMailReceipt(HttpServletRequest request, @RequestBody List<XgImportMailReceiptDO> xgImportMailReceiptList) {

        String authenticate = request.getHeader("authenticate");
        int code = 200;
        String msg = "Ok";
        if (!ENCRYPTION_KEY.equals(authenticate)) {
            throw new RuntimeException("授权码错误!");
        }
        try {
            xgImportMailReceiptService.save(xgImportMailReceiptList);
        } catch (Exception e) {
            log.info("报错" + e);
            code = 500;
            msg = e.getMessage();
        }
        ApiResponseData apiResponseData = new ApiResponseData();
        apiResponseData.setCode(code);
        apiResponseData.setMsg(msg);
        return apiResponseData;
    }

    /**
     * 接收香港出口结算接口
     *
     * @param request                 请求对象
     * @param xgImportMailReceiptList 包含多个 XgCarrierDealerRecivDO 对象的列表
     * @return ApiResponseData 返回响应数据
     */
    @PostMapping("/exportBillSettle")
    public ApiResponseData exportBillSettle(HttpServletRequest request, @RequestBody List<XgXydBillDetailDO> xgImportMailReceiptList) {

        String authenticate = request.getHeader("authenticate");
        int code = 200;
        String msg = "Ok";
        if (!ENCRYPTION_KEY.equals(authenticate)) {
            throw new RuntimeException("授权码错误!");
        }
        try {
            xgXydBillDetailService.save(xgImportMailReceiptList);
        } catch (DuplicateKeyException e) {
            log.warn("主键重复: {}", e.getMessage());
            code = 200;
            msg = "数据已存在，无法重复插入";
        } catch (Exception e) {
            log.info("报错" + e);
            code = 500;
            msg = e.getMessage();
        }
        ApiResponseData apiResponseData = new ApiResponseData();
        apiResponseData.setCode(code);
        apiResponseData.setMsg(msg);
        return apiResponseData;
    }
}
