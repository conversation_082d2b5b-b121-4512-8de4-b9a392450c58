package cn.com.xmmn.system.controller;

import cn.com.xmmn.system.common.SuperAdmin;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import cn.com.xmmn.common.annotation.Log;
import cn.com.xmmn.common.config.Constant;
import cn.com.xmmn.common.controller.BaseController;
import cn.com.xmmn.common.domain.Tree;
import cn.com.xmmn.common.utils.R;
import cn.com.xmmn.system.domain.MenuDO;
import cn.com.xmmn.system.service.MenuService;

import java.util.*;

@RequestMapping("/sys/menu")
@Controller
@Slf4j
public class MenuController extends BaseController {
	String prefix = "system/menu";
	@Autowired
	MenuService menuService;

	@RequiresPermissions("sys:menu:menu")
	@GetMapping()
	String menu(Model model) {
		return prefix+"/menu";
	}

	@RequiresPermissions("sys:menu:menu")
	@RequestMapping("/list")
	@ResponseBody
	List<MenuDO> list(@RequestParam Map<String, Object> params) {
		List<MenuDO> menus = menuService.list(params);
		//log.info(menus.toString());
		List<String> privilegeMenu = new ArrayList<>(SuperAdmin.getPrivilegeMenu());
		// 非超级管理员，剔除超级管理员角色
		if(!SuperAdmin.getUserId().equals(getUserId())){
			int menuDel;
			do {
				menuDel = 0;
				if (!menus.isEmpty()) {
					Iterator it = menus.iterator();
					while (it.hasNext()) {
						MenuDO menuDO = (MenuDO) it.next();
						if (privilegeMenu.contains(menuDO.getMenuId().toString())) {
							it.remove();
							menuDel++;
						}
						if (privilegeMenu.contains(menuDO.getParentId().toString())){
							it.remove();
							menuDel++;
							privilegeMenu.add(menuDO.getMenuId().toString());
						}
					}
				}
			} while (menuDel > 0);
		}
		return menus;
	}

	@Log("添加菜单")
	@RequiresPermissions("sys:menu:add")
	@GetMapping("/add/{pId}")
	String add(Model model, @PathVariable("pId") Long pId) {
		model.addAttribute("pId", pId);
		if (pId == 0) {
			model.addAttribute("pName", "根目录");
		} else {
			model.addAttribute("pName", menuService.get(pId).getName());
		}
		return prefix + "/add";
	}

	@Log("编辑菜单")
	@RequiresPermissions("sys:menu:edit")
	@GetMapping("/edit/{id}")
	String edit(Model model, @PathVariable("id") Long id) {
		MenuDO mdo = menuService.get(id);
		Long pId = mdo.getParentId();
		model.addAttribute("pId", pId);
		if (pId == 0) {
			model.addAttribute("pName", "根目录");
		} else {
			model.addAttribute("pName", menuService.get(pId).getName());
		}
		model.addAttribute("menu", mdo);
		return prefix+"/edit";
	}

	@Log("保存菜单")
	@RequiresPermissions("sys:menu:add")
	@PostMapping("/save")
	@ResponseBody
	R save(MenuDO menu) {
		if (Constant.DEMO_ACCOUNT.equals(getUsername())) {
			return R.error(1, "演示系统不允许修改,完整体验请部署程序");
		}
		if (menuService.save(menu) > 0) {
			return R.ok();
		} else {
			return R.error(1, "保存失败");
		}
	}

	@Log("更新菜单")
	@RequiresPermissions("sys:menu:edit")
	@PostMapping("/update")
	@ResponseBody
	R update(MenuDO menu) {
		if (Constant.DEMO_ACCOUNT.equals(getUsername())) {
			return R.error(1, "演示系统不允许修改,完整体验请部署程序");
		}
		if (menuService.update(menu) > 0) {
			return R.ok();
		} else {
			return R.error(1, "更新失败");
		}
	}

	@Log("删除菜单")
	@RequiresPermissions("sys:menu:remove")
	@PostMapping("/remove")
	@ResponseBody
	R remove(Long id) {
		if (Constant.DEMO_ACCOUNT.equals(getUsername())) {
			return R.error(1, "演示系统不允许修改,完整体验请部署程序");
		}
		if (menuService.remove(id) > 0) {
			return R.ok();
		} else {
			return R.error(1, "删除失败");
		}
	}

	@GetMapping("/tree")
	@ResponseBody
	Tree<MenuDO> tree() {
		Tree<MenuDO>  tree = menuService.getTree();
		// 非超级管理员，剔除超级管理员角色
		if(!SuperAdmin.getUserId().equals(getUserId())){
			if(!tree.getChildren().isEmpty()) {
				Iterator it = tree.getChildren().iterator();
				while (it.hasNext()) {
					Tree<MenuDO> treeMenu = (Tree<MenuDO>) it.next();
					if (SuperAdmin.getPrivilegeMenu().contains(treeMenu.getId())) {
						it.remove();
					}
				}
			}
		}
		return tree;
	}

	@GetMapping("/tree/{roleId}")
	@ResponseBody
	Tree<MenuDO> tree(@PathVariable("roleId") Long roleId) {
		Tree<MenuDO> tree = menuService.getTree(roleId);
		// 非超级管理员，剔除超级管理员角色
		if(!SuperAdmin.getUserId().equals(getUserId())){
			if(!tree.getChildren().isEmpty()) {
				Iterator it = tree.getChildren().iterator();
				while (it.hasNext()) {
					Tree<MenuDO> treeMenu = (Tree<MenuDO>) it.next();
					if (SuperAdmin.getPrivilegeMenu().contains(treeMenu.getId())) {
						it.remove();
					}
				}
			}
		}
		return tree;
	}
}
