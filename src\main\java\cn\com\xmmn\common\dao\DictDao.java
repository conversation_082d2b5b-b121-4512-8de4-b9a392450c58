package cn.com.xmmn.common.dao;

import java.util.List;
import java.util.Map;

import cn.com.xmmn.businesses.domain.FeedbackRecordsDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import cn.com.xmmn.common.domain.DictDO;

/**
 * 字典表
 */
@Mapper
public interface DictDao extends BaseMapper<DictDO> {

	DictDO get(Long id);

	List<DictDO> list(Map<String, Object> map);

	int count(Map<String, Object> map);

	int save(DictDO dict);

	int update(DictDO dict);

	int remove(Long id);

	int batchRemove(Long[] ids);

	List<DictDO> listType();
}
