package cn.com.xmmn.businesses.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;



/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-06 10:55:15
 */
@Data
@ApiModel(value = "香港进口内件表",description = "")
@TableName(value = "tb_xg_import_mail_cargo")
public class XgImportMailCargoDO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//
	private String id;
	//
	private String itemId;
	//
	private String bizName;
	//
	private String taxNo;
	//
	private String originNationCode;
	//
	private BigDecimal goodsNum;
	//
	private String meterUnit;
	//
	private BigDecimal declareTotalVaule;
	//
	private Date pt;


}
