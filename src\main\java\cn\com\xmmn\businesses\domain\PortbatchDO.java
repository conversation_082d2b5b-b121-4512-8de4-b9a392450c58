package cn.com.xmmn.businesses.domain;

import java.io.Serializable;
import java.util.Date;



/**
 * 口岸导入批次表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-22 01:30:58
 */
public class PortbatchDO implements Serializable {
	private static final long serialVersionUID = 1L;

	//ID;批次号
	private Integer id;
	//乐观锁;数据版本号
	private Integer revision;
	//创建人id
	private Integer createUserId;
	//创建人
	private String createUserName;
	//创建部门id
	private Integer createDeptId;
	//创建部门
	private String createDeptName;
	//创建时间
	private Date createTime;
	//修改人id
	private Integer updateUserId;
	//修改人
	private String updateUserName;
	//修改部门id
	private Integer updateDeptId;
	//修改部门
	private String updateDeptName;
	//修改时间
	private Date updateTime;
	//口岸id
	private String portId;
	//口岸名称
	private String portName;
	//渠道商id
	private Integer channelId;
	//渠道商名称
	private String channelName;
	//账单数
	private Integer billNum;
	//明细数
	private Integer detailNum;
	//结算类型
	private String settlementType;
	/**
	 * 设置：ID;批次号
	 */
	public void setId(Integer id) {
		this.id = id;
	}
	/**
	 * 获取：ID;批次号
	 */
	public Integer getId() {
		return id;
	}
	/**
	 * 设置：乐观锁;数据版本号
	 */
	public void setRevision(Integer revision) {
		this.revision = revision;
	}
	/**
	 * 获取：乐观锁;数据版本号
	 */
	public Integer getRevision() {
		return revision;
	}
	/**
	 * 设置：创建人id
	 */
	public void setCreateUserId(Integer createUserId) {
		this.createUserId = createUserId;
	}
	/**
	 * 获取：创建人id
	 */
	public Integer getCreateUserId() {
		return createUserId;
	}
	/**
	 * 设置：创建人
	 */
	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}
	/**
	 * 获取：创建人
	 */
	public String getCreateUserName() {
		return createUserName;
	}
	/**
	 * 设置：创建部门id
	 */
	public void setCreateDeptId(Integer createDeptId) {
		this.createDeptId = createDeptId;
	}
	/**
	 * 获取：创建部门id
	 */
	public Integer getCreateDeptId() {
		return createDeptId;
	}
	/**
	 * 设置：创建部门
	 */
	public void setCreateDeptName(String createDeptName) {
		this.createDeptName = createDeptName;
	}
	/**
	 * 获取：创建部门
	 */
	public String getCreateDeptName() {
		return createDeptName;
	}
	/**
	 * 设置：创建时间
	 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	/**
	 * 获取：创建时间
	 */
	public Date getCreateTime() {
		return createTime;
	}
	/**
	 * 设置：修改人id
	 */
	public void setUpdateUserId(Integer updateUserId) {
		this.updateUserId = updateUserId;
	}
	/**
	 * 获取：修改人id
	 */
	public Integer getUpdateUserId() {
		return updateUserId;
	}
	/**
	 * 设置：修改人
	 */
	public void setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
	}
	/**
	 * 获取：修改人
	 */
	public String getUpdateUserName() {
		return updateUserName;
	}
	/**
	 * 设置：修改部门id
	 */
	public void setUpdateDeptId(Integer updateDeptId) {
		this.updateDeptId = updateDeptId;
	}
	/**
	 * 获取：修改部门id
	 */
	public Integer getUpdateDeptId() {
		return updateDeptId;
	}
	/**
	 * 设置：修改部门
	 */
	public void setUpdateDeptName(String updateDeptName) {
		this.updateDeptName = updateDeptName;
	}
	/**
	 * 获取：修改部门
	 */
	public String getUpdateDeptName() {
		return updateDeptName;
	}
	/**
	 * 设置：修改时间
	 */
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	/**
	 * 获取：修改时间
	 */
	public Date getUpdateTime() {
		return updateTime;
	}
	/**
	 * 设置：口岸id
	 */
	public void setPortId(String portId) {
		this.portId = portId;
	}
	/**
	 * 获取：口岸id
	 */
	public String getPortId() {
		return portId;
	}
	/**
	 * 设置：口岸名称
	 */
	public void setPortName(String portName) {
		this.portName = portName;
	}
	/**
	 * 获取：口岸名称
	 */
	public String getPortName() {
		return portName;
	}
	/**
	 * 设置：渠道商id
	 */
	public void setChannelId(Integer channelId) {
		this.channelId = channelId;
	}
	/**
	 * 获取：渠道商id
	 */
	public Integer getChannelId() {
		return channelId;
	}
	/**
	 * 设置：渠道商名称
	 */
	public void setChannelName(String channelName) {
		this.channelName = channelName;
	}
	/**
	 * 获取：渠道商名称
	 */
	public String getChannelName() {
		return channelName;
	}
	/**
	 * 设置：账单数
	 */
	public void setBillNum(Integer billNum) {
		this.billNum = billNum;
	}
	/**
	 * 获取：账单数
	 */
	public Integer getBillNum() {
		return billNum;
	}
	/**
	 * 设置：明细数
	 */
	public void setDetailNum(Integer detailNum) {
		this.detailNum = detailNum;
	}
	/**
	 * 获取：明细数
	 */
	public Integer getDetailNum() {
		return detailNum;
	}

	public String getSettlementType() {
		return settlementType;
	}

	public void setSettlementType(String settlementType) {
		this.settlementType = settlementType;
	}
}
