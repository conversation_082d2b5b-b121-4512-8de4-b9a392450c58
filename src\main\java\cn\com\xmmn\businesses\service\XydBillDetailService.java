package cn.com.xmmn.businesses.service;

import cn.com.xmmn.businesses.domain.CheckXydCarrierDO;
import cn.com.xmmn.businesses.domain.XydBillDetailDO;
import cn.com.xmmn.common.utils.Query;
import io.swagger.models.auth.In;


import java.util.List;
import java.util.Map;

/**
 * 新一代收入账单明细
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-20 13:12:29
 */
public interface XydBillDetailService {
	
	XydBillDetailDO get(Integer id);
	
	List<XydBillDetailDO> list(Map<String, Object> map);
	
	int count(Map<String, Object> map);
	
	int save(XydBillDetailDO xydBillDetail);
	
	int update(XydBillDetailDO xydBillDetail);
	
	int remove(Integer id);

	void barcodesRemove(String[] barcodes);

	List<CheckXydCarrierDO> checkList(Map<String, Object> map);

	int checkCount(Map<String, Object> map);
}
