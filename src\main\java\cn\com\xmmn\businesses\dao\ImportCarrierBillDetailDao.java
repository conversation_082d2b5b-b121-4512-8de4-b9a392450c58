package cn.com.xmmn.businesses.dao;

import cn.com.xmmn.businesses.domain.ImportCarrierBillDetailDO;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

/**
 * 承运商账单明细
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-27 15:37:47
 */
@Mapper
public interface ImportCarrierBillDetailDao {

	ImportCarrierBillDetailDO get(Integer id);
	
	List<ImportCarrierBillDetailDO> list(Map<String,Object> map);
	
	int count(Map<String,Object> map);

	int countRow(String barcode);

	int countHis(String barcode);
	
	int save(ImportCarrierBillDetailDO ImportcarrierBillDetail);
	
	int update(ImportCarrierBillDetailDO ImportcarrierBillDetail);


	int settleUpdate(ImportCarrierBillDetailDO ImportcarrierBillDetail);
	
	int remove(Integer id);
	
	int batchRemove(Map<String,Object> map);

	List<ImportCarrierBillDetailDO> listByIds(Map<String,Object> map);
}
