package cn.com.xmmn.businesses.service;

import cn.com.xmmn.businesses.domain.CarrierDealerRecivDO;
import cn.com.xmmn.businesses.dt.CarrierDealerRecivDetailDT;
import cn.com.xmmn.businesses.vo.CarrierDealerD2AListVo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * 承运商启运至运抵时限情况
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-04 09:18:55
 */
public interface CarrierDealerD2AService extends IService<CarrierDealerRecivDO> {


    /**
     * 分组获取承运商邮袋
     *
     * @param query
     * @return
     */
    List<CarrierDealerD2AListVo> group(Map<String, Object> query);

    /**
     * 分组获取承运商邮袋总数量
     *
     * @param query
     * @return
     */
    List<CarrierDealerD2AListVo>  groupCount(Map<String, Object> query);

    /**
     * 承运商邮袋详情
     * @param dealerRecivDetailDT
     * @return
     */
    List<CarrierDealerRecivDO> detail(CarrierDealerRecivDetailDT dealerRecivDetailDT);

    /**
     * 承运商邮袋总数
     * @param dealerRecivDetailDT
     * @return
     */
    Integer detailCount(CarrierDealerRecivDetailDT dealerRecivDetailDT);
}
