package cn.com.xmmn.report.controller;

import cn.com.xmmn.businesses.service.OrderDeliveryService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * TMS订单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-30 20:15:07
 */
@Slf4j
@Controller
@RequestMapping("/report/hongkongReport")
public class hongkongReportController {

    @Autowired
    private OrderDeliveryService orderDeliveryService;


    @GetMapping()
    @RequiresPermissions("report:hongkongReport")
    String init(Model model) {
        //设置初始时间  初始时间为三个月前的第一天，比如今天3月22日，则开始时间设为2023-01，结束时间为2023-03
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DATE, 1);// 设为当前月的1号
        calendar.add(Calendar.MONTH, -2);// 0表示当前月，-2就是当前月-2
        Date d = calendar.getTime();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String startDate = sdf.format(d);
        String endDate = sdf.format(new Date());
        //柱形图时间
        model.addAttribute("startDate", startDate);
        model.addAttribute("endDate", endDate);
        //折线图时间
        model.addAttribute("zxStartDate", startDate);
        model.addAttribute("zxEndDate", endDate);

        //饼图时间
        model.addAttribute("sxStartDate", endDate);
        return "report/hongkongEmail";
    }

    //    柱状图
    @ResponseBody
    @PostMapping("/list")
    @RequiresPermissions("report:hongkongReport")
    public Map<String, Object> OrdersTms(String startDate, String endDate) {
        Map<String, Object> data = new HashMap<String, Object>();

        /**********A1.柱形图数据************/
        String endDateStr = endDate + "-32";//加30天
        List<String> title = getTitle(startDate, endDate);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("endDateStr", endDateStr);
        map.put("startDate", startDate);
        map.put("title", title);
        List<Map<String, Object>> dataList = orderDeliveryService.hongkongList(map);
        String result = JSON.toJSONString(dataList).toString();
        log.info("返回结果：" + result);
//        title.add(0, "product");//Echart显示表头的时候需要用到
//        log.info("title" + title.toString());
//        model.addAttribute("yuefen", title);
        data.put("result", result);
        data.put("barnum", title.size());

        /**********A2柱形图右侧的环比数据************/
        List<String> huanbi = getHuanbiMonth(endDate);//若 end = '2022-11',则返回[2022-10, 2022-11],比较环比升降用的
        map.put("huanbi", huanbi);
        map.put("huanbiStart", huanbi.get(0));
        List<Map<String, Object>> huanbiList = orderDeliveryService.huanbiList(map);
        data.put("huanbi", huanbiList);
        data.put("status", "success");

        return data;

    }


    //    折线图
    @ResponseBody
    @PostMapping("/zhexian")
    @RequiresPermissions("report:hongkongReport")
    public Map<String, Object> zhexian(String startDate, String endDate) {
        Map<String, Object> data = new HashMap<String, Object>();
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        List<String> title = getTitle(startDate, endDate);//获取区间月份
        int weeks = 4;  //每个月至少4周，接下去是计算折线图的横轴是显示4周或者5周或者6周
        List<String> weeklist = new ArrayList<String>();
        weeklist.add("第1周");
        weeklist.add("第2周");
        weeklist.add("第3周");
        weeklist.add("第4周");
        //计算折线图横轴的周数
        for (int i = 0; i < title.size(); i++) {
            String laseDate = getLastDayOfMonth(title.get(i));
            int firstWeek = orderDeliveryService.week(title.get(i) + "-01");//获取该月1号是第几周
            int lastWeek = orderDeliveryService.week(title.get(i) +"-"+ laseDate);//获取该月最后一天是第几周
            int weekTemp = lastWeek - firstWeek + 1;//计算该月总共有多少周
            log.info(title.get(i) + "总周数:" + weekTemp);
            if (weekTemp - weeks > 0) {
                weeks = weekTemp;
            }
        }
        if (weeks == 5) {
            weeklist.add("第5周");
        } else if (weeks == 6) {
            weeklist.add("第5周");
            weeklist.add("第6周");
        }
        log.info("所选月份区间，最大月份周数为：" + weeks);
        /**********A1.折线图数据************/
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();//定义折线图数据主体
        for (int i = 0; i < title.size(); i++) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("monthStr", title.get(i));
            map.put("monthFristDay", title.get(i) + "-01");
            map.put("weeks", weeks);
            List<Map<String, Object>> dataList = orderDeliveryService.tuotoulv(map);
            log.info(title.get(i) + "妥投率:" + dataList.toString());
            Map<String, Object> mapResult = new HashMap<String, Object>();
            List listResult = new ArrayList();
            for (int j = 0; j < dataList.size(); j++) {
                Map<String, Object> mapTemp = dataList.get(j);
                listResult.add(mapTemp.get("rate"));
            }
            mapResult.put("yuefen", title.get(i));//折线图月份表头
            mapResult.put("listResult", listResult);//折线图月份数据
            result.add(mapResult);
        }
        data.put("status", "success");
        data.put("weeklist", weeklist);//折线图横轴
        data.put("result", result);//折线图数据
        return data;

    }


    //    饼图--妥投失败原因分类
    @ResponseBody
    @PostMapping("/shanxing")
    @RequiresPermissions("report:hongkongReport")
    public Map<String, Object> shanxing(String startDate) {
        Map<String, Object> data = new HashMap<String, Object>();

        /**********A1.饼图数据************/
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("startDate", startDate);
        List<Map<String, Object>> dataList = orderDeliveryService.tuotou(map);
        String result = JSON.toJSONString(dataList).toString();
        log.info("返回结果：" + result);
//        title.add(0, "product");//Echart显示表头的时候需要用到
//        log.info("title" + title.toString());
//        model.addAttribute("yuefen", title);
        data.put("result", result);
        data.put("status", "success");

        return data;

    }


    //获取sql需要分组的参数
    public List<String> getTitle(String begin, String end) {
        //若 begin = '2022-11',end = '2023-03',则返回[2022-11, 2022-12, 2023-01, 2023-02, 2023-03]
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        List<String> list = new ArrayList<String>();
        /*list.add("product"); //Echart显示表头的时候需要用到*/
        Date d1;
        Date d2;
        try {
            d1 = new SimpleDateFormat("yyyy-MM").parse(begin);
            d2 = new SimpleDateFormat("yyy-MM").parse(end);//定义结束日期可以去当前月也可以手动写日期。
            Calendar dd = Calendar.getInstance();//定义日期实例
            dd.setTime(d1);//设置日期起始时间
            while (dd.getTime().before(d2)) {//判断是否到结束日期
                SimpleDateFormat sdf = new SimpleDateFormat("yyy-MM");
                String str = sdf.format(dd.getTime());
                dd.add(Calendar.MONTH, 1);//进行当前日期月份加1
                list.add(str);
            }
            list.add(end);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return list;

    }

    // 使用参数月份,得到上一个月及参数月，计算环比用
    public List<String> getHuanbiMonth(String end) {
        //若 end = '2022-11',则返回[2022-10, 2022-11]
        List<String> list = new ArrayList<String>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        try {
            date = sdf.parse(end + "-" + "01");
        } catch (ParseException e) {
            e.printStackTrace();
        }

        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MONTH, -1);
        String lastDate = "";
        if(c.get(Calendar.MONTH) <9) {//做这判断是因为，如果上一个月是10/11/12 则会变成2023-010/011/012
            lastDate = c.get(Calendar.YEAR) + "-0"
                    + (c.get(Calendar.MONTH) + 1);
        }else{
            lastDate = c.get(Calendar.YEAR) + "-"
                    + (c.get(Calendar.MONTH) + 1);
        }

        list.add(lastDate);
        list.add(end);
        return list;
    }

    //使用Calendar的set和add方法，从下个月的第一天计算得到当前月的最后一天,并得到最后一天是28/29/30/31
    public String getLastDayOfMonth(String dateStr) {//参数2023-03，返回结果31

        String year = dateStr.substring(0,4);
        String month = dateStr.substring(5,7);
        Calendar cal = Calendar.getInstance();
        //年
        cal.set(Calendar.YEAR, Integer.parseInt(year));
        //月，因为Calendar里的月是从0开始，所以要-1
        cal.set(Calendar.MONTH, Integer.parseInt(month) - 1);
        //日，设为一号
        cal.set(Calendar.DATE, 1);
        //月份加一，得到下个月的一号
        cal.add(Calendar.MONTH,1);
        //下一个月减一为本月最后一天
        cal.add(Calendar.DATE, -1);
        String monthEnd = String.valueOf(cal.get(Calendar.DAY_OF_MONTH));//获得月末是几号
//        System.out.println(year+month+",获得本月月末:" + monthEnd);
        return monthEnd;
    }
}
