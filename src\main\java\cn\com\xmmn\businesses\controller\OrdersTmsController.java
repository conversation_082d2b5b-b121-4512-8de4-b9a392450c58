package cn.com.xmmn.businesses.controller;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import cn.com.xmmn.businesses.domain.OrdersTmsDO;
import cn.com.xmmn.businesses.service.OrdersTmsService;
import cn.com.xmmn.common.utils.PageUtils;
import cn.com.xmmn.common.utils.Query;
import cn.com.xmmn.common.utils.R;

/**
 * TMS订单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-30 20:15:07
 */

@Slf4j
@Controller
@RequestMapping("/businesses/ordersTms")
public class OrdersTmsController {
    @Autowired
    private OrdersTmsService ordersTmsService;

    @GetMapping()
    @RequiresPermissions("businesses:ordersTms:ordersTms")
    String OrdersTms() {
        return "businesses/ordersTms/ordersTms";
    }

    @ResponseBody
    @GetMapping("/list")
    @RequiresPermissions("businesses:ordersTms:ordersTms")
    public PageUtils list(@RequestParam Map<String, Object> params) {
        //查询列表数据
        Query query = new Query(params);
        List<OrdersTmsDO> ordersTmsList = ordersTmsService.list(query);
        int total = ordersTmsService.count(query);
        PageUtils pageUtils = new PageUtils(ordersTmsList, total);
        return pageUtils;
    }

    @ResponseBody
    @PostMapping("/totalCount")
    @RequiresPermissions("businesses:ordersTms:ordersTms")
    public Map<String, Object> totalCount(String dateStart, String dateEnd) {
        Map<String, Object> data = new HashMap<>();
        Map<String, Object> query = new HashMap<>();
        query.put("dateStart",dateStart);
        query.put("dateEnd",dateEnd);
        //新增合计值
        Map<String, Object> countMap = ordersTmsService.countList(query);
        BigDecimal pieces = new BigDecimal(0);
        BigDecimal grossweights = new BigDecimal(0);
        BigDecimal volumeweights = new BigDecimal(0);
        BigDecimal shipweights = new BigDecimal(0);
        BigDecimal chargeweights = new BigDecimal(0);
        BigDecimal serverweights = new BigDecimal(0);
        BigDecimal fees = new BigDecimal(0);
        BigDecimal otherfees = new BigDecimal(0);
        BigDecimal totalfees = new BigDecimal(0);
        //新增合计
        if (countMap != null) {
            BigDecimal r0 = (BigDecimal) countMap.get("pieces");
            pieces = r0.setScale(3, BigDecimal.ROUND_HALF_UP);
            BigDecimal r1 = (BigDecimal) countMap.get("grossweights");
            grossweights = r1.setScale(3, BigDecimal.ROUND_HALF_UP);
            BigDecimal r2 = (BigDecimal) countMap.get("volumeweights");
            volumeweights = r2.setScale(3, BigDecimal.ROUND_HALF_UP);
            BigDecimal r3 = (BigDecimal) countMap.get("shipweights");
            shipweights = r3.setScale(3, BigDecimal.ROUND_HALF_UP);
            BigDecimal r4 = (BigDecimal) countMap.get("chargeweights");
            chargeweights = r4.setScale(3, BigDecimal.ROUND_HALF_UP);
            BigDecimal r5 = (BigDecimal) countMap.get("serverweights");
            serverweights = r5.setScale(3, BigDecimal.ROUND_HALF_UP);
            BigDecimal r6 = (BigDecimal) countMap.get("fees");
            fees = r6.setScale(3, BigDecimal.ROUND_HALF_UP);
            BigDecimal r7 = (BigDecimal) countMap.get("otherfees");
            otherfees = r7.setScale(3, BigDecimal.ROUND_HALF_UP);
            BigDecimal r8 = (BigDecimal) countMap.get("totalfees");
            totalfees = r8.setScale(3, BigDecimal.ROUND_HALF_UP);
        }
        data.put("pieces", pieces);
        data.put("grossweights", grossweights);
        data.put("volumeweights", volumeweights);
        data.put("shipweights", shipweights);
        data.put("chargeweights", chargeweights);
        data.put("serverweights", serverweights);
        data.put("fees", fees);
        data.put("otherfees", otherfees);
        data.put("totalfees", totalfees);
        return data;
    }

    @GetMapping("/add")
    @RequiresPermissions("businesses:ordersTms:add")
    String add() {
        return "businesses/ordersTms/add";
    }

    @GetMapping("/edit/{id}")
    @RequiresPermissions("businesses:ordersTms:edit")
    String edit(@PathVariable("id") Integer id, Model model) {
        OrdersTmsDO ordersTms = ordersTmsService.get(id);
        model.addAttribute("ordersTms", ordersTms);
        return "businesses/ordersTms/edit";
    }

    /**
     * 保存
     */
    @ResponseBody
    @PostMapping("/save")
    @RequiresPermissions("businesses:ordersTms:add")
    public R save(OrdersTmsDO ordersTms) {
        if (ordersTmsService.save(ordersTms) > 0) {
            return R.ok();
        }
        return R.error();
    }

    /**
     * 修改
     */
    @ResponseBody
    @RequestMapping("/update")
    @RequiresPermissions("businesses:ordersTms:edit")
    public R update(OrdersTmsDO ordersTms) {
        ordersTmsService.update(ordersTms);
        return R.ok();
    }

    /**
     * 删除
     */
    @PostMapping("/remove")
    @ResponseBody
    @RequiresPermissions("businesses:ordersTms:remove")
    public R remove(Integer id) {
        if (ordersTmsService.remove(id) > 0) {
            return R.ok();
        }
        return R.error();
    }

    /**
     * 删除
     */
    @PostMapping("/batchRemove")
    @ResponseBody
    @RequiresPermissions("businesses:ordersTms:batchRemove")
    public R remove(@RequestParam("ids[]") Integer[] ids) {
        ordersTmsService.batchRemove(ids);
        return R.ok();
    }

}
