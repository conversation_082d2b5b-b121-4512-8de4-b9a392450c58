# XSS注解使用指南

## 🎯 **概述**

现在您可以使用 `@XssClean` 注解来实现自动的XSS防护，不需要手动调用清洗方法。

## 📋 **注解说明**

### @XssClean 注解
- **作用位置**：方法、参数
- **功能**：自动清洗标记的参数或方法返回值
- **级别**：BASIC（基础）、STRICT（严格）、FILENAME（文件名）

## 🛠️ **使用方式**

### 1. 方法级别使用
```java
@PostMapping("/save")
@XssClean  // 自动清洗所有参数
public R save(MenuDO menu) {
    // 注解自动清洗，直接使用对象
    menuService.save(menu);
    return R.ok();
}
```

### 2. 参数级别使用
```java
@GetMapping("/search")
public String search(@XssClean String keyword, 
                    @XssClean(level = XssClean.Level.STRICT) String content,
                    Model model) {
    // 参数已自动清洗，直接使用
    model.addAttribute("keyword", keyword);
    model.addAttribute("content", content);
    return "search/result";
}
```

### 3. HttpServletRequest 自动清洗
```java
@GetMapping("/detailPage")
@XssClean
public String detailPage(@XssClean HttpServletRequest request, Model model) {
    // request.getParameter() 返回的值已自动清洗
    model.addAttribute("queryParameter", request.getParameter("queryParameter"));
    model.addAttribute("orgCode", request.getParameter("orgCode"));
    return "detail";
}
```

### 4. 实体对象自动清洗
```java
@PostMapping("/save")
@XssClean
public R save(@XssClean UserDO user) {
    // user对象的所有字符串字段已自动清洗
    userService.save(user);
    return R.ok();
}
```

### 5. 文件上传清洗
```java
@PostMapping("/upload")
@XssClean
public R upload(@XssClean(level = XssClean.Level.FILENAME) String filename,
               MultipartFile file) {
    // filename 已按文件名规则清洗
    return R.ok();
}
```

## 🔧 **清洗级别说明**

### BASIC（默认）
- 移除 `<script>` 标签
- 移除 `javascript:` 协议
- 移除事件处理器（onclick、onload等）
- 移除基本的SQL注入模式

### STRICT
- 移除所有HTML标签
- HTML实体编码
- 更严格的安全处理

### FILENAME
- 移除路径遍历字符（../）
- 移除特殊字符（<>:"|?*）
- 移除控制字符
- 基础XSS清洗

## 📊 **实际效果对比**

### 使用注解前：
```java
@PostMapping("/save")
public R save(MenuDO menu) {
    // 需要手动清洗
    if (menu.getName() != null) {
        menu.setName(XssUtil.clean(menu.getName()));
    }
    if (menu.getUrl() != null) {
        menu.setUrl(XssUtil.clean(menu.getUrl()));
    }
    if (menu.getPerms() != null) {
        menu.setPerms(XssUtil.clean(menu.getPerms()));
    }
    
    menuService.save(menu);
    return R.ok();
}
```

### 使用注解后：
```java
@PostMapping("/save")
@XssClean  // 一个注解搞定
public R save(@XssClean MenuDO menu) {
    // 直接使用，已自动清洗
    menuService.save(menu);
    return R.ok();
}
```

## 🎯 **最佳实践**

### 1. 控制器方法
```java
@Controller
@RequestMapping("/example")
public class ExampleController {
    
    @GetMapping("/edit")
    @XssClean
    public String edit(@XssClean HttpServletRequest request, Model model) {
        // 自动清洗所有请求参数
        return "edit";
    }
    
    @PostMapping("/save")
    @XssClean
    public R save(@XssClean ExampleDO example) {
        // 自动清洗实体对象
        exampleService.save(example);
        return R.ok();
    }
}
```

### 2. 文件处理
```java
@PostMapping("/upload")
@XssClean
public R upload(@XssClean(level = XssClean.Level.FILENAME) String filename,
               @XssClean MultipartFile file) {
    // 文件名和其他参数都已清洗
    return R.ok();
}
```

### 3. API接口
```java
@RestController
@RequestMapping("/api")
public class ApiController {
    
    @PostMapping("/data")
    @XssClean
    public ApiResponse saveData(@XssClean @RequestBody DataDTO data) {
        // JSON数据自动清洗
        return ApiResponse.success();
    }
}
```

## ✅ **优势**

1. **简单易用**：一个注解解决XSS防护
2. **自动化**：无需手动调用清洗方法
3. **灵活配置**：支持不同的清洗级别
4. **全面覆盖**：支持各种参数类型
5. **性能优化**：只在需要时进行清洗

## 🚀 **迁移建议**

1. **新代码**：直接使用 `@XssClean` 注解
2. **旧代码**：逐步替换手动清洗为注解方式
3. **测试验证**：确保清洗效果符合预期

## 📝 **注意事项**

1. 注解会自动处理字符串类型的参数和字段
2. 对于复杂对象，会递归清洗字符串字段
3. HttpServletRequest会被包装，getParameter()返回清洗后的值
4. 不会影响非字符串类型的数据

---

**现在您可以享受简洁的注解式XSS防护了！** 🎉
