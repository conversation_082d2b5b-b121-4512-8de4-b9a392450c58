package cn.com.xmmn.businesses.dt.api;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 香港成交通知单请求DTO
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-31
 */
@Data
public class HkNoticeAwordRequestDT {

    /**
     * 年月字符串，格式：yyyy-MM
     */
    @NotBlank(message = "年月参数不能为空")
    @Pattern(regexp = "\\d{4}-\\d{2}", message = "年月格式错误，应为yyyy-MM格式")
    private String yearMonthStr;
}
