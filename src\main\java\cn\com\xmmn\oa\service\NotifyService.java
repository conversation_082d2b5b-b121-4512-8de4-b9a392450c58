package cn.com.xmmn.oa.service;

import java.util.List;
import java.util.Map;

import cn.com.xmmn.common.utils.PageUtils;
import cn.com.xmmn.oa.domain.NotifyDO;

/**
 * 通知通告
 */
public interface NotifyService {

	NotifyDO get(Long id);

	List<NotifyDO> list(Map<String, Object> map);

	int count(Map<String, Object> map);

	int save(NotifyDO notify);

	int update(NotifyDO notify);

	int remove(Long id);

	int batchRemove(Long[] ids);

//	Map<String, Object> message(Long userId);

	PageUtils selfList(Map<String, Object> map);
}
