package cn.com.xmmn.businesses.dao;

import java.util.List;
import java.util.Map;

import cn.com.xmmn.businesses.domain.ImportClearanceEfficiencyDO;
import cn.com.xmmn.businesses.domain.ImportMailCargoDO;
import cn.com.xmmn.businesses.domain.ImportMailDO;
import cn.com.xmmn.businesses.domain.ImportMailReceiptDO;
import cn.com.xmmn.businesses.vo.ImportFullTimeListVO;
import cn.com.xmmn.businesses.vo.ImportMailShouldListVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 进口邮件表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-23 17:32:54
 */
@Mapper
public interface ImportMailDao extends BaseMapper<ImportMailDO> {

	ImportMailDO get(String itemId);
	
	List<ImportMailCargoDO> list(Map<String,Object> map);
	
	int count(Map<String,Object> map);
	
	int save(ImportMailDO importMail);
	
	int update(ImportMailDO importMail);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);

	List<ImportMailReceiptDO> ReceiptList(Map<String, Object> map);

	int ReceiptCount(Map<String,Object> map);


    List<ImportMailShouldListVO> getShouldList(Map<String, Object> map);

	int getShouldCount(Map<String, Object> map);


	/**
	 * 全程时限列表
	 * @param params
	 * @return
	 */
    List<ImportFullTimeListVO> fullTimeList(Map<String, Object> params);

	/**
	 * 全程时限 总数
	 * @param params
	 * @return
	 */
	Integer fullTimeCount(Map<String, Object> params);
}
