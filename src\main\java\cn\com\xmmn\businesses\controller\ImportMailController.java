package cn.com.xmmn.businesses.controller;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.com.xmmn.businesses.domain.ImportMailCargoDO;
import cn.com.xmmn.businesses.domain.ImportMailDO;
import cn.com.xmmn.businesses.domain.ImportMailReceiptDO;
import cn.com.xmmn.businesses.service.ImportMailService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import cn.com.xmmn.common.utils.PageUtils;
import cn.com.xmmn.common.utils.Query;
import cn.com.xmmn.common.utils.R;

/**
 * 进口邮件表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-23 17:32:54
 */
 
@Controller
@RequestMapping("/importMail/importMail")
public class ImportMailController {
	@Autowired
	private ImportMailService importMailService;
	
	@GetMapping()
	@RequiresPermissions("importMail:importMail:importMail")
	String ImportMail(){
	    return "importMail/importMail/importMail";
	}
	
	@ResponseBody
	@GetMapping("/list")
	@RequiresPermissions("importMail:importMail:importMail")
	public Map<String, Object> list(@RequestParam Map<String, Object> params){
        Object offset = params.get("offset");
        //查询列表数据
        Query query = new Query(params);
		String itemId = String.valueOf(query.get("itemId"));

       //查询邮件信息
		ImportMailDO importMailDO = importMailService.get(itemId);
		//内件信息
		List<ImportMailCargoDO> importMailCarGoList = importMailService.list(query);
		int count = importMailService.count(query);
		//回执信息
		List<ImportMailReceiptDO> importMailReceiptList = importMailService.ReceiptList(query);
		int ReceiptCount = importMailService.ReceiptCount(query);
		PageUtils importMailCarGoLists = new PageUtils(importMailCarGoList,count);
		PageUtils importMailReceiptLists = new PageUtils(importMailReceiptList,ReceiptCount);

		Map<String, Object> data = new HashMap<>();
		data.put("importMailDO",importMailDO);
		data.put("importMailCarGoList",importMailCarGoLists);
		data.put("importMailReceiptList",importMailReceiptLists);
		return data;
	}


	
}
