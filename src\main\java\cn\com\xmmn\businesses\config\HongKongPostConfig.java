package cn.com.xmmn.businesses.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 香港邮政系统配置类
 * 支持多种业务模块的配置管理
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-31
 */
@Data
@Component
@ConfigurationProperties(prefix = "hongkong-post")
public class HongKongPostConfig {

    /**
     * 香港邮政API基础地址
     */
    private String baseUrl;

    /**
     * 通用加密密钥
     */
    private String secretKey;

    /**
     * 请求超时时间（毫秒）
     */
    private Integer timeout = 30000;

    /**
     * 是否启用SSL验证
     */
    private Boolean sslEnabled = true;

    /**
     * 业务模块配置
     */
    private Business business = new Business();

    @Data
    public static class Business {
        /**
         * 成交运能通知单相关配置
         */
        private CapacityNotice capacityNotice = new CapacityNotice();

        // 可以继续添加其他业务模块
        // private WaybillQuery waybillQuery = new WaybillQuery();
        // private FeeCalculation feeCalculation = new FeeCalculation();
    }

    @Data
    public static class CapacityNotice {
        /**
         * 成交运能通知单API路径
         */
        private String apiPath = "/api/requestBill";

        /**
         * 业务专用密钥（如果有的话）
         */
        private String businessKey;
    }
}
