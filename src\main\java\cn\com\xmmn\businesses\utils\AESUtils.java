package cn.com.xmmn.businesses.utils;


import com.alibaba.fastjson.JSON;
import org.apache.commons.lang.StringUtils;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;
import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.*;

/**
 *  加密解密类
 */
public class AESUtils {

    private static final String AES = "AES";
    private static final String ENCRYPTION_KEY = "chinacourierhkchinacourierhk"; // 32字节密钥

    /**
     * AES加密
     */
    public static String encryptToBytes(String content) throws Exception {
        Cipher cipher = getCipher(Cipher.ENCRYPT_MODE);
        byte[] bytes = cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
        return base64Encode(bytes);
    }

    /**
     * AES解密
     */
    public static String decryptByBytes(String encryptString) throws Exception {
        byte[] encryptBytes = base64Decode(encryptString);
        Cipher cipher = getCipher(Cipher.DECRYPT_MODE);
        byte[] decryptBytes = cipher.doFinal(encryptBytes);
        return new String(decryptBytes, StandardCharsets.UTF_8);
    }


    /**
     * 加密-可以输入加密秘钥
     * @param content  需要加密的内容
     * @param inputKey 秘钥
     * @return
     * @throws Exception
     */
    public static String encryptToBytes(String content,String inputKey) throws Exception {
        Cipher cipher = getCipherKey(Cipher.ENCRYPT_MODE,inputKey);
        byte[] bytes = cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
        return base64Encode(bytes);
    }

    /**
     * AES解密
     */
    /**
     *  AES解密
     * @param encryptString 需要解密的加密内容
     * @param inputKey 秘钥
     * @return
     * @throws Exception
     */
    public static String decryptByBytes(String encryptString,String inputKey) throws Exception {
        byte[] encryptBytes = base64Decode(encryptString);
        Cipher cipher = getCipherKey(Cipher.DECRYPT_MODE,inputKey);
        byte[] decryptBytes = cipher.doFinal(encryptBytes);
        return new String(decryptBytes, StandardCharsets.UTF_8);
    }


    private static Cipher getCipher(int mode) throws Exception {
        byte[] keyBytes = getKeyBytes(ENCRYPTION_KEY);
        SecretKeySpec secretKey = new SecretKeySpec(keyBytes, AES);
        Cipher cipher = Cipher.getInstance(AES);
        cipher.init(mode, secretKey);
        return cipher;
    }

     private static Cipher getCipherKey(int mode,String key) throws Exception {
        byte[] keyBytes = getKeyBytes(key);
        SecretKeySpec secretKey = new SecretKeySpec(keyBytes, AES);
        Cipher cipher = Cipher.getInstance(AES);
        cipher.init(mode, secretKey);
        return cipher;
    }

    private static byte[] getKeyBytes(String key) throws NoSuchAlgorithmException {
        byte[] keyBytes = new byte[16]; // 128位密钥长度
        byte[] parameterKeyBytes = key.getBytes(StandardCharsets.UTF_8);

        // 使用前16个字节，如果不足则补0
        System.arraycopy(parameterKeyBytes, 0, keyBytes, 0, Math.min(parameterKeyBytes.length, keyBytes.length));
        return keyBytes;
    }

    private static String base64Encode(byte[] bytes) {
        return Base64.getEncoder().encodeToString(bytes);
    }

    private static byte[] base64Decode(String base64Code) {
        if (base64Code == null || base64Code.isEmpty()) {
            throw new IllegalArgumentException("Base64 code is null or empty");
        }
        return Base64.getDecoder().decode(base64Code);
    }

}


