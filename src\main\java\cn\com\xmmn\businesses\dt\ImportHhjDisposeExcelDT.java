package cn.com.xmmn.businesses.dt;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 导出进口互换局时限统计表的DT
 * zjy
 */
@HeadRowHeight(value = 30) // 头部行高
@ContentRowHeight(value = 25) // 内容行高
@ColumnWidth(value = 20) // 列宽
@HeadFontStyle(fontName = "宋体", fontHeightInPoints = 11) // 头部字体样式
@Data
public class ImportHhjDisposeExcelDT implements Serializable {

    private static final long serialVersionUID = 1L;
    //交换站省份名称
    @ExcelProperty("省份")
    private String provinceName;

    //互换局名称
    @ExcelProperty("互换局")
    private String orgName;

    //互换局代码
    @ExcelProperty("机构代码")
    private String orgNo;

    //接收量
    @ExcelProperty("接收量")
    private BigDecimal jsSum;
    //未接收量
    @ExcelProperty("未接收量")
    private BigDecimal wjsSum;


    //应处理量
    @ExcelProperty("应处理量")
    private BigDecimal yclSum;

    //开拆量
    @ExcelProperty("开拆量")
    private BigDecimal kcSum;
    //开拆率
    @ExcelProperty("开拆率")
    private BigDecimal kcRate;

    @ExcelProperty("逾期开拆量")
    private BigDecimal rqKcSum;


    //开拆平均时长
    @ExcelProperty("平均时长(h)")
    private BigDecimal kcPjsc;
    //未开拆量
    @ExcelProperty("未开拆量")
    private BigDecimal wkcSum;

    //通关量
    @ExcelProperty("通关量")
    private BigDecimal tgSum;
    //通关率
    @ExcelProperty("通关率")
    private BigDecimal tgRate;
    //通关平均时长
    @ExcelProperty("通关平均时长")
    private BigDecimal tgPjsc;

    //待通关量
    @ExcelProperty("待通关量")
    private BigDecimal dtgSum;
    //未缴税量
    @ExcelProperty("未缴税量")
    private BigDecimal wjnsSum;

    //补充申报量
    @ExcelProperty("补充申报量")
    private BigDecimal bcsbSum;
    //未申报量
    @ExcelProperty("未申报量")
    private BigDecimal wsbSum;
    //已封发量
    @ExcelProperty("已封发量")
    private BigDecimal yffSum;
    //封发率
    @ExcelProperty("封发率")
    private BigDecimal ffRate;
    //逾期封发量
    @ExcelProperty("逾期封发量")
    private BigDecimal yqFfSum;
    //封发平均时长
    @ExcelProperty("平均时长(h)")
    private BigDecimal ffPjsc;
    //未封发量
    @ExcelProperty("未封发量")
    private BigDecimal wffSum;
    //已发运量
    @ExcelProperty("已发运量")
    private BigDecimal fySum;
    //发运率
    @ExcelProperty("发运率")
    private BigDecimal fyRate;
    //逾期发运量
    @ExcelProperty("逾期发运量")
    private BigDecimal rqFySum;
    //发运平均时长
    @ExcelProperty("平均时长(h)")
    private BigDecimal fyPjsc;
    //未发运量
    @ExcelProperty("未发运量")
    private BigDecimal wfySum;
    //时限内
    @ExcelProperty("时限内")
    private BigDecimal sxnSum;
    //时限内发运率
    @ExcelProperty("及时率")
    private BigDecimal sxnRate;
    //逾期时限内量
    @ExcelProperty("逾期邮件量")
    private BigDecimal rqSxnSum;
    //时限内平均时长
    @ExcelProperty("平均时长(h)")
    private BigDecimal sxnPjsc;
    //封发量
    @ExcelProperty("封发量")
    private BigDecimal ttffSum;

    //发运量
    @ExcelProperty("发运量")
    private BigDecimal ttfySum;
    //发运率
    @ExcelProperty("发运率")
    private BigDecimal ttfyRate;
    //未发运量
    @ExcelProperty("未发运量")
    private BigDecimal wttfySum;
    //下一环节接收量
    @ExcelProperty("下一环节接收量")
    private BigDecimal nextSum;
    //下一环节未接收量
    @ExcelProperty("下一环节未接收量")
    private BigDecimal nextNoSum;
    //已投递量
    @ExcelProperty("已投递量")
    private BigDecimal ttSum;
    //未投递量
    @ExcelProperty("未投递量")
    private BigDecimal wttSum;
    //投递率
    @ExcelProperty("投递率")
    private BigDecimal ttRate;
}
