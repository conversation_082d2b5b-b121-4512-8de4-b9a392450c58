package cn.com.xmmn.businesses.controller.api;

import cn.com.xmmn.businesses.dt.api.HkNoticeAwordRequestDT;
import cn.com.xmmn.businesses.service.HkNoticeAwordService;
import cn.com.xmmn.common.utils.ApiResponseData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 香港成交通知单API接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-31
 */
@Slf4j
@RestController
@RequestMapping("/api/manual")
@RequiredArgsConstructor
public class HkNoticeAwordController {

    private final HkNoticeAwordService hkNoticeAwordService;

    /**
     * 手动调用第三方接口获取成交通知单数据
     * 
     * @param request 请求参数，包含yearMonthStr（格式：yyyy-MM）
     * @return 处理结果
     */
    @PostMapping("/requestBill")
    public ApiResponseData<String> requestBill(@Validated @RequestBody HkNoticeAwordRequestDT request) {
        try {
            log.info("收到手动调用请求，参数：{}", request.getYearMonthStr());
            
            String result = hkNoticeAwordService.manualRequestBill(request.getYearMonthStr());
            
            log.info("手动调用处理完成，结果：{}", result);
            return ApiResponseData.ok(result);
            
        } catch (Exception e) {
            log.error("手动调用处理失败，参数：{}，错误：{}", request.getYearMonthStr(), e.getMessage(), e);
            return ApiResponseData.error(500, "处理失败: " + e.getMessage());
        }
    }
}
