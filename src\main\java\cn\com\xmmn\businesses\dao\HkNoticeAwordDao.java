package cn.com.xmmn.businesses.dao;

import cn.com.xmmn.businesses.domain.HkNoticeAwordDO;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

/**
 * 香港成交通知单接收表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-31
 */
@Mapper
public interface HkNoticeAwordDao {

	HkNoticeAwordDO get(Integer id);
	
	List<HkNoticeAwordDO> list(Map<String,Object> map);
	
	int count(Map<String,Object> map);
	
	int save(HkNoticeAwordDO hkNoticeAword);
	
	int update(HkNoticeAwordDO hkNoticeAword);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);
	
	/**
	 * 根据运能编码和生效日期查询是否存在重复数据
	 * @param capacityCode 运能编码
	 * @param effectiveDate 生效日期
	 * @return 数量
	 */
	int countByCapacityCodeAndDate(String capacityCode, String effectiveDate);
	
	/**
	 * 批量保存成交通知单数据
	 * @param list 数据列表
	 * @return 影响行数
	 */
	int batchSave(List<HkNoticeAwordDO> list);
}
