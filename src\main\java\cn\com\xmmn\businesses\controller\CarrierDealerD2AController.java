package cn.com.xmmn.businesses.controller;

import cn.com.xmmn.businesses.domain.CarrierDealerRecivDO;
import cn.com.xmmn.businesses.dt.CarrierDealerRecivDetailDT;
import cn.com.xmmn.businesses.service.CarrierDealerD2AService;
import cn.com.xmmn.businesses.service.CarrierDealerDepartService;
import cn.com.xmmn.businesses.vo.*;
import cn.com.xmmn.common.controller.BaseController;
import cn.com.xmmn.common.utils.PageUtils;
import cn.com.xmmn.common.utils.Query;
import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 承运商启运至运抵时限情况
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024年6月12日09:43:11
 */
@Slf4j
@Controller
@RequestMapping("/carrierDealerD2A/monitor")
public class CarrierDealerD2AController extends BaseController {

    @Autowired
    private CarrierDealerD2AService carrierDealerD2AService;

    @GetMapping()
    @RequiresPermissions("carrierDealerD2A:carrierDealerD2A")
    String carrierDealerD2A() {
        return "carrierDealerD2A/list";
    }



    /**
     * 分组数据
     * @param params
     * @return
     */
    @ResponseBody
    @GetMapping("/group")
    @RequiresPermissions("carrierDealerD2A:carrierDealerD2A")
    public PageUtils group(@RequestParam Map<String, Object> params) {
        log.info("承运商启运至运抵时限情况参数:{}" , params);
        //查询列表数据
        Query query = new Query(params);
        List<CarrierDealerD2AListVo> carrierDealerDepartListVos = carrierDealerD2AService.group(query);
        List<CarrierDealerD2AListVo> carrierDealerDepartCountListVos = carrierDealerD2AService.groupCount(query);
        Integer total=carrierDealerDepartCountListVos.get(0).getTotal();
        PageUtils pageUtils = new PageUtils(carrierDealerDepartListVos, total);
        pageUtils.setDepartNum(carrierDealerDepartCountListVos.get(0).getDepartNum());
        pageUtils.setInTransitMailbagNum(carrierDealerDepartCountListVos.get(0).getInTransitMailbagNum());
        pageUtils.setArrivedMailbagNum(carrierDealerDepartCountListVos.get(0).getArrivedMailbagNum());
        pageUtils.setExceptionBagNum(carrierDealerDepartCountListVos.get(0).getExceptionBagNum());

        return pageUtils;
    }

    /**
     * 跳转详情页列表
     * @return
     */
    @GetMapping("/detailPage")
    @RequiresPermissions("carrierDealerD2A:carrierDealerD2A")
    public String detailPage(HttpServletRequest request, Model model) {
        //点击的是哪个明细。
        String queryParameter = request.getParameter("queryParameter");
        model.addAttribute("queryParameter", queryParameter);

        String orgCode = request.getParameter("orgCode");
        String orgCodeStr = request.getParameter("orgCodeStr");
        String carrierCode = request.getParameter("carrierCode");
        String productCode = request.getParameter("productCode");
        String receiverCountryCode = request.getParameter("receiverCountryCode");
        String receiverCountry = request.getParameter("receiverCountry");
        String oeDest = request.getParameter("oeDest");
        //分组搜索的字段
        String opTime538Start = request.getParameter("opTime538Start");
        String opTime538End = request.getParameter("opTime538End");
        String provinceCodeStr = request.getParameter("provinceCodeStr");

        model.addAttribute("provinceCodeStr", provinceCodeStr);
        model.addAttribute("orgCodeStr", orgCodeStr);
        model.addAttribute("opTime538Start", opTime538Start);
        model.addAttribute("opTime538End", opTime538End);
        model.addAttribute("orgCode", orgCode);
        model.addAttribute("carrierCode", carrierCode);
        model.addAttribute("productCode", productCode);
        model.addAttribute("oeDest", oeDest);
        model.addAttribute("receiverCountryCode", receiverCountryCode);
        model.addAttribute("receiverCountry", receiverCountry);
        return "carrierDealerD2A/details";
    }

    @ResponseBody
    @GetMapping("/detailTable")
    @RequiresPermissions("carrierDealerD2A:carrierDealerD2A")
    public PageUtils detailTable(CarrierDealerRecivDetailDT dealerReceiveDetailDT) {

        List<CarrierDealerRecivDO> carrierDealerRecivList = carrierDealerD2AService.detail(dealerReceiveDetailDT);
        Integer total = carrierDealerD2AService.detailCount(dealerReceiveDetailDT);
        PageUtils pageUtils = new PageUtils(carrierDealerRecivList, total);
        return pageUtils;

    }


    /**
     * 导出数据
     *
     * @param map
     * @param response 入参
     * @return void
     **/
    @RequestMapping(value = "/exportExcel", method = RequestMethod.GET)
    public void exportExcel(@RequestParam Map<String, Object> map, HttpServletResponse response) throws IOException {

        List<CarrierDealerD2AListVo> carrierDealerDepartListVos = carrierDealerD2AService.group(map);
        List<CarrierDealerD2AListVo> carrierDealerDepartCountListVos = carrierDealerD2AService.groupCount(map);


        //需要计算启运至运抵时限（天）,总包当前状态

        //导出
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");
        // 文件名
        String fileName = map.get("dateStart") + "承运商接收情况统计表" + ".xlsx";
        try {
            CarrierDealerD2AListVo newRecord = new CarrierDealerD2AListVo();
            newRecord.setCarrierDealer("合计");
            newRecord.setDepartNum(carrierDealerDepartCountListVos.get(0).getDepartNum());
            newRecord.setInTransitMailbagNum(carrierDealerDepartCountListVos.get(0).getInTransitMailbagNum());
            newRecord.setArrivedMailbagNum(carrierDealerDepartCountListVos.get(0).getArrivedMailbagNum());
            newRecord.setExceptionBagNum(carrierDealerDepartCountListVos.get(0).getExceptionBagNum());

            carrierDealerDepartListVos.add(newRecord);


            fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
        try {
            //导出
            EasyExcel.write(response.getOutputStream(), CarrierDealerD2AListVo.class).sheet("承运商接收情况统计表").doWrite(carrierDealerDepartListVos);
        } catch (Exception e) {
            log.info("excel文档导出错误-异常信息：" + e);
            e.printStackTrace();
        }

    }


    @RequestMapping(value = "/exportDetailExcel",method = RequestMethod.GET)
    public void exportDetailExcel(CarrierDealerRecivDetailDT dealerReceiveDetailDT, HttpServletResponse response) throws IOException {
        List<CarrierDealerRecivDO> portbatchList = carrierDealerD2AService.detail(dealerReceiveDetailDT);


        //导出
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");
        // 文件名
        String fileName =   "承运商启运至运抵时限明细" + ".xlsx";
        try {
            fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");


        try {
            List<CarrierDealerD2ADetailListVo> exportList = new ArrayList<>();
            for (CarrierDealerRecivDO portbatch : portbatchList) {
                CarrierDealerD2ADetailListVo vo = new CarrierDealerD2ADetailListVo();
                BeanUtils.copyProperties(portbatch, vo);
                exportList.add(vo);
            }
            //导出
            EasyExcel.write(response.getOutputStream(), CarrierDealerD2ADetailListVo.class)
                    //.registerWriteHandler(customHeaderHandler)
                    .sheet("承运商启运至运抵时限明细")
                    .doWrite(exportList);
        } catch (Exception e) {
            log.info("excel文档导出错误-异常信息：" + e);
            e.printStackTrace();
        }
    }
}
