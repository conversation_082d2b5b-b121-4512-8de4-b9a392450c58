package cn.com.xmmn.businesses.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix="interface")
public class InterfaceConfig {
	//上传路径
	private String tmsPath;

	private String deliveryPath;

	private String importPath;

	private String hkNoticePath;

}
