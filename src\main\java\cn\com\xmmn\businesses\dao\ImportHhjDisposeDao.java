package cn.com.xmmn.businesses.dao;

import cn.com.xmmn.businesses.domain.ImportHhjDisposeDO;
import cn.com.xmmn.businesses.vo.ImportFullTimeListVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 进口时限统计表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-20 15:02:00
 */
@Mapper
public interface ImportHhjDisposeDao extends BaseMapper<ImportHhjDisposeDO> {

    /**
     *
     * @param params
     * @return
     */
    List<ImportHhjDisposeDO> list(Map<String, Object> params);
    /**
     * 总数
     * @param params
     * @return
     */
    Integer count(Map<String, Object> params);
}
