package cn.com.xmmn.businesses.strategy;

import cn.com.xmmn.businesses.service.ImportMailService;
import cn.com.xmmn.businesses.vo.ImportMailShouldListVO;
import cn.com.xmmn.businesses.vo.KcffMaillExportVO;
import cn.com.xmmn.businesses.vo.YclMaillExportVO;
import cn.com.xmmn.common.enums.ImportHhjDisposeExportNameEnum;
import cn.hutool.core.convert.Convert;
import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
/**
 * (邮件)开拆-封发导出
 */
@Component
@Slf4j
public class KcffImportHhjDisposeExportStrategy extends AbstractImportHhjDisposeExportStrategy{

    @Autowired
    private ImportMailService importMailService;

    @Override
    public void exportExcel(Map<String, Object> map,HttpServletResponse response) {

        // 获取当前线程的 HttpServletResponse
       // HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getResponse();

        List<ImportMailShouldListVO> list = importMailService.getShouldList(map);

        // 转换为导出的 VO 类型
        List<KcffMaillExportVO> mailExportVOS = convertToExportVO(list);

        // 设置导出响应
        setupResponseHeaders(response, map, mailExportVOS);

        try {
            // 导出
            EasyExcel.write(response.getOutputStream(), getExportVOClass())
                    .sheet(getSheetName(map))
                    .doWrite(mailExportVOS);
        } catch (Exception e) {
            log.error("Excel导出失败，异常信息：", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);  // 返回500错误
        }
    }

    @Override
    protected List<KcffMaillExportVO> convertToExportVO(List<ImportMailShouldListVO> list) {
        List<KcffMaillExportVO> exportVOS = new ArrayList<>();
        for (ImportMailShouldListVO mailShouldListVO : list) {
            KcffMaillExportVO exportVO = new KcffMaillExportVO();
            // 使用 BeanUtils 复制属性
            BeanUtils.copyProperties(mailShouldListVO, exportVO);
            exportVOS.add(exportVO);
        }
        return exportVOS;
    }

    @Override
    protected Class<?> getExportVOClass() {
        return KcffMaillExportVO.class;
    }

    @Override
    protected String getSheetName(Map<String, Object> map) {
        String pageHandled = Convert.toStr(map.get("pageHandled"));
        return ImportHhjDisposeExportNameEnum.getName(pageHandled);
    }
}
