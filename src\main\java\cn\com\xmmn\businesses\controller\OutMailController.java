package cn.com.xmmn.businesses.controller;

import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import cn.com.xmmn.businesses.domain.CarrierBillDetailDO;
import cn.com.xmmn.businesses.domain.CarrierDealerRecivDO;
import cn.com.xmmn.businesses.domain.OutMailDO;
import cn.com.xmmn.businesses.dt.CarrierDealerRecivDetailDT;
import cn.com.xmmn.businesses.dt.OutMailDT;
import cn.com.xmmn.businesses.service.OutMailService;
import cn.com.xmmn.businesses.vo.CarrierDealerArriveToPostDetailListVo;
import cn.com.xmmn.businesses.vo.OutMailDetailListVo;
import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;


import cn.com.xmmn.common.utils.PageUtils;
import cn.com.xmmn.common.utils.Query;
import cn.com.xmmn.common.utils.R;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 出口邮件表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-13 14:08:34
 */
@Slf4j
@Controller
@RequestMapping("/outMail/outMail")
public class OutMailController {
	@Autowired
	private OutMailService outMailService;
	
	@GetMapping()
	@RequiresPermissions("outMail:outMail:outMail")
	String OutMail(){
	    return "outMail/outMail/outMail";
	}
	
	@ResponseBody
	@GetMapping("/list")
	@RequiresPermissions("outMail:outMail:outMail")
	public PageUtils list(@RequestParam Map<String, Object> params){
		//查询列表数据
        Query query = new Query(params);
		List<OutMailDO> outMailList = outMailService.list(query);
		List<OutMailDO> outMailHjList = outMailService.hjList(query);
		Integer total=outMailHjList.get(0).getTotal();
		PageUtils pageUtils = new PageUtils(outMailList, total);
		pageUtils.setYff_hj(outMailHjList.get(0).getFenFaNum());
		pageUtils.setYjh_hj(outMailHjList.get(0).getDevNum());
		pageUtils.setSc_hj(outMailHjList.get(0).getFenFaJiaoHang());
		return pageUtils;
	}
	/**
	 * 跳转详情页列表
	 * @return
	 */
	@GetMapping("/detailPage")
	@RequiresPermissions("outMail:outMail:outMail")
	public String detailPage(HttpServletRequest request, Model model) {
		//点击的是哪个明细。
		String queryParameter = request.getParameter("queryParameter");
		model.addAttribute("queryParameter", queryParameter);

		String orgCode = request.getParameter("orgCode");
		String orgCodeStr = request.getParameter("orgCodeStr");
		String oeDest = request.getParameter("oeDest");
		String opOrgName = request.getParameter("opOrgName");
		String receiverCountryCode = request.getParameter("receiverCountryCode");

		String opTime404Start = request.getParameter("opTime404Start");
		String opTime404End = request.getParameter("opTime404End");
		String cltProvinceCodeStr = request.getParameter("cltProvinceCodeStr");

		model.addAttribute("cltProvinceCodeStr", cltProvinceCodeStr);
		model.addAttribute("opTime404Start", opTime404Start);
		model.addAttribute("opTime404End", opTime404End);
		model.addAttribute("orgCode", orgCode);
		model.addAttribute("orgCodeStr", orgCodeStr);
		model.addAttribute("oeDest", oeDest);
		model.addAttribute("opOrgName", opOrgName);
		model.addAttribute("receiverCountryCode", receiverCountryCode);

		return "outMail/outMail/details";
	}

	@ResponseBody
	@GetMapping("/detailTable")
	@RequiresPermissions("outMail:outMail:outMail")
	public PageUtils detailTable(OutMailDT outMailDT) {

		List<OutMailDO> detail = outMailService.detail(outMailDT);

		Integer total = outMailService.detailCount(outMailDT);
		PageUtils pageUtils = new PageUtils(detail, total);
		return pageUtils;

	}
	@RequestMapping(value = "/exportExcel",method = RequestMethod.GET)
	public void exportExcel(@RequestParam Map<String, Object> map, HttpServletResponse response) throws IOException {
		List<OutMailDO> portbatchList = outMailService.list(map);
		List<OutMailDO> portbatchHjList = outMailService.hjList(map);
		//创建HSSFWorkbook对象,  excel的文档对象
		HSSFWorkbook wb = new HSSFWorkbook();
		// 创建sheet
		Sheet sheet = wb.createSheet("封发时间至交航时限统计表");
		//表头字体
		Font headerFont = wb.createFont();
		headerFont.setFontName("宋体");
		headerFont.setFontHeightInPoints((short) 18);
		headerFont.setBold(true);
		headerFont.setColor(Font.COLOR_NORMAL);
		//正文字体
		Font contextFont = wb.createFont();
		contextFont.setFontName("宋体");
		contextFont.setFontHeightInPoints((short) 12);
		headerFont.setBold(true);
		//表头样式，左右上下居中
		CellStyle headerStyle = wb.createCellStyle();
		headerStyle.setFont(headerFont);
		// 左右居中
		headerStyle.setAlignment(HorizontalAlignment.CENTER);
		// 上下居中
		headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		headerStyle.setLocked(true);
		// 自动换行
		headerStyle.setWrapText(false);
		//单元格样式，右对齐
		CellStyle alignRightStyle = wb.createCellStyle();
		alignRightStyle.setFont(contextFont);
		alignRightStyle.setLocked(true);
		// 自动换行
		alignRightStyle.setWrapText(false);
		alignRightStyle.setAlignment(HorizontalAlignment.RIGHT);
		//单元格样式，左右上下居中 边框
		CellStyle commonStyle = wb.createCellStyle();
		commonStyle.setFont(contextFont);
		// 左右居中
		commonStyle.setAlignment(HorizontalAlignment.CENTER);
		// 上下居中
		commonStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		commonStyle.setLocked(true);
		// 自动换行
		commonStyle.setWrapText(false);
		// 行号
		int rowNum = 0;
		//设置列宽
		for (int i = 0; i < 8; i++) {
			sheet.setColumnWidth(i, 6000);
		}
		//第一行中国快递服务有限公司核账单
		Row r0 = sheet.createRow(rowNum++);
		r0.setHeight((short) 1000);
		Cell c0 = r0.createCell(0);
		c0.setCellValue("封发时间至交航时限统计表");
		c0.setCellStyle(headerStyle);
		//合并单元格
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 6));
		//第二行 结算周期
		Row r1 = sheet.createRow(rowNum++);
		r1.setHeight((short) 500);
		Cell c1 = r1.createCell(0);
		c1.setCellValue("统计时间：" + map.get("opTime404Start")+"-"+map.get("opTime404End"));
		c1.setCellStyle(alignRightStyle);
		//合并单元格
		sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 6));
		Row r2 = sheet.createRow(rowNum++);
		r2.setHeight((short) 500);
		String[] rowSecond = {"互换局", "交换站", "寄达国家/地区", "寄达互换局名称", "已封发袋数", "已交航袋数","封发-交航"};

		for (int i = 0; i < rowSecond.length; i++) {
			Cell tempCell = r2.createCell(i);
			tempCell.setCellValue(rowSecond[i]);
			tempCell.setCellStyle(commonStyle);
			tempCell.setCellStyle(commonStyle);
		}
		for (int i = 0; i < portbatchList.size(); i++) {
			OutMailDO vo= portbatchList.get(i);
			Row tempRow = sheet.createRow(rowNum++);
			tempRow.setRowStyle(commonStyle);
			tempRow.createCell(0).setCellValue(vo.getOpOrgName()!= null ? String.valueOf(vo.getOpOrgName()) : "");//互换局
			tempRow.createCell(1).setCellValue(vo.getOrgName().toString()!= null ? String.valueOf(vo.getOrgName()) : "");//交换站
			tempRow.createCell(2).setCellValue(vo.getReceiverCountryName().toString()!= null ? String.valueOf(vo.getReceiverCountryName()) : "");//寄达国家/地区
			tempRow.createCell(3).setCellValue(vo.getOeDestName().toString()!= null ? String.valueOf(vo.getOeDestName()) : "");//寄达互换局
			tempRow.createCell(4).setCellValue(vo.getFenFaNum()!= null ? String.valueOf(vo.getFenFaNum()) : "");//已封发袋数
			tempRow.createCell(5).setCellValue(vo.getDevNum()!= null ? String.valueOf(vo.getDevNum()) : "");//已交航袋数
			tempRow.createCell(6).setCellValue(vo.getFenFaJiaoHang()!= null ? String.valueOf(vo.getFenFaJiaoHang()) : "");//封发-交航

		}
		//合计
		OutMailDO voHj= portbatchHjList.get(0);
		Row tempRow = sheet.createRow(rowNum++);
		tempRow.setRowStyle(commonStyle);
		tempRow.createCell(0).setCellValue("合计");//互换局
		tempRow.createCell(1).setCellValue("");//交换站
		tempRow.createCell(2).setCellValue("");//寄达国家/地区
		tempRow.createCell(3).setCellValue("");//寄达互换局
		tempRow.createCell(4).setCellValue(voHj.getFenFaNum()!= null ? String.valueOf(voHj.getFenFaNum()) : "");//已封发袋数
		tempRow.createCell(5).setCellValue(voHj.getDevNum()!= null ? String.valueOf(voHj.getDevNum()) : "");//已交航袋数
		tempRow.createCell(6).setCellValue(voHj.getFenFaJiaoHang()!= null ? String.valueOf(voHj.getFenFaJiaoHang()) : "");//封发-交航
		//导出
		response.setContentType("application/vnd.ms-excel");
		response.setCharacterEncoding("UTF-8");
		// 文件名
		String fileName =  map.get("opTime404Start")+"-"+map.get("opTime404End")  + "封发时间至交航时限统计表" + ".xlsx";
		try {
			fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
		OutputStream stream = null;
		try {
			stream = response.getOutputStream();
		} catch (IOException e) {
			e.printStackTrace();
		}
		try {
			if (null != wb && null != stream) {
				wb.write(stream);
				stream.close();
			}
		} catch (Exception e) {
			log.info("excel文档导出错误-异常信息：" + e);
		}
	}

	@RequestMapping(value = "/exportDetailExcel",method = RequestMethod.GET)
	public void exportDetailExcel(OutMailDT outMailDT, HttpServletResponse response) throws IOException {
		List<OutMailDO> portbatchList = outMailService.detail(outMailDT);


		//导出
		response.setContentType("application/vnd.ms-excel");
		response.setCharacterEncoding("UTF-8");
		// 文件名
		String fileName =   "封发时间至交航时限明细" + ".xlsx";
		try {
			fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}

		response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");


		try {
			List<OutMailDetailListVo> exportList = new ArrayList<>();
			for (OutMailDO portbatch : portbatchList) {
				OutMailDetailListVo vo = new OutMailDetailListVo();
				BeanUtils.copyProperties(portbatch, vo);
				exportList.add(vo);
			}
			//导出
			EasyExcel.write(response.getOutputStream(), OutMailDetailListVo.class)
					//.registerWriteHandler(customHeaderHandler)
					.sheet("封发时间至交航时限明细")
					.doWrite(exportList);
		} catch (Exception e) {
			log.info("excel文档导出错误-异常信息：" + e);
			e.printStackTrace();
		}
	}

}
