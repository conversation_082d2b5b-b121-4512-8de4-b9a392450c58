/**
 * 安全工具类 - 用于防止XSS攻击和其他安全问题
 */
var SecurityUtils = {
    /**
     * 对字符串进行HTML编码，防止XSS攻击
     * @param {string} str 需要编码的字符串
     * @returns {string} 编码后的字符串
     */
    encodeHTML: function(str) {
        if (!str || typeof str !== 'string') return '';
        return str
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#x27;')
            .replace(/\//g, '&#x2F;');
    },
    
    /**
     * 对URL参数进行编码
     * @param {string} param 需要编码的参数
     * @returns {string} 编码后的参数
     */
    encodeURLParam: function(param) {
        if (!param || typeof param !== 'string') return '';
        return encodeURIComponent(param);
    },
    
    /**
     * 安全地构建URL查询字符串
     * @param {string} baseUrl 基础URL
     * @param {Object} params 参数对象
     * @returns {string} 构建好的URL
     */
    buildSafeUrl: function(baseUrl, params) {
        debugger;
        if (!baseUrl) return '';
        
        var url = baseUrl;
        var isFirstParam = url.indexOf('?') === -1;
        
        for (var key in params) {
            if (params.hasOwnProperty(key) && params[key] !== null && params[key] !== undefined) {
                url += (isFirstParam ? '?' : '&');
                url += encodeURIComponent(key) + '=' + encodeURIComponent(params[key]);
                isFirstParam = false;
            }
        }
        
        return url;
    },
    
    /**
     * 验证输入是否为安全的值（不含XSS攻击向量）
     * @param {string} input 需要验证的输入
     * @returns {boolean} 是否安全
     */
    isSafeInput: function(input) {
        if (!input || typeof input !== 'string') return true;
        
        // 检查常见的XSS攻击向量
        var dangerousPatterns = [
            /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
            /javascript:/gi,
            /on\w+\s*=/gi,
            /data:/gi
        ];
        
        for (var i = 0; i < dangerousPatterns.length; i++) {
            if (dangerousPatterns[i].test(input)) {
                return false;
            }
        }
        
        return true;
    }
}; 