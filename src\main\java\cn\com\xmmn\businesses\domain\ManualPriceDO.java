package cn.com.xmmn.businesses.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 导入批次表
 * 
 * <AUTHOR>
 */
@Data
public class ManualPriceDO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//ID;批次号
	private Integer id;
	//乐观锁;数据版本号
	private Integer revision;
	//创建人id
	private Integer createUserId;
	//创建人
	private String createUserName;
	//创建部门id
	private Integer createDeptId;
	//创建部门
	private String createDeptName;
	//创建时间
	private Date    createTime;
	//修改人id
	private Integer updateUserId;
	//修改人
	private String   updateUserName;
	//修改部门id
	private Integer updateDeptId;
	//修改部门
	private String   updateDeptName;
	//修改时间
	private Date    updateTime;

	//	port_id	口岸id	VARCHAR(20)
	private String   portId;

	//	port_name	口岸名称	VARCHAR(100)
	private String   portName;

	//	national	口岸国家	VARCHAR(50)
	private String   national;

	//	flight_number	航班号	VARCHAR(20)
	private String   flightNumber;

	//	email_type	邮件类型	VARCHAR(10)
	private String   emailType;

	//	effective_start_date	生效开始日期	DATETIME
	private Date   effectiveStartDate;

	//	effective_end_date	生效结束日期	DATETIME
	private Date   effectiveEndDate;

	//	price	单价	DECIMAL(24,6)
	private BigDecimal price;



}
