package cn.com.xmmn.common.handler;

import com.alibaba.excel.write.handler.AbstractRowWriteHandler;
import com.alibaba.excel.write.handler.AbstractSheetWriteHandler;
import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;


/**
 * 自定义处理EasyExcel写入头处理程序
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-04 09:18:55
 */
public class CustomHeaderWriteHandler extends AbstractRowWriteHandler {

    private final String title;
    private final String dateRange;

    public CustomHeaderWriteHandler(String title, String dateRange) {
        this.title = title;
        this.dateRange = dateRange;
    }

    public void beforeRowCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Integer rowIndex, Integer relativeRowIndex, Boolean isHead) {
    }

    public void afterRowCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Integer relativeRowIndex, Boolean isHead) {
    }


    /**
     * 在销毁行之后的处理方法，用于创建标题和日期行
     */
    public void afterRowDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Integer relativeRowIndex, Boolean isHead) {
        if (relativeRowIndex == 0) {
            Sheet sheet = writeSheetHolder.getSheet();
            Workbook workbook = sheet.getWorkbook();

             // 创建标题行
            Row titleRow = sheet.createRow(0);
            titleRow.setHeightInPoints(30);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue(title);

            // 合并标题行的单元格（跨所有列）
            CellRangeAddress titleRegion = new CellRangeAddress(0, 0, 0, 15);

            // 检查并移除重叠的合并单元格区域
            removeExistingMergedRegion(sheet, titleRegion);

            // 添加新的合并单元格区域
            sheet.addMergedRegion(titleRegion);

            // 设置标题的样式
            CellStyle titleStyle = workbook.createCellStyle();
            Font titleFont = workbook.createFont();
            titleFont.setBold(true);
            titleFont.setFontHeightInPoints((short) 16);
            titleStyle.setFont(titleFont);
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            titleCell.setCellStyle(titleStyle);

            // 创建日期行
            Row dateRow = sheet.createRow(1);
            dateRow.setHeightInPoints(30);
            Cell dateCell = dateRow.createCell(0);
            dateCell.setCellValue(dateRange);

            // // 合并日期行的单元格（跨所有列）
            CellRangeAddress dateRegion = new CellRangeAddress(1, 1, 0, 15);

            // 检查并移除重叠的合并单元格区域
            removeExistingMergedRegion(sheet, dateRegion);

            // 添加新的合并单元格区域
            sheet.addMergedRegion(dateRegion);

            // 设置日期行的样式
            CellStyle dateStyle = workbook.createCellStyle();
            Font dateFont = workbook.createFont();
            dateFont.setFontHeightInPoints((short) 12);
            dateStyle.setFont(dateFont);
            dateStyle.setAlignment(HorizontalAlignment.CENTER);
            dateCell.setCellStyle(dateStyle);

            // 设置所有列的列宽
            for (int i = 0; i <= 15; i++) {
                sheet.setColumnWidth(i, 20 * 256);
            }
        }
    }
    /**
     * 移除已存在的重叠合并单元格区域
     * @param sheet 工作表对象
     * @param newRegion 新的合并单元格区域
     */
    private void removeExistingMergedRegion(Sheet sheet, CellRangeAddress newRegion) {
        for (int i = sheet.getNumMergedRegions() - 1; i >= 0; i--) {
            CellRangeAddress mergedRegion = sheet.getMergedRegion(i);
            if (mergedRegion.intersects(newRegion)) {
                sheet.removeMergedRegion(i);
            }
        }
    }
}
