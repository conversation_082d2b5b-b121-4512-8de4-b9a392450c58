package cn.com.xmmn.businesses.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;



/**
 * 出口邮件表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-13 14:08:34
 */
@Data
public class OutMailDO implements Serializable {
	private static final long serialVersionUID = 1L;
	//封发-交航
	private String  fenFaJiaoHang;

	//已交航袋数
	private Integer devNum;
	//已封发袋数
	private Integer fenFaNum;
	//ID;批次号
	private Integer id;
	//邮件号码
	private String itemId;
	//收寄机构代码
	private String cltOrgCode;
	//收寄日期
	private Date cltDatetime;
	//收寄省份代码
	private String cltProvinceCode;
	//收寄省份名称
	private String cltProvinceName;
	//收寄地市代码
	private String cltCityCode;
	//收寄地市名称
	private String cltCityName;
	//处理中心名称
	private String opOrgName;
	//处理中心卸车时间
	private Date opTime;
	//原寄直封路单开始时间
	private Date oeBillStartTime;
	//本局机构名称
	private String orgName;
	//交换站卸车时间
	private Date unloadOpTime;
	//邮袋条码
	private String oeBagBarcode;
	//计划航班
	private String flightNumber;
	//实际航班
	private String newFlightNo;
	//寄达国家/地区
	private String receiverCountryCode;
	//寄达国家/地区名称
	private String receiverCountryName;
	//寄达互换局代码
	private String oeDest;
	//寄达互换局名称
	private String oeDestName;
	//接收时间
	private Date gmtModified;
	//业务时间
	private Date pt;
	//乐观锁;数据版本号
	private Integer revision;
	//创建人id
	private Integer createUserId;
	//创建部门id
	private Integer createDeptId;
	//创建人
	private String createUserName;
	//创建部门
	private String createDeptName;
	//创建时间
	private Date createTime;
	//修改人id
	private Integer updateUserId;
	//修改人
	private String updateUserName;
	//修改部门id
	private Integer updateDeptId;
	//修改部门
	private String updateDeptName;
    //交换站代码
	private String orgCode;

	private Integer total;


}
