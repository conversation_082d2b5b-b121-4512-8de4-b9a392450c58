package cn.com.xmmn.businesses.dao;


import cn.com.xmmn.businesses.domain.FeedbackRecordsDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 信息反馈
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-08 15:45:24
 */
@Mapper
public interface FeedbackRecordsDao extends BaseMapper<FeedbackRecordsDO> {

	FeedbackRecordsDO get(Integer id);
	
	List<FeedbackRecordsDO> list(Map<String,Object> map);
	
	int count(Map<String,Object> map);
	
	int save(FeedbackRecordsDO feedbackRecords);
	
	int update(FeedbackRecordsDO feedbackRecords);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);
}
