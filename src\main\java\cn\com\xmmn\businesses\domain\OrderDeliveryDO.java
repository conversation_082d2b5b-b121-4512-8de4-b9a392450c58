package cn.com.xmmn.businesses.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;



/**
 * 投递订单表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-30 20:15:07
 */
@Data
public class OrderDeliveryDO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//ID
	private Integer id;
	//乐观锁;数据版本号
	private Integer revision;
	//创建人id
	private Integer createUserId;
	//创建人
	private String createUserName;
	//创建部门id
	private Integer createDeptId;
	//创建部门
	private String createDeptName;
	//创建时间
	private Date createTime;
	//修改人id
	private Integer updateUserId;
	//修改人
	private String updateUserName;
	//修改部门id
	private Integer updateDeptId;
	//修改部门
	private String updateDeptName;
	//修改时间
	private Date updateTime;
	//订单号
	private String trackingNo;
	//出口局编号
	private String exportPortCode;
	//出口局名称【深圳，广州等等】
	private String exportPortCodeName;
	//邮件类型【工商件 或者 住宅件】
	private String addressType;
	//抵港时间
	private Date receivedTime;
	//计费重量
	private BigDecimal feeWeight;
	//投递site编号
	private String deliverySiteNo;
	//成功投递时间
	private Date successDeliveryTime;
	//状态
	private String status;

	/**
	 * 设置：ID
	 */
	public void setId(Integer id) {
		this.id = id;
	}
	/**
	 * 获取：ID
	 */
	public Integer getId() {
		return id;
	}
	/**
	 * 设置：乐观锁;数据版本号
	 */
	public void setRevision(Integer revision) {
		this.revision = revision;
	}
	/**
	 * 获取：乐观锁;数据版本号
	 */
	public Integer getRevision() {
		return revision;
	}
	/**
	 * 设置：创建人id
	 */
	public void setCreateUserId(Integer createUserId) {
		this.createUserId = createUserId;
	}
	/**
	 * 获取：创建人id
	 */
	public Integer getCreateUserId() {
		return createUserId;
	}
	/**
	 * 设置：创建人
	 */
	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}
	/**
	 * 获取：创建人
	 */
	public String getCreateUserName() {
		return createUserName;
	}
	/**
	 * 设置：创建部门id
	 */
	public void setCreateDeptId(Integer createDeptId) {
		this.createDeptId = createDeptId;
	}
	/**
	 * 获取：创建部门id
	 */
	public Integer getCreateDeptId() {
		return createDeptId;
	}
	/**
	 * 设置：创建部门
	 */
	public void setCreateDeptName(String createDeptName) {
		this.createDeptName = createDeptName;
	}
	/**
	 * 获取：创建部门
	 */
	public String getCreateDeptName() {
		return createDeptName;
	}
	/**
	 * 设置：创建时间
	 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	/**
	 * 获取：创建时间
	 */
	public Date getCreateTime() {
		return createTime;
	}
	/**
	 * 设置：修改人id
	 */
	public void setUpdateUserId(Integer updateUserId) {
		this.updateUserId = updateUserId;
	}
	/**
	 * 获取：修改人id
	 */
	public Integer getUpdateUserId() {
		return updateUserId;
	}
	/**
	 * 设置：修改人
	 */
	public void setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
	}
	/**
	 * 获取：修改人
	 */
	public String getUpdateUserName() {
		return updateUserName;
	}
	/**
	 * 设置：修改部门id
	 */
	public void setUpdateDeptId(Integer updateDeptId) {
		this.updateDeptId = updateDeptId;
	}
	/**
	 * 获取：修改部门id
	 */
	public Integer getUpdateDeptId() {
		return updateDeptId;
	}
	/**
	 * 设置：修改部门
	 */
	public void setUpdateDeptName(String updateDeptName) {
		this.updateDeptName = updateDeptName;
	}
	/**
	 * 获取：修改部门
	 */
	public String getUpdateDeptName() {
		return updateDeptName;
	}
	/**
	 * 设置：修改时间
	 */
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	/**
	 * 获取：修改时间
	 */
	public Date getUpdateTime() {
		return updateTime;
	}
	/**
	 * 设置：订单号
	 */
	public void setTrackingNo(String trackingNo) {
		this.trackingNo = trackingNo;
	}
	/**
	 * 获取：订单号
	 */
	public String getTrackingNo() {
		return trackingNo;
	}
	/**
	 * 设置：抵港时间
	 */
	public void setReceivedTime(Date receivedTime) {
		this.receivedTime = receivedTime;
	}
	/**
	 * 获取：抵港时间
	 */
	public Date getReceivedTime() {
		return receivedTime;
	}
	/**
	 * 设置：计费重量
	 */
	public void setFeeWeight(BigDecimal feeWeight) {
		this.feeWeight = feeWeight;
	}
	/**
	 * 获取：计费重量
	 */
	public BigDecimal getFeeWeight() {
		return feeWeight;
	}
	/**
	 * 设置：投递site编号
	 */
	public void setDeliverySiteNo(String deliverySiteNo) {
		this.deliverySiteNo = deliverySiteNo;
	}
	/**
	 * 获取：投递site编号
	 */
	public String getDeliverySiteNo() {
		return deliverySiteNo;
	}
	/**
	 * 设置：成功投递时间
	 */
	public void setSuccessDeliveryTime(Date successDeliveryTime) {
		this.successDeliveryTime = successDeliveryTime;
	}
	/**
	 * 获取：成功投递时间
	 */
	public Date getSuccessDeliveryTime() {
		return successDeliveryTime;
	}
	/**
	 * 设置：状态
	 */
	public void setStatus(String status) {
		this.status = status;
	}
	/**
	 * 获取：状态
	 */
	public String getStatus() {
		return status;
	}
}
