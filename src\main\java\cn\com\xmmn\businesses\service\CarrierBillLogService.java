package cn.com.xmmn.businesses.service;

import cn.com.xmmn.businesses.domain.CarrierBillLogDO;
import cn.com.xmmn.businesses.domain.CarrierDealerRecivDO;
import cn.com.xmmn.common.utils.Query;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * 承运商账单操作日志
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-27 17:15:37
 */
public interface CarrierBillLogService extends IService<CarrierBillLogDO> {

    Page<CarrierBillLogDO> page(Map<String, Object> params);


}
