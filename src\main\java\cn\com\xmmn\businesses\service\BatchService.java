package cn.com.xmmn.businesses.service;

import cn.com.xmmn.businesses.domain.BatchDO;
import cn.com.xmmn.businesses.domain.CommercialDO;
import cn.com.xmmn.businesses.domain.NewDO;

import java.util.List;
import java.util.Map;

/**
 * 导入批次表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-06 13:07:47
 */
public interface BatchService {
	
	//BatchDO get(Integer id);
	
	List<BatchDO> list(Map<String, Object> map);
	
	int count(Map<String, Object> map);
	
	int save(BatchDO batch);


	
	int update(BatchDO batch);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);

	int exportCommercial (CommercialDO bacth);

	int exportNew (NewDO bacth);

	List detailsCommercial(CommercialDO bacth);

	List newCommercial(NewDO bacth);

	//	<!--查询商业导入每月件数报表-->
	List<Map<String,Object>> commercialList(Map<String,Object> map);


}
