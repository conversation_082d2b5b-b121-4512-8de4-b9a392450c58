package cn.com.xmmn.businesses.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description 账单导入列表
 * <AUTHOR>
 * @Date 2024/7/18 16:47
 */
@Data
@HeadRowHeight(value = 30) //头部行高
@ContentRowHeight(value = 25) //内容行高
@ColumnWidth(value = 20) //列宽
@HeadFontStyle(fontName = "宋体",fontHeightInPoints = 11)
public class CarrierBillDetailListVo implements Serializable {

    @ExcelProperty(value = "导入说明", index = 0)
    private String importContent;

    @ExcelProperty(value = "邮件种类", index = 1)
    private String mailType;
    //承运人名称
    @ExcelProperty(value = "承运人名称", index = 2)
    private String carrierName;
    //Barcode
    @ExcelProperty(value = "Barcode", index = 3)
    private String barcode;
    //CN38时间
    @ExcelProperty(value = "CN38时间", index = 4)
    private String cn38timeStr;
    //接收扫描时间
    @ExcelProperty(value = "接收扫描时间", index = 5)
    private String scanTimeStr;
    //启运地点
    @ExcelProperty(value = "启运地点", index = 6)
    private String startPlace;
    //启运时间
    @ExcelProperty(value = "启运时间", index = 7)
    private String startTimeStr;
    //中转到达地点
    @ExcelProperty(value = "中转到达地点", index = 8)
    private String transArrivePlace;
    //中转到达时间
    @ExcelProperty(value = "中转到达时间", index = 9)
    private String transArriveTimeStr;
    //中转启运时间
    @ExcelProperty(value = "中转启运时间", index = 10)
    private String transStartTimeStr;
    //到达地点
    @ExcelProperty(value = "到达地点", index = 11)
    private String arrivePlace;
    //目的地到达时间
    @ExcelProperty(value = "目的地到达时间", index = 12)
    private String arriveTimeStr;
    //目的地交邮时间
    @ExcelProperty(value = "目的地交邮时间", index = 13)
    private String holdTimeStr;
    //收费路由
    @ExcelProperty(value = "收费路由", index = 14)
    private String tollRoute;
    //航班
    @ExcelProperty(value = "航班", index = 15)
    private String flightNum;
    //重量
    @ExcelProperty(value = "重量", index = 16)
    private BigDecimal weight;
    //费率
    @ExcelProperty(value = "费率", index = 17)
    private BigDecimal feeRate;
    //金额
    @ExcelProperty(value = "金额", index = 18)
    private BigDecimal amount;
    //币种
    @ExcelProperty(value = "币种", index = 19)
    private String currency;
    //账务时期
    @ExcelProperty(value = "账务时期", index = 20)
    private String accountPeriod;
    //账单编号
    @ExcelProperty(value = "账单编号", index = 21)
    private String billNo;
    //备注
    @ExcelProperty(value = "备注", index = 22)
    private String remark;
    //运能编码
    @ExcelProperty(value = "运能编码", index = 23)
    private String capacityCode;
    //箱板类型
    @ExcelProperty(value = "箱板类型", index = 24)
    private String caseType;
    //集装器号（板号）
    @ExcelProperty(value = "集装器号（板号）", index = 25)
    private String packNum;

}
