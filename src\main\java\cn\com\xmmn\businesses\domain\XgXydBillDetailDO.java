package cn.com.xmmn.businesses.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-06 10:55:50
 */
@Data
@ApiModel(value = "香港出口结算表",description = "")
@TableName(value = "tb_xg_xyd_bill_detail")
public class XgXydBillDetailDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private String id;

    @TableField("mail_type")
    private String mailType;

    @TableField("carrier_id")
    private String carrierId;

    @TableField("carrier_name")
    private String carrierName;

    @TableField("barcode")
    private String barcode;

    @TableField("cn38time")
    private Date cn38Time;

    @TableField("scan_time")
    private Date scanTime;

    @TableField("start_place")
    private String startPlace;

    @TableField("start_time")
    private Date startTime;

    @TableField("trans_arrive_place")
    private String transArrivePlace;

    @TableField("trans_arrive_time")
    private Date transArriveTime;

    @TableField("trans_start_time")
    private Date transStartTime;

    @TableField("arrive_place")
    private String arrivePlace;

    @TableField("arrive_time")
    private Date arriveTime;

    @TableField("hold_time")
    private Date holdTime;

    @TableField("toll_route")
    private String tollRoute;

    @TableField("plan_flight_num")
    private String planFlightNum;

    @TableField("flight_num")
    private String flightNum;

    @TableField("weight")
    private BigDecimal weight;

    @TableField("fee_rate")
    private BigDecimal feeRate;

    @TableField("amount")
    private BigDecimal amount;

    @TableField("currency")
    private String currency;

    @TableField("account_period")
    private String accountPeriod;

    @TableField("bill_no")
    private String billNo;

    @TableField("remark")
    private String remark;

    @TableField("capacity_code")
    private String capacityCode;

    @TableField("case_type")
    private String caseType;

    @TableField("pack_num")
    private String packNum;

    @TableField("is_return_flag")
    private String isReturnFlag;

    @TableField("is_change_flight_flag")
    private String isChangeFlightFlag;

    @TableField("change_flight")
    private String changeFlight;

    @TableField("dispatch_time")
    private Date dispatchTime;

    @TableField("batch_id")
    private String batchId;

    @TableField("check_state")
    private String checkState;

    @TableField("check_tm")
    private Date checkTm;

    @TableField("check_by")
    private String checkBy;

    @TableField("pt")
    private Date pt;

    @TableField("deal_flag")
    private Integer dealFlag; // 0:未处理，1:已处理

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
}
