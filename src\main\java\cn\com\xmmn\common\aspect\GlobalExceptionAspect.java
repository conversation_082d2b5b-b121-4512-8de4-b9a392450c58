package cn.com.xmmn.common.aspect;

import cn.com.xmmn.common.utils.ApiResponseData;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

/**
 * 全局自定义切面编程
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024年7月25日09:44:35
 */
@Aspect
@Component
public class GlobalExceptionAspect {

    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionAspect.class);

    @AfterThrowing(pointcut = "execution(* cn.com.xmmn.businesses.controller.api..*(..))", throwing = "ex")
    public ResponseEntity<ApiResponseData> handleException(Exception ex) {
        log.error("处理请求时发生错误: ", ex);
        ApiResponseData responseData = new ApiResponseData(500, "服务器内部错误: " + ex.getMessage());
        return new ResponseEntity<>(responseData, HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
