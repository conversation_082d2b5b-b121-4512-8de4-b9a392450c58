package cn.com.xmmn.report.controller;

import cn.com.xmmn.businesses.domain.PortbatchDO;
import cn.com.xmmn.businesses.service.OrderDeliveryService;
import cn.com.xmmn.businesses.service.tableReportService;
import cn.com.xmmn.common.utils.ShiroUtils;
import cn.com.xmmn.report.domain.AirMailBusinessBillsDO;
import cn.com.xmmn.report.domain.DellMailBillsDO;
import cn.com.xmmn.report.domain.LandMailBillsDo;
import cn.com.xmmn.report.domain.ValueAddServiceDO;
import cn.com.xmmn.system.domain.UserDO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * TMS订单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-30 20:15:07
 */
@Slf4j
@Controller
@RequestMapping("/report/tableReport")
public class tableReportController {

    @Autowired
    private tableReportService tableReportService;


    @GetMapping()
    @RequiresPermissions("report:tableReport")
    String init(Model model) {
        //设置初始时间  当前年份
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
        String startDate = sdf.format(new Date());
        //柱形图时间
        model.addAttribute("startDate", startDate);
        return "report/tableReport";
    }

    //    柱状图
    @ResponseBody
    @PostMapping("/list")
    @RequiresPermissions("report:tableReport")
    public Map<String, Object> list(String startDate) {
        Map<String, Object> data = new HashMap<String, Object>();

        //所选年份数据
        Map<String, Object> map = new HashMap<String, Object>();
        //所选年份上一年数据
        String lastYear = String.valueOf(Integer.parseInt(startDate) - 1);
        
        //**********A1.邮件空运数据************//
        map.put("year", startDate);
        List<Map<String, Object>> airCurrentYearList = tableReportService.airList(map);
        String airCurrentYearResult = JSON.toJSONString(airCurrentYearList).toString();
        log.info("邮件空运" + startDate + "年返回结果：" + airCurrentYearResult);
        data.put("airCurrentYearResult", airCurrentYearList);


        map.put("year", lastYear);
        List<Map<String, Object>> airLastYearList = tableReportService.airList(map);
        String airLastYearResult = JSON.toJSONString(airLastYearList).toString();
        log.info("邮件空运" + lastYear + "年返回结果：" + airLastYearResult);
        data.put("airLastYearResult", airLastYearList);

        //计算增长比例
        List airList = new ArrayList();
        for (int i = 0; i < airCurrentYearList.size(); i++) {
            double current = ((BigDecimal) airCurrentYearList.get(i).get("data_count")).doubleValue();
            double last = ((BigDecimal) airLastYearList.get(i).get("data_count")).doubleValue();
            if (last == 0) {//若去年数据为0，则增长比例默认100%
                airList.add(100);
            } else {
                airList.add(Math.round((current - last) / last * 100.00));
            }
        }
        log.info("airList:" + airList.toString());
        data.put("airList", airList);

        //**********A2.邮件陆运数据************//
        map.put("year", startDate);
        Map<String, Object> landCurrentYear = tableReportService.landList(map);
        log.info("邮件陆运" + startDate + "年返回结果：" + landCurrentYear);
        List landCurrentYearList = null;
        if (landCurrentYear == null || landCurrentYear.isEmpty()) {
            //使用Java中的 Collections.nCopies() 方法来创建一个包含若干个 BigDecimal 类型的0的List
            landCurrentYearList = Collections.nCopies(12, BigDecimal.ZERO);
        } else {
            landCurrentYearList = new ArrayList<>(landCurrentYear.values());
        }
        log.info("landCurrentYearList:" + landCurrentYearList.toString());

        map.put("year", lastYear);
        Map<String, Object> landLastYear = tableReportService.landList(map);
        log.info("邮件陆运" + lastYear + "年返回结果：" + landLastYear);
        List landLastYearList = null;
        if (landLastYear == null || landLastYear.isEmpty()) {
            landLastYearList = Collections.nCopies(12, BigDecimal.ZERO);
        } else {
            landLastYearList = new ArrayList<>(landLastYear.values());
        }
        log.info("landLastYearList:" + landLastYearList.toString());
        data.put("landLastYearResult", landLastYearList);
        data.put("landCurrentYearResult", landCurrentYearList);

        //计算增长比例
        List landList = new ArrayList();
        for (int i = 0; i < landCurrentYearList.size(); i++) {
            double current = ((BigDecimal) landCurrentYearList.get(i)).doubleValue();
            double last = ((BigDecimal) landLastYearList.get(i)).doubleValue();
            if (last == 0) {//若去年数据为0，则增长比例默认100%
                landList.add(100);
            } else {
                landList.add(Math.round((current - last) / last * 100.00));
            }
        }
        log.info("landList:" + landList.toString());
        data.put("landList", landList);


        //**********A3.邮件进口数据************//
        //所选年份数据
        map.put("year", startDate);
        List<Map<String, Object>> importCurrentYearList = tableReportService.importList(map);
        String importCurrentYearResult = JSON.toJSONString(importCurrentYearList).toString();
        log.info("邮件进口" + startDate + "年返回结果：" + importCurrentYearResult);
        data.put("importCurrentYearResult", importCurrentYearList);


        map.put("year", lastYear);
        List<Map<String, Object>> importLastYearList = tableReportService.importList(map);
        String importLastYearResult = JSON.toJSONString(importLastYearList).toString();
        log.info("邮件进口" + lastYear + "年返回结果：" + importLastYearResult);
        data.put("importLastYearResult", importLastYearList);

        //计算增长比例
        List importList = new ArrayList();
        for (int i = 0; i < importCurrentYearList.size(); i++) {
            long current = (long) importCurrentYearList.get(i).get("data_count");
            long last = (long) importLastYearList.get(i).get("data_count");
            if (last == 0) {//若去年数据为0，则增长比例默认100%
                importList.add(100);
            } else {
                importList.add(Math.round((current - last) / last * 100.00));
            }
        }
        log.info("importList:" + importList.toString());
        data.put("importList", importList);


        //**********A4.香港快递数据************//
        //所选年份数据
        map.put("year", startDate);
        List<Map<String, Object>> deliveryCurrentYearList = tableReportService.deliveryList(map);
        String deliveryCurrentYearResult = JSON.toJSONString(deliveryCurrentYearList).toString();
        log.info("香港快递" + startDate + "年返回结果：" + deliveryCurrentYearResult);
        data.put("deliveryCurrentYearResult", deliveryCurrentYearList);


        map.put("year", lastYear);
        List<Map<String, Object>> deliveryLastYearList = tableReportService.deliveryList(map);
        String deliveryLastYearResult = JSON.toJSONString(deliveryLastYearList).toString();
        log.info("香港快递" + lastYear + "年返回结果：" + deliveryLastYearResult);
        data.put("deliveryLastYearResult", deliveryLastYearList);

        //计算增长比例
        List deliveryList = new ArrayList();
        for (int i = 0; i < deliveryCurrentYearList.size(); i++) {
            long current = (long) deliveryCurrentYearList.get(i).get("data_count");
            long last = (long) deliveryLastYearList.get(i).get("data_count");
            if (last == 0) {//若去年数据为0，则增长比例默认100%
                deliveryList.add(100);
            } else {
                deliveryList.add(Math.round((current - last) / last * 100.00));
            }
        }
        log.info("deliveryList:" + deliveryList.toString());
        data.put("deliveryList", deliveryList);



        //**********A5.商业专线数据************//
        //所选年份数据
        map.put("year", startDate);
        List<Map<String, Object>> tmsCurrentYearList = tableReportService.tmsList(map);
        String tmsCurrentYearResult = JSON.toJSONString(tmsCurrentYearList).toString();
        log.info("商业专线" + startDate + "年返回结果：" + tmsCurrentYearResult);
        data.put("tmsCurrentYearResult", tmsCurrentYearList);

        map.put("year", lastYear);
        List<Map<String, Object>> tmsLastYearList = tableReportService.tmsList(map);
        String tmsLastYearResult = JSON.toJSONString(tmsLastYearList).toString();
        log.info("商业专线" + lastYear + "年返回结果：" + tmsLastYearResult);
        data.put("tmsLastYearResult", tmsLastYearList);

        //计算增长比例
        List tmsList = new ArrayList();
        for (int i = 0; i < tmsCurrentYearList.size(); i++) {
            double current = ((BigDecimal) tmsCurrentYearList.get(i).get("data_count")).doubleValue();
            double last = ((BigDecimal) tmsLastYearList.get(i).get("data_count")).doubleValue();
            if (last == 0) {//若去年数据为0，则增长比例默认100%
                tmsList.add(100);
            } else {
                tmsList.add(Math.round((current - last) / last * 100.00));
            }
        }
        log.info("tmsList:" + tmsList.toString());
        data.put("tmsList", tmsList);


        //**********A6.增值服务数据************//
        map.put("year", startDate);
        Map<String, Object> addCurrentYear = tableReportService.addList(map);
        log.info("增值服务" + startDate + "年返回结果：" + addCurrentYear);
        List addCurrentYearList = null;
        if (addCurrentYear == null || addCurrentYear.isEmpty()) {
            //使用Java中的 Collections.nCopies() 方法来创建一个包含若干个 BigDecimal 类型的0的List
            addCurrentYearList = Collections.nCopies(12, BigDecimal.ZERO);
        } else {
            addCurrentYearList = new ArrayList<>(addCurrentYear.values());
        }
        log.info("addCurrentYearList:" + addCurrentYearList.toString());

        map.put("year", lastYear);
        Map<String, Object> addLastYear = tableReportService.addList(map);
        log.info("增值服务" + lastYear + "年返回结果：" + addLastYear);
        List addLastYearList = null;
        if (addLastYear == null || addLastYear.isEmpty()) {
            addLastYearList = Collections.nCopies(12, BigDecimal.ZERO);
        } else {
            addLastYearList = new ArrayList<>(addLastYear.values());
        }
        log.info("addLastYearList:" + addLastYearList.toString());
        data.put("addLastYearResult", addLastYearList);
        data.put("addCurrentYearResult", addCurrentYearList);

        //计算增长比例
        List addList = new ArrayList();
        for (int i = 0; i < addCurrentYearList.size(); i++) {
            double current = ((BigDecimal) addCurrentYearList.get(i)).doubleValue();
            double last = ((BigDecimal) addLastYearList.get(i)).doubleValue();
            if (last == 0) {//若去年数据为0，则增长比例默认100%
                addList.add(100);
            } else {
                addList.add(Math.round((current - last) / last * 100.00));
            }
        }
        log.info("addList:" + addList.toString());
        data.put("addList", addList);


        //**********A7.戴尔物流数据************//
        map.put("year", startDate);
        Map<String, Object> dellCurrentYear = tableReportService.dellList(map);
        log.info("戴尔物流" + startDate + "年返回结果：" + dellCurrentYear);
        List dellCurrentYearList = null;
        if (dellCurrentYear == null || dellCurrentYear.isEmpty()) {
            //使用Java中的 Collections.nCopies() 方法来创建一个包含若干个 BigDecimal 类型的0的List
            dellCurrentYearList = Collections.nCopies(12, BigDecimal.ZERO);
        } else {
            dellCurrentYearList = new ArrayList<>(dellCurrentYear.values());
        }
        log.info("dellCurrentYearList:" + dellCurrentYearList.toString());

        map.put("year", lastYear);
        Map<String, Object> dellLastYear = tableReportService.dellList(map);
        log.info("戴尔物流" + lastYear + "年返回结果：" + dellLastYear);
        List dellLastYearList = null;
        if (dellLastYear == null || dellLastYear.isEmpty()) {
            dellLastYearList = Collections.nCopies(12, BigDecimal.ZERO);
        } else {
            dellLastYearList = new ArrayList<>(dellLastYear.values());
        }
        log.info("dellLastYearList:" + dellLastYearList.toString());
        data.put("dellLastYearResult", dellLastYearList);
        data.put("dellCurrentYearResult", dellCurrentYearList);

        //计算增长比例
        List dellList = new ArrayList();
        for (int i = 0; i < dellCurrentYearList.size(); i++) {
            double current = ((BigDecimal) dellCurrentYearList.get(i)).doubleValue();
            double last = ((BigDecimal) dellLastYearList.get(i)).doubleValue();
            if (last == 0) {//若去年数据为0，则增长比例默认100%
                dellList.add(100);
            } else {
                dellList.add(Math.round((current - last) / last * 100.00));
            }
        }
        log.info("dellList:" + dellList.toString());
        data.put("dellList", dellList);


        //**********A8.商业空运数据************//
        map.put("year", startDate);
        Map<String, Object> airBusinessCurrentYear = tableReportService.airBusinessList(map);
        log.info("商业空运" + startDate + "年返回结果：" + airBusinessCurrentYear);
        List airBusinessCurrentYearList = null;
        if (airBusinessCurrentYear == null || airBusinessCurrentYear.isEmpty()) {
            //使用Java中的 Collections.nCopies() 方法来创建一个包含若干个 BigDecimal 类型的0的List
            airBusinessCurrentYearList = Collections.nCopies(12, BigDecimal.ZERO);
        } else {
            airBusinessCurrentYearList = new ArrayList<>(airBusinessCurrentYear.values());
        }
        log.info("airBusinessCurrentYearList:" + airBusinessCurrentYearList.toString());

        map.put("year", lastYear);
        Map<String, Object> airBusinessLastYear = tableReportService.airBusinessList(map);
        log.info("商业空运" + lastYear + "年返回结果：" + airBusinessLastYear);
        List airBusinessLastYearList = null;
        if (airBusinessLastYear == null || airBusinessLastYear.isEmpty()) {
            airBusinessLastYearList = Collections.nCopies(12, BigDecimal.ZERO);
        } else {
            airBusinessLastYearList = new ArrayList<>(airBusinessLastYear.values());
        }
        log.info("airBusinessLastYearList:" + airBusinessLastYearList.toString());
        data.put("airBusinessLastYearResult", airBusinessLastYearList);
        data.put("airBusinessCurrentYearResult", airBusinessCurrentYearList);

        //计算增长比例
        List airBusinessList = new ArrayList();
        for (int i = 0; i < airBusinessCurrentYearList.size(); i++) {
            double current = ((BigDecimal) airBusinessCurrentYearList.get(i)).doubleValue();
            double last = ((BigDecimal) airBusinessLastYearList.get(i)).doubleValue();
            if (last == 0) {//若去年数据为0，则增长比例默认100%
                airBusinessList.add(100);
            } else {
                airBusinessList.add(Math.round((current - last) / last * 100.00));
            }
        }
        log.info("airBusinessList:" + airBusinessList.toString());
        data.put("airBusinessList", airBusinessList);
        
        
        data.put("status", "success");
        return data;

    }


    //邮件陆运保存
    @ResponseBody
    @PostMapping("/landSave")
    @RequiresPermissions("report:tableReport")
    public Map<String, Object> landSave(String current, String current1, String current2, String current3, String current4, String current5, String current6
            , String current7, String current8, String current9, String current10, String current11, String current12, String last1, String last2
            , String last3, String last4, String last5, String last6
            , String last7, String last8, String last9, String last10, String last11, String last12) {
        Map<String, Object> data = new HashMap<String, Object>();

        //登录信息插批次表
        UserDO userDO = ShiroUtils.getUser();
        LandMailBillsDo dto = new LandMailBillsDo();
        long userId = userDO.getUserId();
        String UserName = userDO.getUsername();
        dto.setCreateUserId((int) userId);
        dto.setCreateUserName(UserName);
        dto.setCreateTime(new Date());
        dto.setYear(current.substring(0, 4));
        dto.setJan("".equals(current1) ? BigDecimal.ZERO : new BigDecimal(current1));
        dto.setFeb("".equals(current2) ? BigDecimal.ZERO : new BigDecimal(current2));
        dto.setMar("".equals(current3) ? BigDecimal.ZERO : new BigDecimal(current3));
        dto.setApr("".equals(current4) ? BigDecimal.ZERO : new BigDecimal(current4));
        dto.setMay("".equals(current5) ? BigDecimal.ZERO : new BigDecimal(current5));
        dto.setJun("".equals(current6) ? BigDecimal.ZERO : new BigDecimal(current6));
        dto.setJul("".equals(current7) ? BigDecimal.ZERO : new BigDecimal(current7));
        dto.setAug("".equals(current8) ? BigDecimal.ZERO : new BigDecimal(current8));
        dto.setSep("".equals(current9) ? BigDecimal.ZERO : new BigDecimal(current9));
        dto.setOct("".equals(current10) ? BigDecimal.ZERO : new BigDecimal(current10));
        dto.setNov("".equals(current11) ? BigDecimal.ZERO : new BigDecimal(current11));
        dto.setDece("".equals(current12) ? BigDecimal.ZERO : new BigDecimal(current12));
        //保存条件筛选框选择的年份数据
        tableReportService.landSave(dto);
        //保存条件筛选框选择的年份-1年数据
        dto.setYear(String.valueOf((Integer.parseInt(current.substring(0, 4)) - 1)));
        dto.setJan("".equals(last1) ? BigDecimal.ZERO : new BigDecimal(last1));
        dto.setFeb("".equals(last2) ? BigDecimal.ZERO : new BigDecimal(last2));
        dto.setMar("".equals(last3) ? BigDecimal.ZERO : new BigDecimal(last3));
        dto.setApr("".equals(last4) ? BigDecimal.ZERO : new BigDecimal(last4));
        dto.setMay("".equals(last5) ? BigDecimal.ZERO : new BigDecimal(last5));
        dto.setJun("".equals(last6) ? BigDecimal.ZERO : new BigDecimal(last6));
        dto.setJul("".equals(last7) ? BigDecimal.ZERO : new BigDecimal(last7));
        dto.setAug("".equals(last8) ? BigDecimal.ZERO : new BigDecimal(last8));
        dto.setSep("".equals(last9) ? BigDecimal.ZERO : new BigDecimal(last9));
        dto.setOct("".equals(last10) ? BigDecimal.ZERO : new BigDecimal(last10));
        dto.setNov("".equals(last11) ? BigDecimal.ZERO : new BigDecimal(last11));
        dto.setDece("".equals(last12) ? BigDecimal.ZERO : new BigDecimal(last12));
        tableReportService.landSave(dto);
        data.put("status", "success");
        return data;
    }


    //增值服务保存
    @ResponseBody
    @PostMapping("/addSave")
    @RequiresPermissions("report:tableReport")
    public Map<String, Object> addSave(String current, String current1, String current2, String current3, String current4, String current5, String current6
            , String current7, String current8, String current9, String current10, String current11, String current12, String last1, String last2
            , String last3, String last4, String last5, String last6
            , String last7, String last8, String last9, String last10, String last11, String last12) {
        Map<String, Object> data = new HashMap<String, Object>();

        //登录信息插批次表
        UserDO userDO = ShiroUtils.getUser();
        ValueAddServiceDO dto = new ValueAddServiceDO();
        long userId = userDO.getUserId();
        String UserName = userDO.getUsername();
        dto.setCreateUserId((int) userId);
        dto.setCreateUserName(UserName);
        dto.setCreateTime(new Date());
        dto.setYear(current.substring(0, 4));
        dto.setJan("".equals(current1) ? BigDecimal.ZERO : new BigDecimal(current1));
        dto.setFeb("".equals(current2) ? BigDecimal.ZERO : new BigDecimal(current2));
        dto.setMar("".equals(current3) ? BigDecimal.ZERO : new BigDecimal(current3));
        dto.setApr("".equals(current4) ? BigDecimal.ZERO : new BigDecimal(current4));
        dto.setMay("".equals(current5) ? BigDecimal.ZERO : new BigDecimal(current5));
        dto.setJun("".equals(current6) ? BigDecimal.ZERO : new BigDecimal(current6));
        dto.setJul("".equals(current7) ? BigDecimal.ZERO : new BigDecimal(current7));
        dto.setAug("".equals(current8) ? BigDecimal.ZERO : new BigDecimal(current8));
        dto.setSep("".equals(current9) ? BigDecimal.ZERO : new BigDecimal(current9));
        dto.setOct("".equals(current10) ? BigDecimal.ZERO : new BigDecimal(current10));
        dto.setNov("".equals(current11) ? BigDecimal.ZERO : new BigDecimal(current11));
        dto.setDece("".equals(current12) ? BigDecimal.ZERO : new BigDecimal(current12));
        //保存条件筛选框选择的年份数据
        tableReportService.addSave(dto);
        //保存条件筛选框选择的年份-1年数据
        dto.setYear(String.valueOf((Integer.parseInt(current.substring(0, 4)) - 1)));
        dto.setJan("".equals(last1) ? BigDecimal.ZERO : new BigDecimal(last1));
        dto.setFeb("".equals(last2) ? BigDecimal.ZERO : new BigDecimal(last2));
        dto.setMar("".equals(last3) ? BigDecimal.ZERO : new BigDecimal(last3));
        dto.setApr("".equals(last4) ? BigDecimal.ZERO : new BigDecimal(last4));
        dto.setMay("".equals(last5) ? BigDecimal.ZERO : new BigDecimal(last5));
        dto.setJun("".equals(last6) ? BigDecimal.ZERO : new BigDecimal(last6));
        dto.setJul("".equals(last7) ? BigDecimal.ZERO : new BigDecimal(last7));
        dto.setAug("".equals(last8) ? BigDecimal.ZERO : new BigDecimal(last8));
        dto.setSep("".equals(last9) ? BigDecimal.ZERO : new BigDecimal(last9));
        dto.setOct("".equals(last10) ? BigDecimal.ZERO : new BigDecimal(last10));
        dto.setNov("".equals(last11) ? BigDecimal.ZERO : new BigDecimal(last11));
        dto.setDece("".equals(last12) ? BigDecimal.ZERO : new BigDecimal(last12));
        tableReportService.addSave(dto);
        data.put("status", "success");
        return data;
    }


    //戴尔物流保存
    @ResponseBody
    @PostMapping("/dellSave")
    @RequiresPermissions("report:tableReport")
    public Map<String, Object> dellSave(String current, String current1, String current2, String current3, String current4, String current5, String current6
            , String current7, String current8, String current9, String current10, String current11, String current12, String last1, String last2
            , String last3, String last4, String last5, String last6
            , String last7, String last8, String last9, String last10, String last11, String last12) {
        Map<String, Object> data = new HashMap<String, Object>();

        //登录信息插批次表
        UserDO userDO = ShiroUtils.getUser();
        DellMailBillsDO dto = new DellMailBillsDO();
        long userId = userDO.getUserId();
        String UserName = userDO.getUsername();
        dto.setCreateUserId((int) userId);
        dto.setCreateUserName(UserName);
        dto.setCreateTime(new Date());
        dto.setYear(current.substring(0, 4));
        dto.setJan("".equals(current1) ? BigDecimal.ZERO : new BigDecimal(current1));
        dto.setFeb("".equals(current2) ? BigDecimal.ZERO : new BigDecimal(current2));
        dto.setMar("".equals(current3) ? BigDecimal.ZERO : new BigDecimal(current3));
        dto.setApr("".equals(current4) ? BigDecimal.ZERO : new BigDecimal(current4));
        dto.setMay("".equals(current5) ? BigDecimal.ZERO : new BigDecimal(current5));
        dto.setJun("".equals(current6) ? BigDecimal.ZERO : new BigDecimal(current6));
        dto.setJul("".equals(current7) ? BigDecimal.ZERO : new BigDecimal(current7));
        dto.setAug("".equals(current8) ? BigDecimal.ZERO : new BigDecimal(current8));
        dto.setSep("".equals(current9) ? BigDecimal.ZERO : new BigDecimal(current9));
        dto.setOct("".equals(current10) ? BigDecimal.ZERO : new BigDecimal(current10));
        dto.setNov("".equals(current11) ? BigDecimal.ZERO : new BigDecimal(current11));
        dto.setDece("".equals(current12) ? BigDecimal.ZERO : new BigDecimal(current12));
        //保存条件筛选框选择的年份数据
        tableReportService.dellSave(dto);
        //保存条件筛选框选择的年份-1年数据
        dto.setYear(String.valueOf((Integer.parseInt(current.substring(0, 4)) - 1)));
        dto.setJan("".equals(last1) ? BigDecimal.ZERO : new BigDecimal(last1));
        dto.setFeb("".equals(last2) ? BigDecimal.ZERO : new BigDecimal(last2));
        dto.setMar("".equals(last3) ? BigDecimal.ZERO : new BigDecimal(last3));
        dto.setApr("".equals(last4) ? BigDecimal.ZERO : new BigDecimal(last4));
        dto.setMay("".equals(last5) ? BigDecimal.ZERO : new BigDecimal(last5));
        dto.setJun("".equals(last6) ? BigDecimal.ZERO : new BigDecimal(last6));
        dto.setJul("".equals(last7) ? BigDecimal.ZERO : new BigDecimal(last7));
        dto.setAug("".equals(last8) ? BigDecimal.ZERO : new BigDecimal(last8));
        dto.setSep("".equals(last9) ? BigDecimal.ZERO : new BigDecimal(last9));
        dto.setOct("".equals(last10) ? BigDecimal.ZERO : new BigDecimal(last10));
        dto.setNov("".equals(last11) ? BigDecimal.ZERO : new BigDecimal(last11));
        dto.setDece("".equals(last12) ? BigDecimal.ZERO : new BigDecimal(last12));
        tableReportService.dellSave(dto);
        data.put("status", "success");
        return data;
    }


    //商业空运保存
    @ResponseBody
    @PostMapping("/airBusinessSave")
    @RequiresPermissions("report:tableReport")
    public Map<String, Object> airBusinessSave(String current, String current1, String current2, String current3, String current4, String current5, String current6
            , String current7, String current8, String current9, String current10, String current11, String current12, String last1, String last2
            , String last3, String last4, String last5, String last6
            , String last7, String last8, String last9, String last10, String last11, String last12) {
        Map<String, Object> data = new HashMap<String, Object>();

        //登录信息插批次表
        UserDO userDO = ShiroUtils.getUser();
        AirMailBusinessBillsDO dto = new AirMailBusinessBillsDO();
        long userId = userDO.getUserId();
        String UserName = userDO.getUsername();
        dto.setCreateUserId((int) userId);
        dto.setCreateUserName(UserName);
        dto.setCreateTime(new Date());
        dto.setYear(current.substring(0, 4));
        dto.setJan("".equals(current1) ? BigDecimal.ZERO : new BigDecimal(current1));
        dto.setFeb("".equals(current2) ? BigDecimal.ZERO : new BigDecimal(current2));
        dto.setMar("".equals(current3) ? BigDecimal.ZERO : new BigDecimal(current3));
        dto.setApr("".equals(current4) ? BigDecimal.ZERO : new BigDecimal(current4));
        dto.setMay("".equals(current5) ? BigDecimal.ZERO : new BigDecimal(current5));
        dto.setJun("".equals(current6) ? BigDecimal.ZERO : new BigDecimal(current6));
        dto.setJul("".equals(current7) ? BigDecimal.ZERO : new BigDecimal(current7));
        dto.setAug("".equals(current8) ? BigDecimal.ZERO : new BigDecimal(current8));
        dto.setSep("".equals(current9) ? BigDecimal.ZERO : new BigDecimal(current9));
        dto.setOct("".equals(current10) ? BigDecimal.ZERO : new BigDecimal(current10));
        dto.setNov("".equals(current11) ? BigDecimal.ZERO : new BigDecimal(current11));
        dto.setDece("".equals(current12) ? BigDecimal.ZERO : new BigDecimal(current12));
        //保存条件筛选框选择的年份数据
        tableReportService.airBusinessSave(dto);
        //保存条件筛选框选择的年份-1年数据
        dto.setYear(String.valueOf((Integer.parseInt(current.substring(0, 4)) - 1)));
        dto.setJan("".equals(last1) ? BigDecimal.ZERO : new BigDecimal(last1));
        dto.setFeb("".equals(last2) ? BigDecimal.ZERO : new BigDecimal(last2));
        dto.setMar("".equals(last3) ? BigDecimal.ZERO : new BigDecimal(last3));
        dto.setApr("".equals(last4) ? BigDecimal.ZERO : new BigDecimal(last4));
        dto.setMay("".equals(last5) ? BigDecimal.ZERO : new BigDecimal(last5));
        dto.setJun("".equals(last6) ? BigDecimal.ZERO : new BigDecimal(last6));
        dto.setJul("".equals(last7) ? BigDecimal.ZERO : new BigDecimal(last7));
        dto.setAug("".equals(last8) ? BigDecimal.ZERO : new BigDecimal(last8));
        dto.setSep("".equals(last9) ? BigDecimal.ZERO : new BigDecimal(last9));
        dto.setOct("".equals(last10) ? BigDecimal.ZERO : new BigDecimal(last10));
        dto.setNov("".equals(last11) ? BigDecimal.ZERO : new BigDecimal(last11));
        dto.setDece("".equals(last12) ? BigDecimal.ZERO : new BigDecimal(last12));
        tableReportService.airBusinessSave(dto);
        data.put("status", "success");
        return data;
    }

}
