package cn.com.xmmn.businesses.controller;

import cn.com.xmmn.businesses.domain.CarrierAppraisalDO;
import cn.com.xmmn.businesses.service.CarrierAppraisalService;
import cn.com.xmmn.businesses.vo.CarrierDealerDepartListVo;
import cn.com.xmmn.common.utils.Convert;
import cn.com.xmmn.common.utils.PageUtils;
import cn.com.xmmn.common.utils.Query;
import cn.com.xmmn.common.utils.StringUtils;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;

/**
 * 承运商整体情况的考评表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-26 15:59:10
 */
@Slf4j
@Controller
@RequestMapping("/carrierAppraisal/monitor")
public class CarrierAppraisalController {
	@Autowired
	private CarrierAppraisalService carrierAppraisalService;
	
	@GetMapping()
	@RequiresPermissions("carrierAppraisal:carrierAppraisal")
	String carrierAppraisal(){
	    return "carrierAppraisal/list";
	}
	
	@ResponseBody
	@GetMapping("/list")
	@RequiresPermissions("carrierAppraisal:carrierAppraisal")
	public PageUtils list(@RequestParam Map<String, Object> params){
		//查询列表数据
        Query query = new Query(params);
		Page<CarrierAppraisalDO> carrierAppraisalDOPage  = carrierAppraisalService.page(query);
		PageUtils pageUtils = new PageUtils(carrierAppraisalDOPage.getRecords(),(int)carrierAppraisalDOPage.getTotal());
		return pageUtils;
	}

	/**
	 * 导出数据
	 *
	 * @param map
	 * @param response 入参
	 * @return void
	 **/
	@RequestMapping(value = "/exportExcel", method = RequestMethod.GET)
	public void exportExcel(@RequestParam Map<String, Object> map, HttpServletResponse response) throws IOException {

		QueryWrapper<CarrierAppraisalDO> queryWrapper = new QueryWrapper<CarrierAppraisalDO>();
		//月份
		String appraisalMonStart = Convert.toStr(map.get("appraisalMonStart"));
		String appraisalMonEnd = Convert.toStr(map.get("appraisalMonEnd"));

		if (StringUtils.isNotEmpty(appraisalMonStart) && StringUtils.isNotEmpty(appraisalMonEnd)){
			queryWrapper.between("appraisal_mon", appraisalMonStart, appraisalMonEnd);
		}
		String productCode = Convert.toStr(map.get("productCode"));
		if(StringUtils.isNotEmpty(productCode)){
			queryWrapper.eq("product_code",productCode);
		}
		String carrierCode = Convert.toStr(map.get("carrierCode"));
		if(StringUtils.isNotEmpty(carrierCode)){
			queryWrapper.eq("carrier_code",productCode);
		}
		List<CarrierAppraisalDO> carrierDealerDepartListVos = carrierAppraisalService.list(queryWrapper);
		//导出
		response.setContentType("application/vnd.ms-excel");
		response.setCharacterEncoding("UTF-8");
		// 文件名
		String fileName = map.get("dateStart") + "承运商接收情况统计表" + ".xlsx";
		try {
			fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");

		try {
			//导出
			EasyExcel.write(response.getOutputStream(), CarrierAppraisalDO.class).sheet("承运商接收情况统计表").doWrite(carrierDealerDepartListVos);
		} catch (Exception e) {
			log.info("excel文档导出错误-异常信息：" + e);
			e.printStackTrace();
		}

	}
	
}
