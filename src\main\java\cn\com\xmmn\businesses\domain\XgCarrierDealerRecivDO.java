package cn.com.xmmn.businesses.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 香港出口邮袋表;
 * <AUTHOR> zjy
 * @date : 2024-12-27
 */
@ApiModel(value = "香港出口邮袋表",description = "")
@Data
@TableName(value = "TB_XG_CARRIER_DEALER_RECIV")
@JsonIgnoreProperties(ignoreUnknown = true)
public class XgCarrierDealerRecivDO implements Serializable {

    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String id ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String orgName ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String carrierDealer ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String bussType ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String oeBagBarcode ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String flightNumber ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String newFlightNo ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String transType ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String receiverCountryCode ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String oeDest ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String oeDestName ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date oeBillStartTime ;
    /**  */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "",notes = "")
    private Date opTime352 ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date opTime404 ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date opTime538 ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date opTime457 ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date opTime505 ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date opTime507 ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date opTime516 ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date opTime459 ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date opTime486 ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date opTime460 ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date opTime461 ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date opTime462 ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date opTime463 ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date pt ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String totalWeight ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String signBody ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String orgCode ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String provinceCode ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String provinceName ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String carrierName ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String carrierCode ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String productCode ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String product ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String promiseTime ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String cyssxId ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String receiverCountryName ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String receiverCountry ;
}
