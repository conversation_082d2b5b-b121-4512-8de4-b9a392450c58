package cn.com.xmmn.businesses.strategy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 进口互换局时限对应类型导出策略工厂
 * <AUTHOR>
 * @email <EMAIL>
 * @date  2024年11月29日10:53:34
 */
@Component
public class ImportHhjDisposeExportStrategyFactory {
    @Autowired
    private ApplicationContext applicationContext;

    // 使用 Map 存储策略信息
    private Map<String, ImportHhjDisposeExportStrategy> strategyMap = new HashMap<>();

    // 自动注册策略类 对应类型---处理类
    @PostConstruct
    public void init() {
        // 从 Spring 上下文中获取所有的策略类，并注册到策略映射中
        // (邮件)接收-开拆 --应处理量
        registerStrategy("ycl", YclImportHhjDisposeExportStrategy.class);
        //(邮件)接收-开拆 -- 开拆量
        registerStrategy("kc", KcInfoImportHhjDisposeExportStrategy.class);
        registerStrategy("dtg", DtgImportHhjDisposeExportStrategy.class);
        registerStrategy("kcff", KcffImportHhjDisposeExportStrategy.class);
        registerStrategy("jsfy", JsfyImportHhjDisposeExportStrategy.class);
        registerStrategy("fytd", FytdImportHhjDisposeExportStrategy.class);
        registerStrategy("fffy", FffyImportHhjDisposeExportStrategy.class);
    }

    // 注册策略类到 strategyMap
    private void registerStrategy(String type, Class<? extends ImportHhjDisposeExportStrategy> strategyClass) {
        ImportHhjDisposeExportStrategy strategy = applicationContext.getBean(strategyClass);
        strategyMap.put(type, strategy);
    }

    // 根据导出类型获取对应的策略
    public ImportHhjDisposeExportStrategy getExportTypeHandleStrategy(String receiptType) {
        return strategyMap.get(receiptType);
    }

    // 提供动态注册策略的功能
    public void registerStrategy(String exportType, ImportHhjDisposeExportStrategy strategy) {
        strategyMap.put(exportType, strategy);
    }
}
