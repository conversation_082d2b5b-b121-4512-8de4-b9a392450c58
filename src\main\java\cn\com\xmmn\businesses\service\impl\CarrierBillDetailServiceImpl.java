package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.CarrierBillDetailDao;
import cn.com.xmmn.businesses.domain.CarrierBillDetailDO;
import cn.com.xmmn.businesses.service.CarrierBillDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;



@Service
public class CarrierBillDetailServiceImpl implements CarrierBillDetailService {
	@Autowired
	private CarrierBillDetailDao carrierBillDetailDao;
	
	@Override
	public CarrierBillDetailDO get(Integer id){
		return carrierBillDetailDao.get(id);
	}
	
	@Override
	public List<CarrierBillDetailDO> list(Map<String, Object> map){
		return carrierBillDetailDao.list(map);
	}
	
	@Override
	public int count(Map<String, Object> map){
		return carrierBillDetailDao.count(map);
	}
	
	@Override
	public int save(CarrierBillDetailDO carrierBillDetail){
		return carrierBillDetailDao.save(carrierBillDetail);
	}
	
	@Override
	public int update(CarrierBillDetailDO carrierBillDetail){
		return carrierBillDetailDao.update(carrierBillDetail);
	}

	@Override
	public int settleUpdate(CarrierBillDetailDO carrierBillDetail){
		return carrierBillDetailDao.settleUpdate(carrierBillDetail);
	}
	
	@Override
	public int remove(Integer id){
		return carrierBillDetailDao.remove(id);
	}
	
	@Override
	public int batchRemove(Integer[] ids){
		return carrierBillDetailDao.batchRemove(ids);
	}
	
}
