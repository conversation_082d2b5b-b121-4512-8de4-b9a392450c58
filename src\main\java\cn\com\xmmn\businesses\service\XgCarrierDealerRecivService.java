package cn.com.xmmn.businesses.service;

import cn.com.xmmn.businesses.domain.CarrierDealerRecivDO;
import cn.com.xmmn.businesses.domain.XgCarrierDealerRecivDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 香港出口邮袋表;(TB_XG_CARRIER_DEALER_RECIV)表服务接口
 * <AUTHOR> zjy
 * @date : 2024-12-27
 */
public interface XgCarrierDealerRecivService extends IService<XgCarrierDealerRecivDO> {

    /**
     * 保存信息
     * @param dealerRecivList
     */
    void save(List<XgCarrierDealerRecivDO> dealerRecivList);
}
