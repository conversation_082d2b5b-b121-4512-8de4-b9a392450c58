package cn.com.xmmn.businesses.vo;

import cn.com.xmmn.businesses.domain.ImportMailDO;
import lombok.Data;

/**
 * 进口邮件应处理列表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-20 15:02:00
 */
@Data
public class ImportMailShouldListVO extends ImportMailDO {




     // 应处理量
    //接收互换局是否与寄达局一致:0=否1=是
    private  Integer chIsEqualDest;
    //交换站发运-互换局接收时长(h)
    private Double sendRecvtimeDiff;



    /////////////////开拆量////////////////////////////////////////

    //互换局接收-开拆时长（h）
    private Double recvAndOpenTime;

    //内部格口封发-开拆时长（h）
    private Double sendAndOpenTime;


    //封发量

    //互换局开拆-互换局封发 （h）
    private Double openTimeMinusDisTime;


    //发运量--互换局封发-互换局发运（h）
    private Double disTimeMinusSendTime;


    //发运、投递及时率邮件明细-- （接收-发运）时长(h)
    private Double recvTimeReduceSendTime;
    //发运、投递及时率邮件明细-- （发运 -投递）时长（h)
    private Double sendTimeReduceDeliverTime;
}
