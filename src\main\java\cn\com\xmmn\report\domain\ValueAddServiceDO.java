package cn.com.xmmn.report.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;



/**
 * 增值服务表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-14 17:31:07
 */
public class ValueAddServiceDO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//ID
	private Integer id;
	//创建人id
	private Integer createUserId;
	//创建人
	private String createUserName;
	//创建时间
	private Date createTime;
	//修改人id
	private Integer updateUserId;
	//修改人
	private String updateUserName;
	//修改时间
	private Date updateTime;
	//年份
	private String year;
	//1月
	private BigDecimal jan;
	//2月
	private BigDecimal feb;
	//3月
	private BigDecimal mar;
	//4月
	private BigDecimal apr;
	//5月
	private BigDecimal may;
	//6月
	private BigDecimal jun;
	//7月
	private BigDecimal jul;
	//8月
	private BigDecimal aug;
	//9月
	private BigDecimal sep;
	//10月
	private BigDecimal oct;
	//11月
	private BigDecimal nov;
	//12月
	private BigDecimal dece;

	/**
	 * 设置：ID
	 */
	public void setId(Integer id) {
		this.id = id;
	}
	/**
	 * 获取：ID
	 */
	public Integer getId() {
		return id;
	}
	/**
	 * 设置：创建人id
	 */
	public void setCreateUserId(Integer createUserId) {
		this.createUserId = createUserId;
	}
	/**
	 * 获取：创建人id
	 */
	public Integer getCreateUserId() {
		return createUserId;
	}
	/**
	 * 设置：创建人
	 */
	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}
	/**
	 * 获取：创建人
	 */
	public String getCreateUserName() {
		return createUserName;
	}
	/**
	 * 设置：创建时间
	 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	/**
	 * 获取：创建时间
	 */
	public Date getCreateTime() {
		return createTime;
	}
	/**
	 * 设置：修改人id
	 */
	public void setUpdateUserId(Integer updateUserId) {
		this.updateUserId = updateUserId;
	}
	/**
	 * 获取：修改人id
	 */
	public Integer getUpdateUserId() {
		return updateUserId;
	}
	/**
	 * 设置：修改人
	 */
	public void setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
	}
	/**
	 * 获取：修改人
	 */
	public String getUpdateUserName() {
		return updateUserName;
	}
	/**
	 * 设置：修改时间
	 */
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	/**
	 * 获取：修改时间
	 */
	public Date getUpdateTime() {
		return updateTime;
	}
	/**
	 * 设置：年份
	 */
	public void setYear(String year) {
		this.year = year;
	}
	/**
	 * 获取：年份
	 */
	public String getYear() {
		return year;
	}
	/**
	 * 设置：1月
	 */
	public void setJan(BigDecimal jan) {
		this.jan = jan;
	}
	/**
	 * 获取：1月
	 */
	public BigDecimal getJan() {
		return jan;
	}
	/**
	 * 设置：2月
	 */
	public void setFeb(BigDecimal feb) {
		this.feb = feb;
	}
	/**
	 * 获取：2月
	 */
	public BigDecimal getFeb() {
		return feb;
	}
	/**
	 * 设置：3月
	 */
	public void setMar(BigDecimal mar) {
		this.mar = mar;
	}
	/**
	 * 获取：3月
	 */
	public BigDecimal getMar() {
		return mar;
	}
	/**
	 * 设置：4月
	 */
	public void setApr(BigDecimal apr) {
		this.apr = apr;
	}
	/**
	 * 获取：4月
	 */
	public BigDecimal getApr() {
		return apr;
	}
	/**
	 * 设置：5月
	 */
	public void setMay(BigDecimal may) {
		this.may = may;
	}
	/**
	 * 获取：5月
	 */
	public BigDecimal getMay() {
		return may;
	}
	/**
	 * 设置：6月
	 */
	public void setJun(BigDecimal jun) {
		this.jun = jun;
	}
	/**
	 * 获取：6月
	 */
	public BigDecimal getJun() {
		return jun;
	}
	/**
	 * 设置：7月
	 */
	public void setJul(BigDecimal jul) {
		this.jul = jul;
	}
	/**
	 * 获取：7月
	 */
	public BigDecimal getJul() {
		return jul;
	}
	/**
	 * 设置：8月
	 */
	public void setAug(BigDecimal aug) {
		this.aug = aug;
	}
	/**
	 * 获取：8月
	 */
	public BigDecimal getAug() {
		return aug;
	}
	/**
	 * 设置：9月
	 */
	public void setSep(BigDecimal sep) {
		this.sep = sep;
	}
	/**
	 * 获取：9月
	 */
	public BigDecimal getSep() {
		return sep;
	}
	/**
	 * 设置：10月
	 */
	public void setOct(BigDecimal oct) {
		this.oct = oct;
	}
	/**
	 * 获取：10月
	 */
	public BigDecimal getOct() {
		return oct;
	}
	/**
	 * 设置：11月
	 */
	public void setNov(BigDecimal nov) {
		this.nov = nov;
	}
	/**
	 * 获取：11月
	 */
	public BigDecimal getNov() {
		return nov;
	}
	/**
	 * 设置：12月
	 */
	public void setDece(BigDecimal dece) {
		this.dece = dece;
	}
	/**
	 * 获取：12月
	 */
	public BigDecimal getDece() {
		return dece;
	}
}
