package cn.com.xmmn.businesses.service;

import cn.com.xmmn.businesses.domain.HkNoticeAwordDO;

import java.util.List;
import java.util.Map;

/**
 * 香港成交通知单接收表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-31
 */
public interface HkNoticeAwordService {
	
	HkNoticeAwordDO get(Integer id);
	
	List<HkNoticeAwordDO> list(Map<String, Object> map);
	
	int count(Map<String, Object> map);
	
	int save(HkNoticeAwordDO hkNoticeAword);
	
	int update(HkNoticeAwordDO hkNoticeAword);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);

	/**
	 * 手动调用第三方接口获取成交通知单数据
	 * @param yearMonthStr 年月字符串，格式：yyyy-MM
	 * @return 处理结果信息
	 */
	String manualRequestBill(String yearMonthStr);

	/**
	 * 定时任务调用第三方接口获取成交通知单数据
	 * @return 处理结果信息
	 */
	String scheduledRequestBill();

	/**
	 * 调用第三方接口获取数据并处理
	 * @param yearMonthStr 年月字符串，格式：yyyy-MM
	 * @return 处理结果信息
	 */
	String requestBillData(String yearMonthStr);
}
