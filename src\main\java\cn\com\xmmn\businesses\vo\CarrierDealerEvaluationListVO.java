package cn.com.xmmn.businesses.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 承运商整体考评表
 * <AUTHOR>
 * @Date 2024年6月14日15:18:12
 */
@Data
public class CarrierDealerEvaluationListVO implements Serializable {




    //承运商
    private String carrierDealer;
    //承运商code
    private String carrierCode;

    //业务种类
    private String product;

    //业务种类code
    private String productCode;

    //统计日期（yyyy-dd）
    private String countDate;

    //承运商接收袋数：展示承运商反馈已接收邮袋总袋数
    private Integer receiveNum;

    //承运商接收重量（kg)：展示承运商反馈已接收邮袋总袋数重量
    private Double receiverTotalWeight;


    //承运商已启运袋数
    private Integer departNum;

    //承运商已启运重量（kg)：展示承运商反馈已启运邮袋总袋数重量
    private Double departTotalWeight;

    //运抵邮袋：已运抵有“抵达目的地”/“送交境外邮政”信息节点邮袋总袋数,-OP_TIME457 不为空OP_TIME505 不为空或者OP_TIME507不为空的汇总数
    private Integer arrivedMailbagNum;


    //承运商运抵重量（kg)
    private Double arrivedTotalWeight;

    //交邮袋数
    private Integer postNum;

    //交邮重量（kg)
    private Double postTotalWeight;

    //接收信息反馈率
    private Double receiverInfoFeeRate;

    //启运信息反馈率
    private Double departInfoFeeRate;

    //运抵信息反馈率
    private Double arrivedInfoFeeRate;

    //交邮信息反馈率
    private Double postInfoFeeRate;


    //启运及时率
    private Double departInfoTimelyRate;

    //运抵信息及时率
    private Double arrivedInfoTimelyRate;

    //交邮信息及时率
    private Double postInfoTimelyRate;



}
