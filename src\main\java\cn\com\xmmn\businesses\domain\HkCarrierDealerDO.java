package cn.com.xmmn.businesses.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 香港邮政邮袋接收接口表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-25 09:53:59
 */
@Data
public class HkCarrierDealerDO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//ID;批次号
	private Integer id;
	//邮袋条码
	private String bagBarcode;
	//CN38号
	private String deliverBillNo;
	//承运商
	private String carrierName;
	//容器号
	private String containerNumbers;
	//邮袋接收时间
	private Date receiceDatetime;
	//起飞时间
	private Date departureDatetime;
	//创建时间
	private Date createTime;
	private String carrierDealer;
	//创建时间
	private Date returnDatetimeStart;
}
