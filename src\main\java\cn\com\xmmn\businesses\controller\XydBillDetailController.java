package cn.com.xmmn.businesses.controller;

import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;

import cn.com.xmmn.businesses.domain.CarrierBillDetailDO;
import cn.com.xmmn.businesses.domain.XydBillDetailDO;
import cn.com.xmmn.businesses.service.XydBillDetailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import cn.com.xmmn.common.utils.PageUtils;
import cn.com.xmmn.common.utils.Query;
import cn.com.xmmn.common.utils.R;

import javax.servlet.http.HttpServletResponse;

/**
 * 新一代收入账单明细
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-20 13:12:29
 */
@Slf4j
@Controller
@RequestMapping("/check/xydBillDetail")
public class XydBillDetailController {
	@Autowired
	private XydBillDetailService xydBillDetailService;
	
	@GetMapping()
	@RequiresPermissions("check:xydBillDetail:xydBillDetail")
	String XydBillDetail(){
	    return "check/xydBillDetail/xydBillDetail";
	}
	
	@ResponseBody
	@GetMapping("/list")
	@RequiresPermissions("check:xydBillDetail:xydBillDetail")
	public PageUtils list(@RequestParam Map<String, Object> params){
		//查询列表数据
        Query query = new Query(params);
		List<XydBillDetailDO> xydBillDetailList = xydBillDetailService.list(query);
		int total = 1;
		PageUtils pageUtils = new PageUtils(xydBillDetailList,total);
		return pageUtils;
	}
	@RequestMapping(value = "/exportExcel",method = RequestMethod.GET)
	public void exportExcel(@RequestParam Map<String, Object> map, HttpServletResponse response) throws IOException {
		List<XydBillDetailDO> portbatchList = xydBillDetailService.list(map);//账单
		//创建HSSFWorkbook对象,  excel的文档对象
		HSSFWorkbook wb = new HSSFWorkbook();
		// 创建sheet
		Sheet sheet = wb.createSheet("中国快递服务有限公司国际邮件航空运输业务收入汇总表");
		//表头字体
		Font headerFont = wb.createFont();
		headerFont.setFontName("宋体");
		headerFont.setFontHeightInPoints((short) 18);
		headerFont.setBold(true);
		headerFont.setColor(Font.COLOR_NORMAL);
		//正文字体
		Font contextFont = wb.createFont();
		contextFont.setFontName("宋体");
		contextFont.setFontHeightInPoints((short) 12);
		headerFont.setBold(true);
		//表头样式，左右上下居中
		CellStyle headerStyle = wb.createCellStyle();
		headerStyle.setFont(headerFont);
		// 左右居中
		headerStyle.setAlignment(HorizontalAlignment.CENTER);
		// 上下居中
		headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		headerStyle.setLocked(true);
		// 自动换行
		headerStyle.setWrapText(false);
		//单元格样式，右对齐
		CellStyle alignRightStyle = wb.createCellStyle();
		alignRightStyle.setFont(contextFont);
		alignRightStyle.setLocked(true);
		// 自动换行
		alignRightStyle.setWrapText(false);
		alignRightStyle.setAlignment(HorizontalAlignment.RIGHT);
		//单元格样式，左右上下居中 边框
		CellStyle commonStyle = wb.createCellStyle();
		commonStyle.setFont(contextFont);
		// 左右居中
		commonStyle.setAlignment(HorizontalAlignment.CENTER);
		// 上下居中
		commonStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		commonStyle.setLocked(true);
		// 自动换行
		commonStyle.setWrapText(false);
		// 行号
		int rowNum = 0;
		//设置列宽
		for (int i = 0; i < 5; i++) {
			sheet.setColumnWidth(i, 6000);
		}
		//第一行中国快递服务有限公司核账单
		Row r0 = sheet.createRow(rowNum++);
		r0.setHeight((short) 1000);
		Cell c0 = r0.createCell(0);
		c0.setCellValue("中国快递服务有限公司\n" +
				"国际邮件航空运输业务收入汇总表");
		c0.setCellStyle(headerStyle);
		//合并单元格
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 4));
		//第二行 结算周期
		Row r1 = sheet.createRow(rowNum++);
		r1.setHeight((short) 500);
		Cell c1 = r1.createCell(0);
		c1.setCellValue("财务时期：" + map.get("dateStart"));
		c1.setCellStyle(alignRightStyle);
		//合并单元格
		sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 4));
		Row r2 = sheet.createRow(rowNum++);
		r2.setHeight((short) 500);
		String[] rowSecond = {"对方单位", "袋数", "重量（KG）", "业务收入", "备注"};

		for (int i = 0; i < rowSecond.length; i++) {
			Cell tempCell = r2.createCell(i);
			tempCell.setCellValue(rowSecond[i]);
			tempCell.setCellStyle(commonStyle);
			tempCell.setCellStyle(commonStyle);
		}
		for (int i = 0; i < portbatchList.size(); i++) {
			XydBillDetailDO vo= portbatchList.get(i);
			Row tempRow = sheet.createRow(rowNum++);
			tempRow.setRowStyle(commonStyle);
			tempRow.createCell(0).setCellValue(vo.getDanWei()!= null ? String.valueOf(vo.getDanWei()) : "");//对方单位
			tempRow.createCell(1).setCellValue(vo.getDaiShu()!= null ? String.valueOf(vo.getDaiShu()) : "");//袋数
			tempRow.createCell(2).setCellValue(vo.getWeight().toString()!= null ? String.valueOf(vo.getWeight()) : "");//重量（KG）
			tempRow.createCell(3).setCellValue(vo.getAmount().toString()!= null ? String.valueOf(vo.getAmount()) : "");//业务收入
			tempRow.createCell(4).setCellValue(vo.getAccountPeriod()!= null ? String.valueOf(vo.getAccountPeriod()) : "");//备注
		}
		//导出
		response.setContentType("application/vnd.ms-excel");
		response.setCharacterEncoding("UTF-8");
		// 文件名
		String fileName =  map.get("dateStart")  + "中国快递服务有限公司国际邮件航空运输业务收入汇总表" + ".xlsx";
		try {
			fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
		OutputStream stream = null;
		try {
			stream = response.getOutputStream();
		} catch (IOException e) {
			e.printStackTrace();
		}
		try {
			if (null != wb && null != stream) {
				wb.write(stream);
				stream.close();
			}
		} catch (Exception e) {
			log.info("excel文档导出错误-异常信息：" + e);
		}
	}
	/*@GetMapping("/add")
	@RequiresPermissions("system:xydBillDetail:add")
	String add(){
	    return "system/xydBillDetail/add";
	}

	@GetMapping("/edit/{id}")
	@RequiresPermissions("system:xydBillDetail:edit")
	String edit(@PathVariable("id") Integer id,Model model){
		XydBillDetailDO xydBillDetail = xydBillDetailService.get(id);
		model.addAttribute("xydBillDetail", xydBillDetail);
	    return "system/xydBillDetail/edit";
	}
	
	*//**
	 * 保存
	 *//*
	@ResponseBody
	@PostMapping("/save")
	@RequiresPermissions("system:xydBillDetail:add")
	public R save( XydBillDetailDO xydBillDetail){
		if(xydBillDetailService.save(xydBillDetail)>0){
			return R.ok();
		}
		return R.error();
	}
	*//**
	 * 修改
	 *//*
	@ResponseBody
	@RequestMapping("/update")
	@RequiresPermissions("system:xydBillDetail:edit")
	public R update( XydBillDetailDO xydBillDetail){
		xydBillDetailService.update(xydBillDetail);
		return R.ok();
	}
	
	*//**
	 * 删除
	 *//*
	@PostMapping( "/remove")
	@ResponseBody
	@RequiresPermissions("system:xydBillDetail:remove")
	public R remove( Integer id){
		if(xydBillDetailService.remove(id)>0){
		return R.ok();
		}
		return R.error();
	}
	
	*//**
	 * 删除
	 *//*
	@PostMapping( "/batchRemove")
	@ResponseBody
	@RequiresPermissions("system:xydBillDetail:batchRemove")
	public R remove(@RequestParam("ids[]") Integer[] ids){
		xydBillDetailService.batchRemove(ids);
		return R.ok();
	}*/
	
}
