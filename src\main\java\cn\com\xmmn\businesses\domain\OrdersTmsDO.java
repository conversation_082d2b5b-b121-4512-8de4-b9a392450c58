package cn.com.xmmn.businesses.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;



/**
 * TMS订单表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-30 20:15:07
 */
public class OrdersTmsDO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	//ID
	private Integer id;
	//乐观锁;数据版本号
	private Integer revision;
	//创建人id
	private Integer createUserId;
	//创建人
	private String createUserName;
	//创建部门id
	private Integer createDeptId;
	//创建部门
	private String createDeptName;
	//创建时间
	private Date createTime;
	//修改人id
	private Integer updateUserId;
	//修改人
	private String updateUserName;
	//修改部门id
	private Integer updateDeptId;
	//修改部门
	private String updateDeptName;
	//修改时间
	private Date updateTime;
	//运单号
	private String shipperHawbcode;
	//客户代码
	private String customerCode;
	//客户简称
	private String customerShortname;
	//客户全称
	private String customerAllname;
	//销售产品名称
	private String productCnname;
	//目的国家(中文)
	private String countryCnname;
	//服务商中文名称
	private String serverAllname;
	//服务渠道名称
	private String serverChannelCnname;
	//到货时间
	private Date arrivalDate;
	//签入时间
	private Date checkinDate;
	//签出时间
	private Date checkoutDate;
	//申报品名(中文汇总)
	private String allInvoiceCnname;
	//件数
	private Integer pieces;
	//收货实重
	private BigDecimal checkinGrossweight;
	//收货材积重
	private BigDecimal checkinVolumeweight;
	//客户重量
	private BigDecimal shipperWeight;
	//收货计费重
	private BigDecimal shipperChargeweight;
	//服务商重量
	private BigDecimal serverChargeweight;
	//运费金额(RMB)
	private BigDecimal rmbFee;
	//其他费用(RMB)
	private BigDecimal rmbOtherFee;
	//总应收金额(RMB)
	private BigDecimal rmbTotalReceivableFee;

	/**
	 * 设置：ID
	 */
	public void setId(Integer id) {
		this.id = id;
	}
	/**
	 * 获取：ID
	 */
	public Integer getId() {
		return id;
	}
	/**
	 * 设置：乐观锁;数据版本号
	 */
	public void setRevision(Integer revision) {
		this.revision = revision;
	}
	/**
	 * 获取：乐观锁;数据版本号
	 */
	public Integer getRevision() {
		return revision;
	}
	/**
	 * 设置：创建人id
	 */
	public void setCreateUserId(Integer createUserId) {
		this.createUserId = createUserId;
	}
	/**
	 * 获取：创建人id
	 */
	public Integer getCreateUserId() {
		return createUserId;
	}
	/**
	 * 设置：创建人
	 */
	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}
	/**
	 * 获取：创建人
	 */
	public String getCreateUserName() {
		return createUserName;
	}
	/**
	 * 设置：创建部门id
	 */
	public void setCreateDeptId(Integer createDeptId) {
		this.createDeptId = createDeptId;
	}
	/**
	 * 获取：创建部门id
	 */
	public Integer getCreateDeptId() {
		return createDeptId;
	}
	/**
	 * 设置：创建部门
	 */
	public void setCreateDeptName(String createDeptName) {
		this.createDeptName = createDeptName;
	}
	/**
	 * 获取：创建部门
	 */
	public String getCreateDeptName() {
		return createDeptName;
	}
	/**
	 * 设置：创建时间
	 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	/**
	 * 获取：创建时间
	 */
	public Date getCreateTime() {
		return createTime;
	}
	/**
	 * 设置：修改人id
	 */
	public void setUpdateUserId(Integer updateUserId) {
		this.updateUserId = updateUserId;
	}
	/**
	 * 获取：修改人id
	 */
	public Integer getUpdateUserId() {
		return updateUserId;
	}
	/**
	 * 设置：修改人
	 */
	public void setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
	}
	/**
	 * 获取：修改人
	 */
	public String getUpdateUserName() {
		return updateUserName;
	}
	/**
	 * 设置：修改部门id
	 */
	public void setUpdateDeptId(Integer updateDeptId) {
		this.updateDeptId = updateDeptId;
	}
	/**
	 * 获取：修改部门id
	 */
	public Integer getUpdateDeptId() {
		return updateDeptId;
	}
	/**
	 * 设置：修改部门
	 */
	public void setUpdateDeptName(String updateDeptName) {
		this.updateDeptName = updateDeptName;
	}
	/**
	 * 获取：修改部门
	 */
	public String getUpdateDeptName() {
		return updateDeptName;
	}
	/**
	 * 设置：修改时间
	 */
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	/**
	 * 获取：修改时间
	 */
	public Date getUpdateTime() {
		return updateTime;
	}
	/**
	 * 设置：运单号
	 */
	public void setShipperHawbcode(String shipperHawbcode) {
		this.shipperHawbcode = shipperHawbcode;
	}
	/**
	 * 获取：运单号
	 */
	public String getShipperHawbcode() {
		return shipperHawbcode;
	}
	/**
	 * 设置：客户代码
	 */
	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}
	/**
	 * 获取：客户代码
	 */
	public String getCustomerCode() {
		return customerCode;
	}
	/**
	 * 设置：客户简称
	 */
	public void setCustomerShortname(String customerShortname) {
		this.customerShortname = customerShortname;
	}
	/**
	 * 获取：客户简称
	 */
	public String getCustomerShortname() {
		return customerShortname;
	}
	/**
	 * 设置：客户全称
	 */
	public void setCustomerAllname(String customerAllname) {
		this.customerAllname = customerAllname;
	}
	/**
	 * 获取：客户全称
	 */
	public String getCustomerAllname() {
		return customerAllname;
	}
	/**
	 * 设置：销售产品名称
	 */
	public void setProductCnname(String productCnname) {
		this.productCnname = productCnname;
	}
	/**
	 * 获取：销售产品名称
	 */
	public String getProductCnname() {
		return productCnname;
	}
	/**
	 * 设置：目的国家(中文)
	 */
	public void setCountryCnname(String countryCnname) {
		this.countryCnname = countryCnname;
	}
	/**
	 * 获取：目的国家(中文)
	 */
	public String getCountryCnname() {
		return countryCnname;
	}
	/**
	 * 设置：服务商中文名称
	 */
	public void setServerAllname(String serverAllname) {
		this.serverAllname = serverAllname;
	}
	/**
	 * 获取：服务商中文名称
	 */
	public String getServerAllname() {
		return serverAllname;
	}
	/**
	 * 设置：服务渠道名称
	 */
	public void setServerChannelCnname(String serverChannelCnname) {
		this.serverChannelCnname = serverChannelCnname;
	}
	/**
	 * 获取：服务渠道名称
	 */
	public String getServerChannelCnname() {
		return serverChannelCnname;
	}
	/**
	 * 设置：到货时间
	 */
	public void setArrivalDate(Date arrivalDate) {
		this.arrivalDate = arrivalDate;
	}
	/**
	 * 获取：到货时间
	 */
	public Date getArrivalDate() {
		return arrivalDate;
	}
	/**
	 * 设置：签入时间
	 */
	public void setCheckinDate(Date checkinDate) {
		this.checkinDate = checkinDate;
	}
	/**
	 * 获取：签入时间
	 */
	public Date getCheckinDate() {
		return checkinDate;
	}
	/**
	 * 设置：签出时间
	 */
	public void setCheckoutDate(Date checkoutDate) {
		this.checkoutDate = checkoutDate;
	}
	/**
	 * 获取：签出时间
	 */
	public Date getCheckoutDate() {
		return checkoutDate;
	}
	/**
	 * 设置：申报品名(中文汇总)
	 */
	public void setAllInvoiceCnname(String allInvoiceCnname) {
		this.allInvoiceCnname = allInvoiceCnname;
	}
	/**
	 * 获取：申报品名(中文汇总)
	 */
	public String getAllInvoiceCnname() {
		return allInvoiceCnname;
	}
	/**
	 * 设置：件数
	 */
	public void setPieces(Integer pieces) {
		this.pieces = pieces;
	}
	/**
	 * 获取：件数
	 */
	public Integer getPieces() {
		return pieces;
	}
	/**
	 * 设置：收货实重
	 */
	public void setCheckinGrossweight(BigDecimal checkinGrossweight) {
		this.checkinGrossweight = checkinGrossweight;
	}
	/**
	 * 获取：收货实重
	 */
	public BigDecimal getCheckinGrossweight() {
		return checkinGrossweight;
	}
	/**
	 * 设置：收货材积重
	 */
	public void setCheckinVolumeweight(BigDecimal checkinVolumeweight) {
		this.checkinVolumeweight = checkinVolumeweight;
	}
	/**
	 * 获取：收货材积重
	 */
	public BigDecimal getCheckinVolumeweight() {
		return checkinVolumeweight;
	}
	/**
	 * 设置：客户重量
	 */
	public void setShipperWeight(BigDecimal shipperWeight) {
		this.shipperWeight = shipperWeight;
	}
	/**
	 * 获取：客户重量
	 */
	public BigDecimal getShipperWeight() {
		return shipperWeight;
	}
	/**
	 * 设置：收货计费重
	 */
	public void setShipperChargeweight(BigDecimal shipperChargeweight) {
		this.shipperChargeweight = shipperChargeweight;
	}
	/**
	 * 获取：收货计费重
	 */
	public BigDecimal getShipperChargeweight() {
		return shipperChargeweight;
	}
	/**
	 * 设置：服务商重量
	 */
	public void setServerChargeweight(BigDecimal serverChargeweight) {
		this.serverChargeweight = serverChargeweight;
	}
	/**
	 * 获取：服务商重量
	 */
	public BigDecimal getServerChargeweight() {
		return serverChargeweight;
	}
	/**
	 * 设置：运费金额(RMB)
	 */
	public void setRmbFee(BigDecimal rmbFee) {
		this.rmbFee = rmbFee;
	}
	/**
	 * 获取：运费金额(RMB)
	 */
	public BigDecimal getRmbFee() {
		return rmbFee;
	}
	/**
	 * 设置：其他费用(RMB)
	 */
	public void setRmbOtherFee(BigDecimal rmbOtherFee) {
		this.rmbOtherFee = rmbOtherFee;
	}
	/**
	 * 获取：其他费用(RMB)
	 */
	public BigDecimal getRmbOtherFee() {
		return rmbOtherFee;
	}
	/**
	 * 设置：总应收金额(RMB)
	 */
	public void setRmbTotalReceivableFee(BigDecimal rmbTotalReceivableFee) {
		this.rmbTotalReceivableFee = rmbTotalReceivableFee;
	}
	/**
	 * 获取：总应收金额(RMB)
	 */
	public BigDecimal getRmbTotalReceivableFee() {
		return rmbTotalReceivableFee;
	}
}
