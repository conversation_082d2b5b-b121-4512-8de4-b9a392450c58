# XSS防护使用示例

## 概述

本文档提供了XSS防护工具类的详细使用示例，帮助开发者在项目中正确使用XSS防护功能。

## 🛠️ 可用的XSS防护工具

### 1. XssUtil - 静态工具类
- `XssUtil.clean(input)` - 基础XSS清洗
- `XssUtil.cleanStrict(input)` - 严格XSS清洗
- `XssUtil.cleanFileName(filename)` - 文件名清洗
- `XssUtil.htmlEncode(content)` - HTML编码
- `XssUtil.urlEncode(content)` - URL编码

### 2. XssProtection - Spring组件
- 注入使用：`@Autowired private XssProtection xssProtection;`
- 提供相同功能的实例方法

## 📋 使用示例

### 1. 控制器中的基本使用

```java
@Controller
@RequestMapping("/example")
public class ExampleController {
    
    @Autowired
    private XssProtection xssProtection;
    
    /**
     * 方式1：使用静态工具类
     */
    @GetMapping("/search1")
    public String search1(HttpServletRequest request, Model model) {
        // 清洗单个参数
        String keyword = XssUtil.clean(request.getParameter("keyword"));
        String category = XssUtil.clean(request.getParameter("category"));
        
        model.addAttribute("keyword", keyword);
        model.addAttribute("category", category);
        
        return "search/result";
    }
    
    /**
     * 方式2：使用Spring组件
     */
    @GetMapping("/search2")
    public String search2(HttpServletRequest request, Model model) {
        // 清洗单个参数
        String keyword = xssProtection.sanitizeInput(request.getParameter("keyword"));
        String category = xssProtection.sanitizeInput(request.getParameter("category"));
        
        model.addAttribute("keyword", keyword);
        model.addAttribute("category", category);
        
        return "search/result";
    }
    
    /**
     * 方式3：批量清洗所有参数
     */
    @GetMapping("/search3")
    public String search3(HttpServletRequest request, Model model) {
        // 批量清洗所有请求参数
        Map<String, String> cleanParams = XssUtil.cleanAll(request);
        
        model.addAttribute("keyword", cleanParams.get("keyword"));
        model.addAttribute("category", cleanParams.get("category"));
        
        return "search/result";
    }
}
```

### 2. POST请求处理

```java
@Controller
@RequestMapping("/user")
public class UserController {
    
    @Autowired
    private XssProtection xssProtection;
    
    /**
     * 保存用户信息 - 手动清洗
     */
    @PostMapping("/save")
    public R saveUser(HttpServletRequest request) {
        // 清洗用户输入
        String username = XssUtil.clean(request.getParameter("username"));
        String email = XssUtil.clean(request.getParameter("email"));
        String description = XssUtil.cleanStrict(request.getParameter("description")); // 严格清洗
        
        // 验证输入安全性
        if (!XssUtil.isSafe(username) || !XssUtil.isSafe(email)) {
            return R.error("输入包含不安全内容");
        }
        
        // 保存用户...
        
        return R.ok();
    }
    
    /**
     * 保存用户信息 - 使用组件
     */
    @PostMapping("/save2")
    public R saveUser2(HttpServletRequest request) {
        try {
            // 使用检查并清洗方法
            String username = xssProtection.checkAndSanitize(request.getParameter("username"), false);
            String email = xssProtection.checkAndSanitize(request.getParameter("email"), false);
            String description = xssProtection.checkAndSanitize(request.getParameter("description"), true); // 严格模式
            
            // 保存用户...
            
            return R.ok();
        } catch (SecurityException e) {
            return R.error("输入验证失败: " + xssProtection.getSafeErrorMessage(e.getMessage()));
        }
    }
}
```

### 3. 文件上传处理

```java
@Controller
@RequestMapping("/file")
public class FileController {
    
    @PostMapping("/upload")
    public R uploadFile(@RequestParam("file") MultipartFile file, 
                       HttpServletRequest request) {
        
        // 清洗文件名
        String originalFilename = file.getOriginalFilename();
        String safeFilename = XssUtil.cleanFileName(originalFilename);
        
        // 清洗其他参数
        String description = XssUtil.clean(request.getParameter("description"));
        String category = XssUtil.clean(request.getParameter("category"));
        
        // 验证文件名安全性
        if (!XssUtil.isSafe(safeFilename)) {
            return R.error("文件名包含不安全字符");
        }
        
        // 处理文件上传...
        
        return R.ok();
    }
}
```

### 4. API接口处理

```java
@RestController
@RequestMapping("/api")
public class ApiController {
    
    @Autowired
    private XssProtection xssProtection;
    
    /**
     * 处理JSON数据
     */
    @PostMapping("/data")
    public ApiResponse processData(@RequestBody Map<String, Object> data) {
        try {
            // 批量检查并清洗Map数据
            Map<String, Object> safeData = xssProtection.checkAndSanitizeMap(data, false);
            
            // 处理数据...
            
            return ApiResponse.success(safeData);
        } catch (SecurityException e) {
            return ApiResponse.error(xssProtection.getSafeErrorMessage(e.getMessage()));
        }
    }
    
    /**
     * 处理列表数据
     */
    @PostMapping("/list")
    public ApiResponse processList(@RequestBody List<String> items) {
        // 清洗列表中的所有字符串
        List<String> safeItems = XssUtil.cleanList(items);
        
        // 验证所有项目的安全性
        for (String item : safeItems) {
            if (!XssUtil.isSafe(item)) {
                return ApiResponse.error("列表中包含不安全内容");
            }
        }
        
        // 处理列表...
        
        return ApiResponse.success(safeItems);
    }
}
```

### 5. 错误处理

```java
@ControllerAdvice
public class GlobalExceptionHandler {
    
    @Autowired
    private XssProtection xssProtection;
    
    @ExceptionHandler(Exception.class)
    public R handleException(Exception e) {
        // 获取安全的错误消息
        String safeMessage = xssProtection.getSafeErrorMessage(e.getMessage());
        
        return R.error(safeMessage);
    }
    
    @ExceptionHandler(SecurityException.class)
    public R handleSecurityException(SecurityException e) {
        // 安全异常的特殊处理
        String safeMessage = XssUtil.getSafeErrorMessage(e.getMessage());
        
        return R.error("安全验证失败: " + safeMessage);
    }
}
```

### 6. 输出编码

```java
@Controller
public class OutputController {
    
    @GetMapping("/display")
    public String displayContent(Model model, HttpServletRequest request) {
        String userContent = request.getParameter("content");
        
        // 用于HTML输出的编码
        String htmlSafeContent = XssUtil.htmlEncode(userContent);
        model.addAttribute("htmlContent", htmlSafeContent);
        
        // 用于URL的编码
        String urlSafeContent = XssUtil.urlEncode(userContent);
        model.addAttribute("urlContent", urlSafeContent);
        
        return "display";
    }
}
```

## 🎯 最佳实践

### 1. 输入验证优先
```java
// 先验证，再清洗
if (!XssUtil.isSafe(input)) {
    throw new SecurityException("输入不安全");
}
String cleanInput = XssUtil.clean(input);
```

### 2. 根据上下文选择清洗级别
```java
// 普通文本使用基础清洗
String title = XssUtil.clean(request.getParameter("title"));

// 富文本或敏感内容使用严格清洗
String content = XssUtil.cleanStrict(request.getParameter("content"));

// 文件名使用专门的文件名清洗
String filename = XssUtil.cleanFileName(file.getOriginalFilename());
```

### 3. 批量处理提高效率
```java
// 批量清洗所有参数
Map<String, String> cleanParams = XssUtil.cleanAll(request);

// 批量清洗Map数据
Map<String, Object> cleanData = XssUtil.cleanMap(originalData);
```

### 4. 输出编码防止XSS
```java
// HTML输出编码
String htmlOutput = XssUtil.htmlEncode(userInput);

// URL输出编码
String urlOutput = XssUtil.urlEncode(userInput);
```

## ⚠️ 注意事项

1. **不要过度清洗**：根据实际需要选择合适的清洗级别
2. **保留原始数据**：在某些情况下可能需要原始输入用于日志记录
3. **性能考虑**：对于大量数据，考虑使用批量处理方法
4. **错误处理**：始终处理SecurityException异常
5. **日志记录**：重要的安全事件应该记录日志

## 🔧 配置选项

可以在 `application.yml` 中配置XSS防护参数：

```yaml
security:
  xss:
    enabled: true
    strict-mode: false
    log-attacks: true
    max-input-length: 10000
    sql-injection-detection: true
    path-traversal-detection: true
    whitelist-paths:
      - "/static/**"
      - "/api/export/**"
    whitelist-params:
      - "export"
      - "download"
```

---

**通过使用这些XSS防护工具，可以有效防止XSS攻击，同时让安全扫描工具能够识别到明确的防护措施。**
