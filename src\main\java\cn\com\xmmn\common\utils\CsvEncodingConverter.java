//package cn.com.xmmn.common.utils;
//
//import org.mozilla.intl.chardet.nsDetector;
//import org.springframework.web.multipart.MultipartFile;
//
//import java.io.IOException;
//import java.io.InputStream;
//
//public class CsvEncodingConverter {
//
//
//    private boolean found = false;
//    private String encoding = null;
//
//    /**
//     * 传入一个文件(File)对象，检查文件编码
//     *
//     * @param file File对象实例
//     * @return 文件编码，若无，则返回null
//     * @throws IOException
//     */
//    public String guessFileEncoding(MultipartFile file) throws IOException {
//        return guessFileEncoding(file, new nsDetector());
//    }
//
//    public String guessFileEncoding(InputStream imp) throws IOException {
//        nsDetector det=new nsDetector();
//        byte[] buf = new byte[1024];
//        int len;
//        boolean done;
//        boolean isAscii = false;
//
//        while ((len = imp.read(buf, 0, buf.length)) != -1) {
//            // Check if the stream is only ascii.
//            isAscii = det.isAscii(buf, len);
//            if (isAscii) {
//                break;
//            }
//            // DoIt if non-ascii and not done yet.
//            done = det.DoIt(buf, len, false);
//            if (done) {
//                break;
//            }
//        }
//        imp.close();
//        det.DataEnd();
//
//        if (isAscii) {
//            encoding = "ASCII";
//            found = true;
//        }
//
//        if (!found) {
//            String[] prob = det.getProbableCharsets();
//            //可能有多个但只取第一个
//            if (prob.length > 0) {
//                // 在没有发现情况下,也可以只取第一个可能的编码,这里返回的是一个可能的序列
//                //return encoding;
//                return prob[0];
//            } else {
//                return null;
//            }
//        }
//        return encoding;
//    }
//    /**
//     * 获取文件的编码
//     *
//     * @param file
//     * @param det
//     * @return
//     * @throws IOException
//     */
//    private String guessFileEncoding(MultipartFile file, nsDetector det) throws IOException {
//
//        InputStream imp = file.getInputStream();
//        byte[] buf = new byte[1024];
//        int len;
//        boolean done;
//        boolean isAscii = false;
//
//        while ((len = imp.read(buf, 0, buf.length)) != -1) {
//            // Check if the stream is only ascii.
//            isAscii = det.isAscii(buf, len);
//            if (isAscii) {
//                break;
//            }
//            // DoIt if non-ascii and not done yet.
//            done = det.DoIt(buf, len, false);
//            if (done) {
//                break;
//            }
//        }
//        imp.close();
//        det.DataEnd();
//
//        if (isAscii) {
//            encoding = "ASCII";
//            found = true;
//        }
//
//        if (!found) {
//            String[] prob = det.getProbableCharsets();
//            //可能有多个但只取第一个
//            if (prob.length > 0) {
//                // 在没有发现情况下,也可以只取第一个可能的编码,这里返回的是一个可能的序列
//                //return encoding;
//                return prob[0];
//            } else {
//                return null;
//            }
//        }
//        return encoding;
//    }
//}
