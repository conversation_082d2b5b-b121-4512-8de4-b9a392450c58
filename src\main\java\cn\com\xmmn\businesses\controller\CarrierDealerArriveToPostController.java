package cn.com.xmmn.businesses.controller;

import cn.com.xmmn.businesses.domain.CarrierDealerRecivDO;
import cn.com.xmmn.businesses.dt.CarrierDealerRecivDetailDT;
import cn.com.xmmn.businesses.service.CarrierDealerArriveToPostService;
import cn.com.xmmn.businesses.service.CarrierDealerDepartService;
import cn.com.xmmn.businesses.vo.*;
import cn.com.xmmn.common.controller.BaseController;
import cn.com.xmmn.common.utils.PageUtils;
import cn.com.xmmn.common.utils.Query;
import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 运抵至交邮时限情况统计
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-13 22:18:55
 */
@Slf4j
@Controller
@RequestMapping("/carrierDealerArriveToPost/monitor")
public class CarrierDealerArriveToPostController extends BaseController {

    @Autowired
    private CarrierDealerArriveToPostService carrierDealerArriveToPostService;

    @GetMapping()
    @RequiresPermissions("carrierDealerArriveToPost:carrierDealerArriveToPost")
    String carrierDealerDepart() {
        return "carrierDealerArriveToPost/list";
    }



    /**
     * 分组数据
     * @param params
     * @return
     */
    @ResponseBody
    @GetMapping("/group")
    @RequiresPermissions("carrierDealerArriveToPost:carrierDealerArriveToPost")
    public PageUtils group(@RequestParam Map<String, Object> params) {
        log.info("运抵至交邮时限情况统计参数:{}" , params);
        //查询列表数据
        Query query = new Query(params);
        List<CarrierDealerArriveToPostListVo> carrierDealerArriveToPostListVos = carrierDealerArriveToPostService.group(query);
        List<CarrierDealerArriveToPostListVo> carrierDealerArriveToPostCountListVos = carrierDealerArriveToPostService.groupCount(query);
        Integer total = carrierDealerArriveToPostCountListVos.get(0).getTotal();
        PageUtils pageUtils = new PageUtils(carrierDealerArriveToPostListVos, total);
        pageUtils.setDepartNum(carrierDealerArriveToPostCountListVos.get(0).getDepartNum());
        pageUtils.setArriveNum(carrierDealerArriveToPostCountListVos.get(0).getArriveNum());
        pageUtils.setPostNum(carrierDealerArriveToPostCountListVos.get(0).getPostNum());
        pageUtils.setNormalPostNum(carrierDealerArriveToPostCountListVos.get(0).getNormalPostNum());
        pageUtils.setOverPostNum(carrierDealerArriveToPostCountListVos.get(0).getOverPostNum());
        pageUtils.setNotPostNum(carrierDealerArriveToPostCountListVos.get(0).getNotPostNum());
        pageUtils.setExceptionBagNum(carrierDealerArriveToPostCountListVos.get(0).getExceptionBagNum());


        return pageUtils;

    }

    /**
     * 跳转详情页列表
     * @return
     */
    @GetMapping("/detailPage")
    @RequiresPermissions("carrierDealerArriveToPost:carrierDealerArriveToPost")
    public String detailPage(HttpServletRequest request, Model model) {
        //点击的是哪个明细。
        String queryParameter = request.getParameter("queryParameter");
        model.addAttribute("queryParameter", queryParameter);

        String orgCode = request.getParameter("orgCode");
        String orgCodeStr = request.getParameter("orgCodeStr");
        String carrierCode = request.getParameter("carrierCode");
        String productCode = request.getParameter("productCode");
        String receiverCountryCode = request.getParameter("receiverCountryCode");
        String receiverCountry = request.getParameter("receiverCountry");
        String opTime538Start = request.getParameter("opTime538Start");
        String opTime538End = request.getParameter("opTime538End");
        String oeDest = request.getParameter("oeDest");

        String provinceCodeStr = request.getParameter("provinceCodeStr");

        model.addAttribute("provinceCodeStr", provinceCodeStr);
        model.addAttribute("orgCodeStr", orgCodeStr);
        model.addAttribute("opTime538Start", opTime538Start);
        model.addAttribute("opTime538End", opTime538End);

        model.addAttribute("provinceCodeStr", provinceCodeStr);
        model.addAttribute("orgCode", orgCode);
        model.addAttribute("carrierCode", carrierCode);
        model.addAttribute("productCode", productCode);
        model.addAttribute("oeDest", oeDest);
        model.addAttribute("receiverCountryCode", receiverCountryCode);
        model.addAttribute("receiverCountry", receiverCountry);
        model.addAttribute("opTime538Start", opTime538Start);
        model.addAttribute("opTime538End", opTime538End);
        return "carrierDealerArriveToPost/details";
    }

    @ResponseBody
    @GetMapping("/detailTable")
    @RequiresPermissions("carrierDealerArriveToPost:carrierDealerArriveToPost")
    public PageUtils detailTable(CarrierDealerRecivDetailDT dealerReceiveDetailDT) {
        log.info("运抵至交邮时限情况统计参数:{}" , dealerReceiveDetailDT);
        List<CarrierDealerRecivDO> carrierDealerRecivList = carrierDealerArriveToPostService.detail(dealerReceiveDetailDT);
        Integer total = carrierDealerArriveToPostService.detailCount(dealerReceiveDetailDT);
        PageUtils pageUtils = new PageUtils(carrierDealerRecivList, total);
        return pageUtils;

    }

    /**
     * 导出数据
     *
     * @param map
     * @param response 入参
     * @return void
     **/
    @RequestMapping(value = "/exportExcel", method = RequestMethod.GET)
    public void exportExcel(@RequestParam Map<String, Object> map, HttpServletResponse response) throws IOException {

        List<CarrierDealerArriveToPostListVo> carrierDealerArriveToPostListVos = carrierDealerArriveToPostService.group(map);
        List<CarrierDealerArriveToPostListVo> carrierDealerArriveToPostCountListVos = carrierDealerArriveToPostService.groupCount(map);

        //导出
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");
        // 文件名
        String fileName = map.get("dateStart") + "承运商接收情况统计表" + ".xlsx";
        try {
            fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
        try {
            CarrierDealerArriveToPostListVo newRecord = new CarrierDealerArriveToPostListVo();

            newRecord.setCarrierDealer("合计");
            newRecord.setDepartNum(carrierDealerArriveToPostCountListVos.get(0).getDepartNum());
            newRecord.setArriveNum(carrierDealerArriveToPostCountListVos.get(0).getArriveNum());
            newRecord.setPostNum(carrierDealerArriveToPostCountListVos.get(0).getPostNum());
            newRecord.setNormalPostNum(carrierDealerArriveToPostCountListVos.get(0).getNormalPostNum());
            newRecord.setOverPostNum(carrierDealerArriveToPostCountListVos.get(0).getOverPostNum());
            newRecord.setNotPostNum(carrierDealerArriveToPostCountListVos.get(0).getNotPostNum());
            newRecord.setExceptionBagNum(carrierDealerArriveToPostCountListVos.get(0).getExceptionBagNum());
            carrierDealerArriveToPostListVos.add(newRecord);
            //导出
            EasyExcel.write(response.getOutputStream(), CarrierDealerArriveToPostListVo.class).sheet("承运商接收情况统计表").doWrite(carrierDealerArriveToPostListVos);
        } catch (Exception e) {
            log.info("excel文档导出错误-异常信息：" + e);
            e.printStackTrace();
        }

    }


    @RequestMapping(value = "/exportDetailExcel",method = RequestMethod.GET)
    public void exportDetailExcel(CarrierDealerRecivDetailDT dealerReceiveDetailDT, HttpServletResponse response) throws IOException {
        List<CarrierDealerRecivDO> portbatchList = carrierDealerArriveToPostService.detail(dealerReceiveDetailDT);


        //导出
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");
        // 文件名
        String fileName =   "承运商运抵至交邮时限明细" + ".xlsx";
        try {
            fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");


        try {
            List<CarrierDealerArriveToPostDetailListVo> exportList = new ArrayList<>();
            for (CarrierDealerRecivDO portbatch : portbatchList) {
                CarrierDealerArriveToPostDetailListVo vo = new CarrierDealerArriveToPostDetailListVo();
                BeanUtils.copyProperties(portbatch, vo);
                exportList.add(vo);
            }
            //导出
            EasyExcel.write(response.getOutputStream(), CarrierDealerArriveToPostDetailListVo.class)
                    //.registerWriteHandler(customHeaderHandler)
                    .sheet("承运商运抵至交邮时限明细")
                    .doWrite(exportList);
        } catch (Exception e) {
            log.info("excel文档导出错误-异常信息：" + e);
            e.printStackTrace();
        }
    }

}
