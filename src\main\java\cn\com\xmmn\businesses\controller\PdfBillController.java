package cn.com.xmmn.businesses.controller;

import cn.com.xmmn.businesses.common.EmailTypeEnum;
import cn.com.xmmn.businesses.domain.ManualPriceDO;
import cn.com.xmmn.businesses.domain.PdfBillDO;
import cn.com.xmmn.businesses.service.PdfBillService;
import cn.com.xmmn.common.config.SimpleDemoConfig;
import cn.com.xmmn.common.controller.BaseController;
import cn.com.xmmn.common.utils.*;
import com.aspose.pdf.Document;
import com.aspose.pdf.SaveFormat;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.usermodel.*;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * 常规账单pdf转excel
 * 
 * <AUTHOR>

 */
 @Slf4j
@Controller
@RequestMapping("/businesses/onepdf")
public class PdfBillController extends BaseController {

	@Autowired
	private SimpleDemoConfig simpleDemoConfig;

	@Autowired
	private PdfBillService pdfBillService;

	@GetMapping()
	@RequiresPermissions("businesses:batch:batch")
	String Batch(){
	    return "businesses/pdfbill/pdfBill";
	}

	/**
	 *   接受文件   解析  上传资料。
	 *   /admin/client/upload_excel
	 */
	@PostMapping("/uploadPdfToExcel")
	public void uploadPdfToExcel(MultipartFile pdffile, String modelType, Model model, HttpServletResponse response)throws Exception {
		log.info("==pdf转换==========================================================");
		log.info("originalFilename={}",pdffile.getOriginalFilename());
		log.info("fileSize={}",pdffile.getSize());

		// 创建日期目录
		String dateDir = DateUtils.format(new Date(),"yyyyMMdd") + File.separator;
		String uploadPath = simpleDemoConfig.getUploadPath() + dateDir;
		
		// 使用安全文件上传工具类上传PDF文件
		String fileName = SecureFileUploadUtil.uploadFileSafely(pdffile, uploadPath);
		
		log.info("filename={}",fileName);
		log.info("pdf文件保存成功。filename={}",fileName);

		//2-pdf文件转excel
		long old = System.currentTimeMillis();
		String excelPath = "";
		try {
			// 获取不含扩展名的文件名
			String fileNameWithoutExt = fileName.substring(0, fileName.lastIndexOf("."));
			excelPath = uploadPath + fileNameWithoutExt + ".xlsx";
			
			// 验证excel路径安全性
			File excelFile = new File(excelPath);
			File uploadDir = new File(uploadPath).getCanonicalFile();
			if (!excelFile.getCanonicalPath().startsWith(uploadDir.getCanonicalPath())) {
				throw new SecurityException("检测到路径遍历攻击尝试");
			}
			
			FileOutputStream os = new FileOutputStream(excelPath);
			Document doc = new Document(uploadPath + fileName);
			doc.save(os, SaveFormat.Excel);
			os.close();
			long now = System.currentTimeMillis();
			log.info("Pdf 转 EXCEL 共耗时：" + ((now - old) / 1000.0) + "秒");//一页一秒左右
		} catch (Exception e) {
			log.error("Pdf 转 EXCEL 失败...", e);
			throw new Exception("PDF转Excel失败: " + e.getMessage());
		}

		//3-读取excel============================================================================================
/*		如果是.xlsx格式，使用HSSFWorkbook，HSSFSheet，HSSFRow来进行相关操作
		如果是.xlsx格式，使用XSSFWorkbook，XSSFSheet，XSSFRow来进行相关操作*/
		File excelFile = new File(excelPath);    //获取文件后缀名
		InputStream inputStream = null;
		XSSFWorkbook workbook = null;
		List<PdfBillDO> datalist = new ArrayList<>();
		List<String> portIdList = new ArrayList<>();//用作查询价格表
		List<String> flightNoList = new ArrayList<>();//用作查询价格表
		try {
			inputStream = new FileInputStream(excelFile);
			workbook = new XSSFWorkbook(inputStream);
			int sheetNum=workbook.getNumberOfSheets();

			for(int i=0;i<sheetNum;i++){
				log.info("开始处理第{}个sheet============",i+1);
				XSSFSheet sheet=workbook.getSheetAt(i);

				/*
				航班信息
				*/
				//先将获取的单元格设置为String类型，下面使用getStringCellValue获取单元格内容
				//如果不设置为String类型，如果单元格是数字，则报如下异常
				//java.lang.IllegalStateException: Cannot get a STRING value from a NUMERIC cell
				sheet.getRow(2).getCell(0).setCellType(CellType.STRING);
				//读取单元格内容//出发地=HONG KONG Office of origin of the bill
				String originAddress = sheet.getRow(2).getCell(0).getStringCellValue();
				log.info("出发地={}",originAddress);

				sheet.getRow(7).getCell(0).setCellType(CellType.STRING);
				//读取单元格内容//航班号=CX506
				String flightNO = sheet.getRow(7).getCell(0).getStringCellValue().trim();
				log.info("航班号={}",flightNO);
				flightNoList.add(flightNO);

				sheet.getRow(7).getCell(3).setCellType(CellType.STRING);
				//读取单元格内容//发运日期=01/06/22
				String departureDate = sheet.getRow(7).getCell(3).getStringCellValue();
				log.info("发运日期={}",departureDate);

				sheet.getRow(9).getCell(2).setCellType(CellType.STRING);
				//读取单元格内容//目的地=[KIX] OSAKA
				String destinationAddress = sheet.getRow(9).getCell(2).getStringCellValue();
				log.info("目的地={}",destinationAddress);
				destinationAddress=destinationAddress.substring(destinationAddress.indexOf("[")+1,destinationAddress.indexOf("]"));
				portIdList.add(destinationAddress);
				/*
				明细信息
				*/
				//确定明细数据起始行数-----------------------------------------------------------------
				int beginRow=15;
				sheet.getRow(beginRow).getCell(0).setCellType(CellType.STRING);
				//读取单元格内容
				String thead = sheet.getRow(beginRow).getCell(0).getStringCellValue();
				log.info("16行表头thead={}",thead);
				while(!thead.contains("Mail No")&&!thead.contains("Dispatching office")){
					beginRow++;
					sheet.getRow(beginRow).getCell(0).setCellType(CellType.STRING);
					//读取单元格内容
					thead = sheet.getRow(beginRow).getCell(0).getStringCellValue();
				}
				if(thead.contains("Dispatching office")){
					continue;//此sheet无数据，跳过循环，处理下一个sheet
				}
				if(thead.contains("Mail No")){
					beginRow+=2;
				}
				log.info("第{}行开始读取数据",beginRow+1);//
				//循环读取明细数据-----------------------------------------------------------------
				sheet.getRow(beginRow).getCell(0).setCellType(CellType.STRING);
				//读取单元格内容
				String mailType = sheet.getRow(beginRow).getCell(0).getStringCellValue();
				log.info("mailType={}",mailType);//
				while(!mailType.contains("Dispatching office")){
					if(StringUtils.isBlank(mailType)){//跳过空号、合计行
						beginRow++;
						mailType = sheet.getRow(beginRow).getCell(0).getStringCellValue();
						continue;
					}
					//读取:总包号
					sheet.getRow(beginRow).getCell(1).setCellType(CellType.STRING);
					String packageNo = sheet.getRow(beginRow).getCell(1).getStringCellValue();

					//读取:原寄局
					sheet.getRow(beginRow).getCell(2).setCellType(CellType.STRING);
					String originOffice = sheet.getRow(beginRow).getCell(2).getStringCellValue();

					//读取:寄达局
					sheet.getRow(beginRow).getCell(3).setCellType(CellType.STRING);
					String destinationOffice = sheet.getRow(beginRow).getCell(3).getStringCellValue();

					//读取:重量
					sheet.getRow(beginRow).getCell(11).setCellType(CellType.STRING);
					String weight = sheet.getRow(beginRow).getCell(11).getStringCellValue();

					//读取:袋号
					sheet.getRow(beginRow).getCell(12).setCellType(CellType.STRING);
					String bagNo = sheet.getRow(beginRow).getCell(12).getStringCellValue();

					PdfBillDO pdfBillDO=new PdfBillDO();
					//出发地：香港
					pdfBillDO.setOriginAddress("香港");
					//航班号：CX056
					pdfBillDO.setFlightNO(flightNO);
					//发运日期:01/06/22
					pdfBillDO.setDepartureDate((new SimpleDateFormat("dd/MM/yy")).parse(departureDate));
					//目的地:[KIX] OSAKA
					pdfBillDO.setDestinationAddress(destinationAddress);
					//路由：来源口岸-出发地-目的地：SZX-HKG-NRT
					pdfBillDO.setMailRoute("SZX-HKG-"+pdfBillDO.getDestinationAddress());
					//邮件类型:EMS
					//pdfBillDO.setMailType(mailType.contains("EM")?"EMS":mailType);
					pdfBillDO.setMailType(EmailTypeEnum.getValueByCode(mailType.trim())!=null?EmailTypeEnum.getValueByCode(mailType.trim()):mailType.trim());

					//总包号
					pdfBillDO.setPackageNo(packageNo);
					//原寄局
					pdfBillDO.setOriginOffice(originOffice);
					//寄达局
					pdfBillDO.setDestinationOffice(destinationOffice);
					//重量
					pdfBillDO.setWeight(new Double(weight));
					//袋号：BAG 17 F
					String regEx="[^0-9]";
					Pattern p = Pattern.compile(regEx);
					Matcher m = p.matcher(bagNo);
					pdfBillDO.setBagNo(new Integer(m.replaceAll("").trim()));
					//---------------------------------------

					log.info("pdfBillDO={}",pdfBillDO);
					datalist.add(pdfBillDO);

					beginRow++;
					mailType = sheet.getRow(beginRow).getCell(0).getStringCellValue();
				}

			}// end for(int i=0;i<sheetNum;i++)

		}catch (Exception e) {
			e.printStackTrace();
		}finally {
			if(inputStream != null){
				inputStream.close();
			}
			if(workbook != null){
				workbook.close();
			}
		}

		//4-统计排序=====================================================================================
		//根据口岸代码、航班号查询结算价格表数据
		//TODO  根据口岸、航班号、邮件类型、发运日期 取表 t_manual_price [常规价格表]
		List<ManualPriceDO> manualPriceList=pdfBillService.getManualPrice(portIdList,flightNoList);

		//datalist
		List<PdfBillDO> pdfBillList = new ArrayList<>();
		//分组
		Map<PdfBillGroupBy, List<PdfBillDO>> collect = datalist.stream()
				.collect(groupingBy(bill -> new PdfBillGroupBy(bill.getOriginAddress(), bill.getFlightNO(),bill.getDepartureDate(),bill.getMailRoute(),bill.getMailType(),bill.getPackageNo())));
		log.info("collect={}",collect.toString());
		//遍历各分组统计
		collect.entrySet().forEach(entry -> {
			double weightCount = entry.getValue().stream().mapToDouble(PdfBillDO::getWeight).sum();
			PdfBillDO pdfBillDO=new PdfBillDO();
			pdfBillDO.setDepartureDate(entry.getKey().getDepartureDate());
			pdfBillDO.setOriginAddress(entry.getKey().getOriginAddress());
			pdfBillDO.setMailRoute(entry.getKey().getMailRoute());
			pdfBillDO.setFlightNO(entry.getKey().getFlightNO());
			pdfBillDO.setMailType(entry.getKey().getMailType());
			pdfBillDO.setPackageNo(entry.getKey().getPackageNo());
			pdfBillDO.setWeight(weightCount);
			pdfBillDO.setBagCount(entry.getValue().size());

			String portId=pdfBillDO.getMailRoute().substring(pdfBillDO.getMailRoute().indexOf("-")+1);
			//结算金额先设置默认值,没有查询到置空
			pdfBillDO.setUnitPrice(null);
			pdfBillDO.setAmount(null);
			for(int j=0;j<manualPriceList.size();j++){
				ManualPriceDO manualPriceDO=manualPriceList.get(j);
				//发运日期 [ xx , xx )
				if(pdfBillDO.getFlightNO().equals(manualPriceDO.getFlightNumber())  //匹配航班号
					&& portId.equals(manualPriceDO.getPortId()) //匹配口岸
						&& pdfBillDO.getMailType().equals(EmailTypeEnum.getValueByCodeNo(manualPriceDO.getEmailType()))//匹配邮件类型（数据库中是数字编码要转换后匹配）
						&& pdfBillDO.getDepartureDate().getTime()>=manualPriceDO.getEffectiveStartDate().getTime() //匹配生效日期
						&& (manualPriceDO.getEffectiveEndDate()==null || pdfBillDO.getDepartureDate().getTime()<manualPriceDO.getEffectiveEndDate().getTime())
				){
					pdfBillDO.setUnitPrice(manualPriceDO.getPrice().doubleValue());
					pdfBillDO.setAmount(new BigDecimal(weightCount).multiply(manualPriceDO.getPrice()).doubleValue());
					break;
				}else{
					continue;
				}
			}
			pdfBillList.add(pdfBillDO);
			log.info("pdfBillDO={}",pdfBillDO);
		});
		//排序
		Collections.sort(pdfBillList, new Comparator<PdfBillDO>() {
			@Override
			public int compare(PdfBillDO o1, PdfBillDO o2) {
				int flag = o1.getDepartureDate().compareTo(o2.getDepartureDate());
				return flag;
			}
		});
		log.info("pdfBillList={}",pdfBillList);

		//5、导出excel==================================================================================

		// 1、创建工作表
		XSSFWorkbook xssfWorkbook = new XSSFWorkbook();
		XSSFSheet billSheet=xssfWorkbook.createSheet("核账单");
		billSheet.setColumnWidth(1,3766);
		billSheet.setColumnWidth(2,3766);
		billSheet.setColumnWidth(3,4200);
		billSheet.setColumnWidth(4,3500);
		billSheet.setColumnWidth(5,3500);
		billSheet.setColumnWidth(8,3766);
		billSheet.setColumnWidth(9,3766);
		billSheet.setColumnWidth(10,4000);
		XSSFSheet detailSheet=xssfWorkbook.createSheet("明细");
		detailSheet.setColumnWidth(1,3766);
		detailSheet.setColumnWidth(2,4200);
		detailSheet.setColumnWidth(3,3766);
		XSSFRow xssfRow; // 行
		XSSFCell xssfCell; // 列

		// 设置单元格样式：
		CellStyle titleStyle = xssfWorkbook.createCellStyle();
		// 1、水平\垂直居中
		titleStyle.setAlignment(HorizontalAlignment.CENTER);
		titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		// 3、字段加粗
		XSSFFont titleFont = xssfWorkbook.createFont();
		titleFont.setBold(true);
		titleFont.setFontName("宋体");
		titleFont.setFontHeightInPoints((short) 18);
		titleStyle.setFont(titleFont);

		CellStyle headStyle = xssfWorkbook.createCellStyle();
		// 1、水平\垂直居中
		headStyle.setAlignment(HorizontalAlignment.CENTER);
		headStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		// 3、字段加粗
		XSSFFont headFont = xssfWorkbook.createFont();
		headFont.setBold(true);
		headFont.setFontName("宋体");
		headFont.setFontHeightInPoints((short) 12);
		headStyle.setBorderBottom(BorderStyle.THIN); //下边框
		headStyle.setBorderLeft(BorderStyle.THIN); //左边框
		headStyle.setBorderTop(BorderStyle.THIN); //上边框
		headStyle.setBorderRight(BorderStyle.THIN); //右边框
		headStyle.setFont(headFont);

		CellStyle cellStyle = xssfWorkbook.createCellStyle();
		// 1、水平\垂直居中
		cellStyle.setAlignment(HorizontalAlignment.CENTER);
		cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		// 3、字段加粗
		XSSFFont cellFont = xssfWorkbook.createFont();
		cellFont.setBold(false);
		cellFont.setFontName("宋体");
		cellFont.setFontHeightInPoints((short) 12);
		cellStyle.setBorderBottom(BorderStyle.THIN); //下边框
		cellStyle.setBorderLeft(BorderStyle.THIN); //左边框
		cellStyle.setBorderTop(BorderStyle.THIN); //上边框
		cellStyle.setBorderRight(BorderStyle.THIN); //右边框
		cellStyle.setFont(cellFont);

		CellStyle moneyStyle = xssfWorkbook.createCellStyle();
		XSSFDataFormat format= xssfWorkbook.createDataFormat();
		moneyStyle.setDataFormat(format.getFormat("[$HK$-C04]#,##0.00_);[Red]\\([$HK$-C04]#,##0.00\\)"));
		// 1、水平居右\垂直居中
		moneyStyle.setAlignment(HorizontalAlignment.RIGHT);
		moneyStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		moneyStyle.setBorderBottom(BorderStyle.THIN); //下边框
		moneyStyle.setBorderLeft(BorderStyle.THIN); //左边框
		moneyStyle.setBorderTop(BorderStyle.THIN); //上边框
		moneyStyle.setBorderRight(BorderStyle.THIN); //右边框
		moneyStyle.setFont(cellFont);



		// 2、在sheet中创建行，注意判断 第一行是否已经创建，否则会覆盖之前的数据
		//--------sheet1-------------------------------------------------------------


		// 创建合并单元格（int firstRow, int lastRow, int firstCol, int lastCol）
		CellRangeAddress range = new CellRangeAddress(0, 0, 0, 10);
		// 创建行
		xssfRow = billSheet.getRow(range.getFirstRow());
		if (xssfRow == null) {
			xssfRow = billSheet.createRow(range.getFirstRow());
		}
		//设置标题样式
		xssfRow.setRowStyle(titleStyle);
		// 创建列
		xssfCell = xssfRow.createCell(range.getFirstColumn());
		// 设置单元格内容
		xssfCell.setCellValue(DateUtils.format(datalist.get(0).getDepartureDate(),"yyyy年M月")+"常规核账单(深圳黄包)");
		//设置样式
		xssfCell.setCellStyle(titleStyle);
		// 添加合并单元格到sheet
		billSheet.addMergedRegion(range);



		xssfRow = billSheet.createRow(1);
		String[] titles="序号,发运日期,出发地,航线,航班号,总包号,袋数,重量,邮件类型,结算单价,结算金额".split(",");
		for(int i=0;i<titles.length;i++){
			xssfCell=xssfRow.createCell(i);
			xssfCell.setCellValue(titles[i]);
			xssfCell.setCellStyle(headStyle);
		}

		int bagcount=0;
		BigDecimal weightCount=BigDecimal.ZERO;
		BigDecimal amountCount=BigDecimal.ZERO;
		for(int i=0;i<pdfBillList.size();i++){
			PdfBillDO bill=pdfBillList.get(i);
			xssfRow = billSheet.createRow(2+i);

			xssfCell=xssfRow.createCell(0);
			xssfCell.setCellStyle(cellStyle);
			xssfCell.setCellValue(i+1);

			xssfCell=xssfRow.createCell(1);
			xssfCell.setCellStyle(cellStyle);
			xssfCell.setCellValue(DateUtils.format(bill.getDepartureDate(),"yyyy/M/d"));

			xssfCell=xssfRow.createCell(2);
			xssfCell.setCellStyle(cellStyle);
			xssfCell.setCellValue(bill.getOriginAddress());

			xssfCell=xssfRow.createCell(3);
			xssfCell.setCellStyle(cellStyle);
			xssfCell.setCellValue(bill.getMailRoute());

			xssfCell=xssfRow.createCell(4);
			xssfCell.setCellStyle(cellStyle);
			xssfCell.setCellValue(bill.getFlightNO());

			xssfCell=xssfRow.createCell(5);
			xssfCell.setCellStyle(cellStyle);
			xssfCell.setCellValue(bill.getPackageNo());

			xssfCell=xssfRow.createCell(6);
			xssfCell.setCellStyle(cellStyle);
			xssfCell.setCellValue(bill.getBagCount());

			xssfCell=xssfRow.createCell(7);
			xssfCell.setCellStyle(cellStyle);
			xssfCell.setCellValue(bill.getWeight());

			xssfCell=xssfRow.createCell(8);
			xssfCell.setCellStyle(cellStyle);
			xssfCell.setCellValue(bill.getMailType());

			xssfCell=xssfRow.createCell(9);
			xssfCell.setCellStyle(moneyStyle);
			if(bill.getUnitPrice()==null){
				xssfCell.setCellValue("");
			}else{
				xssfCell.setCellValue(bill.getUnitPrice());
			}

			xssfCell=xssfRow.createCell(10);
			xssfCell.setCellStyle(moneyStyle);
			if(bill.getAmount()==null){
				xssfCell.setCellValue("");
			}else{
				xssfCell.setCellValue(bill.getAmount());
			}

			bagcount+=bill.getBagCount();
			weightCount=weightCount.add(new BigDecimal(bill.getWeight()));
			if(bill.getAmount()!=null){
				amountCount=amountCount.add(new BigDecimal(bill.getAmount()));
			}
		}
		xssfRow = billSheet.createRow(2+pdfBillList.size());
		xssfRow.createCell(0).setCellValue("合计");//袋数合计
		xssfRow.createCell(6).setCellValue(bagcount);//袋数合计
		xssfRow.createCell(7).setCellValue(weightCount.doubleValue());//重量合计
		xssfCell=xssfRow.createCell(10);
		xssfCell.setCellStyle(moneyStyle);
		xssfCell.setCellValue(amountCount.doubleValue());//结算金额合计
		//--------sheet2------------------------------------------------------------
		// 创建合并单元格（int firstRow, int lastRow, int firstCol, int lastCol）
		CellRangeAddress detailRange = new CellRangeAddress(0, 0, 0, 6);
		// 创建行
		xssfRow = detailSheet.getRow(detailRange.getFirstRow());
		if (xssfRow == null) {
			xssfRow = detailSheet.createRow(detailRange.getFirstRow());
		}
		// 创建列
		xssfCell = xssfRow.createCell(detailRange.getFirstColumn());
		// 设置单元格内容
		xssfCell.setCellValue("常规邮件数据表(明细)");
		//设置样式
		xssfCell.setCellStyle(titleStyle);
		// 添加合并单元格到sheet
		detailSheet.addMergedRegion(detailRange);

		xssfRow = detailSheet.createRow(1);
		String[] detailTitles="原寄局,日期,路由,总包,袋号,重量,邮件类型".split(",");
		for(int i=0;i<detailTitles.length;i++){
			xssfCell=xssfRow.createCell(i);
			xssfCell.setCellValue(detailTitles[i]);
			xssfCell.setCellStyle(headStyle);
		}


		for(int i=0;i<datalist.size();i++){
			PdfBillDO bill=datalist.get(i);
			xssfRow = detailSheet.createRow(2+i);
			xssfCell=xssfRow.createCell(0);
			xssfCell.setCellStyle(cellStyle);
			xssfCell.setCellValue(bill.getOriginOffice());
			xssfCell=xssfRow.createCell(1);
			xssfCell.setCellStyle(cellStyle);
			xssfCell.setCellValue(DateUtils.format(bill.getDepartureDate(),"yyyy/M/d"));
			xssfCell=xssfRow.createCell(2);
			xssfCell.setCellStyle(cellStyle);
			xssfCell.setCellValue(bill.getMailRoute());
			xssfCell=xssfRow.createCell(3);
			xssfCell.setCellStyle(cellStyle);
			xssfCell.setCellValue(bill.getPackageNo());
			xssfCell=xssfRow.createCell(4);
			xssfCell.setCellStyle(cellStyle);
			xssfCell.setCellValue(bill.getBagNo());
			xssfCell=xssfRow.createCell(5);
			xssfCell.setCellStyle(cellStyle);
			xssfCell.setCellValue(bill.getWeight());
			xssfCell=xssfRow.createCell(6);
			xssfCell.setCellStyle(cellStyle);
			xssfCell.setCellValue(bill.getMailType());
		}
		xssfRow = detailSheet.createRow(2+datalist.size());
		xssfRow.createCell(5).setCellValue(weightCount.doubleValue());

		// 5、导出excel
		response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName.substring(0, fileName.lastIndexOf(".")) + ".xlsx", "UTF-8"));
		ServletOutputStream out = response.getOutputStream();
		xssfWorkbook.write(out);
		out.close();
		xssfWorkbook.close();
	}



}
