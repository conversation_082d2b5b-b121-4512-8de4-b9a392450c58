package cn.com.xmmn.businesses.dao;


import java.util.List;
import java.util.Map;

import cn.com.xmmn.businesses.domain.ImportMailTraceDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 进口轨迹表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-04 10:28:53
 */
@Mapper
public interface ImportMailTraceDao {

	ImportMailTraceDO get(Integer id);
	
	List<ImportMailTraceDO> list(Map<String,Object> map);
	
	int count(Map<String,Object> map);
	
	int save(ImportMailTraceDO importMailTrace);
	
	int update(ImportMailTraceDO importMailTrace);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);
}
