package cn.com.xmmn.businesses.dt.api;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 接收客户订单信息
 */
@Data
public class ReceiveMailOrderDT implements Serializable {


    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 扫描时间（yyyyMMddhhmmss）
     */
    private String scanTime;

    /**
     * 跟踪号码
     */
    private String trackingNo;

    /**
     * 扫描重量
     */
    private String scanWeight;

    /**
     * 国家编码
     */
    private String receiverCountryCode;

    /**
     * 省份
     */
    private String receiverState;

    /**
     * 城市
     */
    private String receiverCity;

    /**
     * 收件人地址
     */
    private String receiverAddress;

    /**
     * 收件人电话
     */
    private String receiverPhoneNumber;

    /**
     * 收件人编码
     */
    private String receiverPostCode;


    /**
     * 是否寄件人付税（1 寄件人支付关税（DDP）
     */
    private String dutyPaid;

    /**
     * 申报信息
     */
    ReceiveMailOrderCustomDT orderCustoms;


}
