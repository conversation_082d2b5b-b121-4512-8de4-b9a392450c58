package cn.com.xmmn.businesses.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description 承运商邮袋接收列表
 * <AUTHOR>
 * @Date 2024/6/4 16:47
 */
@Data
@HeadRowHeight(value = 30) //头部行高
@ContentRowHeight(value = 25) //内容行高
@ColumnWidth(value = 20) //列宽
@HeadFontStyle(fontName = "宋体",fontHeightInPoints = 11)
public class HKCarrierDealerRecivListVo implements Serializable {
    //创建时间
    @ExcelProperty(value = "接口接收时间", index = 0)
    private Date createTime;
    @ExcelProperty(value = "邮袋号", index = 1)
    private String bagBarcode;
    //CN38号
    @ExcelProperty(value = "CN38号", index = 2)
    private String deliverBillNo;
    //航班号
    @ExcelProperty(value = "航班号", index = 3)
    private String carrierName;
    //承运商
    @ExcelProperty(value = "承运商", index = 4)
    private String carrierDealer;
    //容器号
    @ExcelProperty(value = "容器号", index = 5)
    private String containerNumbers;
    //邮袋接收时间
    @ExcelProperty(value = "邮袋接收时间", index = 6)
    private Date receiceDatetime;
    //起飞时间
    @ExcelProperty(value = "起飞时间", index = 7)
    private Date departureDatetime;
    //回退时间
    @ExcelProperty(value = "回退时间", index = 7)
    private Date returnDatetimeStart;


}
