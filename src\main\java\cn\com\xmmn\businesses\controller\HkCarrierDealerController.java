package cn.com.xmmn.businesses.controller;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import cn.com.xmmn.businesses.domain.CarrierDealerRecivDO;
import cn.com.xmmn.businesses.vo.CarrierDealerArriveToPostDetailListVo;
import cn.com.xmmn.businesses.vo.CarrierDealerArriveToPostListVo;
import cn.com.xmmn.businesses.vo.HKCarrierDealerRecivListVo;
import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import cn.com.xmmn.businesses.domain.HkCarrierDealerDO;
import cn.com.xmmn.businesses.service.HkCarrierDealerService;
import cn.com.xmmn.common.utils.PageUtils;
import cn.com.xmmn.common.utils.Query;
import cn.com.xmmn.common.utils.R;

import javax.servlet.http.HttpServletResponse;

/**
 * 香港邮政邮袋接收接口表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-19 15:18:40
 */
@Slf4j
@Controller
@RequestMapping("/hkCarrierDealer/monitor")
public class HkCarrierDealerController {
	@Autowired
	private HkCarrierDealerService hkCarrierDealerService;
	
	@GetMapping()
	@RequiresPermissions("hkCarrierDealer:hkCarrierDealer")
	String HkCarrierDealer(){
	    return "hkCarrierDealer/list";
	}


	@ResponseBody
	@GetMapping("/list")
	@RequiresPermissions("hkCarrierDealer:hkCarrierDealer")
	public PageUtils list(@RequestParam Map<String, Object> params){
		log.info("香港邮政发运数据统计参数:{}" , params);
		//查询列表数据
        Query query = new Query(params);
		List<HkCarrierDealerDO> hkCarrierDealerList = hkCarrierDealerService.list(query);
		int total = hkCarrierDealerService.count(query);
		PageUtils pageUtils = new PageUtils(hkCarrierDealerList, total);
		return pageUtils;
	}
	/**
	 * 导出数据
	 *
	 * @param map
	 * @param response 入参
	 * @return void
	 **/
	@RequestMapping(value = "/exportExcel", method = RequestMethod.GET)
	public void exportExcel(@RequestParam Map<String, Object> map, HttpServletResponse response) throws IOException {

		List<HkCarrierDealerDO> hkCarrierDealerList = hkCarrierDealerService.list(map);

		//导出
		response.setContentType("application/vnd.ms-excel");
		response.setCharacterEncoding("UTF-8");
		// 文件名
		String fileName = map.get("dateStart") + "香港邮政发运数据统计表" + ".xlsx";
		try {
			fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
		try {
			List<HKCarrierDealerRecivListVo> exportList = new ArrayList<>();
			for (HkCarrierDealerDO portbatch : hkCarrierDealerList) {
				HKCarrierDealerRecivListVo vo = new HKCarrierDealerRecivListVo();
				BeanUtils.copyProperties(portbatch, vo);
				exportList.add(vo);
			}
			//导出
			EasyExcel.write(response.getOutputStream(), HKCarrierDealerRecivListVo.class).sheet("香港邮政发运数据统计表").doWrite(exportList);
		} catch (Exception e) {
			log.info("excel文档导出错误-异常信息：" + e);
			e.printStackTrace();
		}

	}
	
}
