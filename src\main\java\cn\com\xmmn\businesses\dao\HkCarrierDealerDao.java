package cn.com.xmmn.businesses.dao;

import cn.com.xmmn.businesses.domain.HkCarrierDealerDO;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

/**
 * 香港邮政邮袋接收接口表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-25 09:53:59
 */
@Mapper
public interface HkCarrierDealerDao {

	HkCarrierDealerDO get(Integer id);
	
	List<HkCarrierDealerDO> list(Map<String,Object> map);
	
	int count(Map<String,Object> map);
	
	int save(HkCarrierDealerDO hkCarrierDealer);
	
	int update(HkCarrierDealerDO hkCarrierDealer);
	
	int remove(Integer id);
	
	int batchRemove(Integer[] ids);
}
