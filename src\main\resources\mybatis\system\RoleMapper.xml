<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.xmmn.system.dao.RoleDao">

	<select id="get" resultType="cn.com.xmmn.system.domain.RoleDO">
		select
		`role_id`,`role_name`,`role_sign`,`remark`,`user_id_create`,`gmt_create`,`gmt_modified`
		from sys_role where role_id = #{value}
	</select>

	<select id="list" resultType="cn.com.xmmn.system.domain.RoleDO">
		select
		`role_id`,`role_name`,`role_sign`,`remark`,`user_id_create`,`gmt_create`,`gmt_modified`
		from sys_role
		<where>
			<if test="roleId != null and roleId != ''"> and role_id = #{roleId} </if>
			<if test="roleName != null and roleName != ''"> and role_name = #{roleName} </if>
			<if test="roleSign != null and roleSign != ''"> and role_sign = #{roleSign} </if>
			<if test="remark != null and remark != ''"> and remark = #{remark} </if>
			<if test="userIdCreate != null and userIdCreate != ''"> and user_id_create = #{userIdCreate} </if>
			<if test="gmtCreate != null and gmtCreate != ''"> and gmt_create = #{gmtCreate} </if>
			<if test="gmtModified != null and gmtModified != ''"> and gmt_modified = #{gmtModified} </if>
		</where>
		<choose>
			<when test="sort != null and sort.trim() != ''">
				order by role_id desc
			</when>
			<otherwise>
				order by role_id desc
			</otherwise>
		</choose>
		<if test="offset != null and limit != null">
			limit #{offset}, #{limit}
		</if>
	</select>

	<select id="count" resultType="int">
		select count(*) from sys_role
		<where>
			<if test="roleId != null and roleId != ''"> and role_id = #{roleId} </if>
			<if test="roleName != null and roleName != ''"> and role_name = #{roleName} </if>
			<if test="roleSign != null and roleSign != ''"> and role_sign = #{roleSign} </if>
			<if test="remark != null and remark != ''"> and remark = #{remark} </if>
			<if test="userIdCreate != null and userIdCreate != ''"> and user_id_create = #{userIdCreate} </if>
			<if test="gmtCreate != null and gmtCreate != ''"> and gmt_create = #{gmtCreate} </if>
			<if test="gmtModified != null and gmtModified != ''"> and gmt_modified = #{gmtModified} </if>
		</where>
	</select>

	<insert id="save" parameterType="cn.com.xmmn.system.domain.RoleDO"
		useGeneratedKeys="true" keyProperty="roleId">
		insert into sys_role
		(
		`role_name`,
		`role_sign`,
		`remark`,
		`user_id_create`,
		`gmt_create`,
		`gmt_modified`
		)
		values
		(
		#{roleName},
		#{roleSign},
		#{remark},
		#{userIdCreate},
		#{gmtCreate},
		#{gmtModified}
		)
	</insert>

	<update id="update" parameterType="cn.com.xmmn.system.domain.RoleDO">
		update sys_role
		<set>
			<if test="roleName != null">`role_name` = #{roleName}, </if>
			<if test="roleSign != null">`role_sign` = #{roleSign}, </if>
			<if test="remark != null">`remark` = #{remark}, </if>
			<if test="userIdCreate != null">`user_id_create` = #{userIdCreate}, </if>
			<if test="gmtCreate != null">`gmt_create` = #{gmtCreate}, </if>
			<if test="gmtModified != null">`gmt_modified` = #{gmtModified}</if>
		</set>
		where role_id = #{roleId}
	</update>

	<delete id="remove">
		delete from sys_role where role_id = #{value}
	</delete>

	<delete id="batchRemove">
		delete from sys_role where role_id in
		<foreach item="roleId" collection="array" open="(" separator=","
			close=")">
			#{roleId}
		</foreach>
	</delete>

</mapper>