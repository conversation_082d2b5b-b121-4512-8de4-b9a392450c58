package cn.com.xmmn.businesses.service.impl;

import cn.com.xmmn.businesses.dao.ImportMailTraceDao;
import cn.com.xmmn.businesses.domain.ImportMailTraceDO;
import cn.com.xmmn.businesses.service.ImportMailTraceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;





@Service
public class ImportMailTraceServiceImpl implements ImportMailTraceService {
	@Autowired
	private ImportMailTraceDao importMailTraceDao;
	
	@Override
	public ImportMailTraceDO get(Integer id){
		return importMailTraceDao.get(id);
	}
	
	@Override
	public List<ImportMailTraceDO> list(Map<String, Object> map){
		return importMailTraceDao.list(map);
	}
	
	@Override
	public int count(Map<String, Object> map){
		return importMailTraceDao.count(map);
	}
	
	@Override
	public int save(ImportMailTraceDO importMailTrace){
		return importMailTraceDao.save(importMailTrace);
	}
	
	@Override
	public int update(ImportMailTraceDO importMailTrace){
		return importMailTraceDao.update(importMailTrace);
	}
	
	@Override
	public int remove(Integer id){
		return importMailTraceDao.remove(id);
	}
	
	@Override
	public int batchRemove(Integer[] ids){
		return importMailTraceDao.batchRemove(ids);
	}
	
}
