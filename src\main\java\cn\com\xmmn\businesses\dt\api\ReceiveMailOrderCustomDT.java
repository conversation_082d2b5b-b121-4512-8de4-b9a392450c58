package cn.com.xmmn.businesses.dt.api;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 订单关税信息
 */
@Data
public class ReceiveMailOrderCustomDT implements Serializable {

    /**
     * 申报币种
     */
    private String currency;

    /**
     * 海关申报类型：G D S O
     */
    private String customsType;

    /**
     * 申报详情列表
     */
    private List<ReceiveMailDeclarationDT> customsItemList;
}
