package cn.com.xmmn.common.service.impl;

import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.com.xmmn.common.config.Constant;
import cn.com.xmmn.common.dao.TaskDao;
import cn.com.xmmn.common.domain.ScheduleJob;
import cn.com.xmmn.common.domain.TaskDO;
import cn.com.xmmn.common.quartz.utils.QuartzManager;
import cn.com.xmmn.common.service.JobService;
import cn.com.xmmn.common.utils.ScheduleJobUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class JobServiceImpl implements JobService {

	@Autowired
	private TaskDao taskScheduleJobMapper;

	@Autowired
	QuartzManager quartzManager;

	@Override
	public TaskDO get(Long id) {
		return taskScheduleJobMapper.get(id);
	}

	@Override
	public List<TaskDO> list(Map<String, Object> map) {
		return taskScheduleJobMapper.list(map);
	}

	@Override
	public int count(Map<String, Object> map) {
		return taskScheduleJobMapper.count(map);
	}

	@Override
	public int save(TaskDO taskScheduleJob) {
		return taskScheduleJobMapper.save(taskScheduleJob);
	}

	@Override
	public int update(TaskDO taskScheduleJob) {
		return taskScheduleJobMapper.update(taskScheduleJob);
	}

	@Override
	public int remove(Long id) {
		try {
			TaskDO scheduleJob = get(id);
			quartzManager.deleteJob(ScheduleJobUtils.entityToData(scheduleJob));
			return taskScheduleJobMapper.remove(id);
		} catch (SchedulerException e) {
			e.printStackTrace();
			return 0;
		}

	}

	@Override
	public int batchRemove(Long[] ids) {
		for (Long id : ids) {
			try {
				TaskDO scheduleJob = get(id);
				quartzManager.deleteJob(ScheduleJobUtils.entityToData(scheduleJob));
			} catch (SchedulerException e) {
				e.printStackTrace();
				return 0;
			}
		}
		return taskScheduleJobMapper.batchRemove(ids);
	}

	@Override
	public void initSchedule() throws SchedulerException {
		// 这里获取任务信息数据
		List<TaskDO> jobList = taskScheduleJobMapper.list(new HashMap<String, Object>(16));
		for (TaskDO scheduleJob : jobList) {
			if ("1".equals(scheduleJob.getJobStatus())) {
				ScheduleJob job = ScheduleJobUtils.entityToData(scheduleJob);
				quartzManager.addJob(job);
			}

		}
	}

//	@Override
//	public void changeStatus(Long jobId, String cmd) throws SchedulerException {
//		TaskDO scheduleJob = get(jobId);
//		if (scheduleJob == null) {
//			return;
//		}
//		if (Constant.STATUS_RUNNING_STOP.equals(cmd)) {
//			quartzManager.deleteJob(ScheduleJobUtils.entityToData(scheduleJob));
//			scheduleJob.setJobStatus(ScheduleJob.STATUS_NOT_RUNNING);
//		} else {
//			if (!Constant.STATUS_RUNNING_START.equals(cmd)) {
//			} else {
//                scheduleJob.setJobStatus(ScheduleJob.STATUS_RUNNING);
//                quartzManager.addJob(ScheduleJobUtils.entityToData(scheduleJob));
//            }
//		}
//		update(scheduleJob);
//	}
	@Override
	public void changeStatus(Long jobId, String cmd) throws SchedulerException {
		TaskDO scheduleJob = get(jobId);
		if (scheduleJob == null) {
			return;
		}

		ScheduleJob jobData = ScheduleJobUtils.entityToData(scheduleJob);
		if (Constant.STATUS_RUNNING_STOP.equals(cmd)) {
			quartzManager.deleteJob(jobData); // 删除任务
			scheduleJob.setJobStatus(ScheduleJob.STATUS_NOT_RUNNING);
			System.out.println("Job stopped: " + jobData.getJobName());
		} else {
			if (Constant.STATUS_RUNNING_START.equals(cmd)) {
				if (scheduleJob.getJobStatus().equals(ScheduleJob.STATUS_RUNNING)) {
					System.out.println("Job is already running.");
					return;
				}
				scheduleJob.setJobStatus(ScheduleJob.STATUS_RUNNING);
				quartzManager.addJob(jobData); // 添加任务
				System.out.println("Job started: " + jobData.getJobName());
			}
		}

		update(scheduleJob); // 更新任务状态
	}
	@Override
	public void updateCron(Long jobId) throws SchedulerException {
		TaskDO scheduleJob = get(jobId);
		if (scheduleJob == null) {
			return;
		}
		if (ScheduleJob.STATUS_RUNNING.equals(scheduleJob.getJobStatus())) {
			quartzManager.updateJobCron(ScheduleJobUtils.entityToData(scheduleJob));
		}
		update(scheduleJob);
	}

}
