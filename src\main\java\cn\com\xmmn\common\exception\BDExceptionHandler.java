//package cn.com.xmmn.common.exception;
//
//import org.apache.shiro.authz.AuthorizationException;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.dao.DuplicateKeyException;
//import org.springframework.web.bind.annotation.ExceptionHandler;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestControllerAdvice;
//import org.springframework.web.servlet.ModelAndView;
//
//import cn.com.xmmn.common.config.Constant;
//import cn.com.xmmn.common.domain.LogDO;
//import cn.com.xmmn.common.service.LogService;
//import cn.com.xmmn.common.utils.ExceptionUtils;
//import cn.com.xmmn.common.utils.HttpServletUtils;
//import cn.com.xmmn.common.utils.R;
//import cn.com.xmmn.common.utils.ShiroUtils;
//import cn.com.xmmn.system.domain.UserDO;
//
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.util.Date;
//
///**
// * 异常处理器
// */
//@RestControllerAdvice
//public class BDExceptionHandler {
//    private Logger logger = LoggerFactory.getLogger(getClass());
//    @Autowired
//    LogService logService;
////
////    /**
////     * 自定义异常
////     */
////    @ExceptionHandler(BDException.class)
////    public R handleBDException(BDException e) {
////        logger.error(e.getMessage(), e);
////        R r = new R();
////        r.put("code", e.getCode());
////        r.put("msg", e.getMessage());
////        return r;
////    }
////
////    @ExceptionHandler(DuplicateKeyException.class)
////    public R handleDuplicateKeyException(DuplicateKeyException e) {
////        logger.error(e.getMessage(), e);
////        return R.error("数据库中已存在该记录");
////    }
////
////    @ExceptionHandler(org.springframework.web.servlet.NoHandlerFoundException.class)
////    public R noHandlerFoundException(org.springframework.web.servlet.NoHandlerFoundException e) {
////        logger.error(e.getMessage(), e);
////        return R.error(404, "没找找到页面");
////    }
//
//    @ExceptionHandler(AuthorizationException.class)
//    public Object handleAuthorizationException(AuthorizationException e, HttpServletRequest request) {
//        logger.error(e.getMessage(), e);
//        if (HttpServletUtils.jsAjax(request)) {
//            return R.error(403, "未授权");
//        }
//        return new ModelAndView("error/403");
//    }
//
//
//    @ExceptionHandler({Exception.class})
//    public Object handleException(Exception e, HttpServletRequest request) {
//        LogDO logDO = new LogDO();
//        logDO.setGmtCreate(new Date());
//        logDO.setOperation(Constant.LOG_ERROR);
//        logDO.setMethod(request.getRequestURL().toString());
//        logDO.setParams(e.toString().length()>2000?e.toString().substring(0,2000):e.toString());//未截取超长
//        UserDO current = ShiroUtils.getUser();
//        if(null!=current){
//            logDO.setUserId(current.getUserId());
//            logDO.setUsername(current.getUsername());
//        }
//        logService.save(logDO);
//        logger.error(e.getMessage(), e);
//        if (HttpServletUtils.jsAjax(request)) {
//            return R.error(500, "服务器错误，请联系管理员");
//        }
//        return new ModelAndView("error/500");
//    }
//}
