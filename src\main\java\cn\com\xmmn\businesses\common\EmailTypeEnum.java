package cn.com.xmmn.businesses.common;

public enum EmailTypeEnum {

    TYPE_UL("UL","航空函件","03"),
    TYPE_UA("UA","航空函件","03"),
    TYPE_UN("UN","航空函件","03"),
    TYPE_UM("UM","航空函件","03"),
    TYPE_UR("UR","航空函件","03"),
    TYPE_CB("CB","包裹","04"),
    TYPE_CN("CN","包裹","04"),
    TYPE_CV("CV","包裹","04"),
    TYPE_CZ("CZ","包裹","04"),
    TYPE_ED("ED","EMS","01"),
    TYPE_EM("EM","EMS","01"),
    TYPE_EN("EN","EMS","01"),
    TYPE_UT("UT","空袋",""),
    TYPE_CT("CT","空袋",""),
    TYPE_ET("ET","空袋",""),
    TYPE_TT("TT","空袋",""),
    TYPE_UD("UD","E邮宝","02"),
    TYPE_UX("UX","E邮宝","02"),
    TYPE_UZ("UZ","E邮宝","02"),
    TYPE_EZ("EZ","E邮宝","02"),
    TYPE_EY("EY","E邮宝","02"),
    TYPE_UU("UU","E邮宝","02");

    private String code;
    private String value;
    private String codeNo;

    private EmailTypeEnum(String code,String value,String codeNo){
        this.code=code;
        this.value=value;
        this.codeNo=codeNo;

    }


    /**
     * 获取编码
     *
     * @return 编码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getValue() {
        return value;
    }

    /**
     * 获取数字编码
     *
     * @return 编码
     */
    public String getCodeNo() {
        return codeNo;
    }

    /**
     * 根据编码返回对应的EnumExample的描述信息
     * 如果不存在对应的EnumExample，则返回null
     *
     * @param code 编码
     * @return 描述信息
     */
    public static String getValueByCode(String code) {
       for(EmailTypeEnum emailTypeEnum:EmailTypeEnum.values()){
           if(code.equals(emailTypeEnum.getCode())){
               return  emailTypeEnum.getValue();
           }
       }
       return null;
    }

    public static String getValueByCodeNo(String codeNo) {
        for(EmailTypeEnum emailTypeEnum:EmailTypeEnum.values()){
            if(codeNo.equals(emailTypeEnum.getCodeNo())){
                return  emailTypeEnum.getValue();
            }
        }
        return null;
    }

}
