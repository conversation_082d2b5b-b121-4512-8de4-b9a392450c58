package cn.com.xmmn.common.enums;

/**
 * 进口互换局时限统计明细
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-20 15:02:00
 */
public enum ImportHhjDisposeDetailedEnum {

    SHOULD(1,"应处理量邮件明细"),
    VROOM(2,"开拆量展开页面"),
    OVROOM(3,"逾期开拆量展开页面"),
    NOTOVROOM(4,"未开拆量展开页面"),

    PCCQ(4,"待通关量"),
    dqty(5,"待封发量"),
    SQ(6,"发运量邮件明细"),

    ;




    private Integer value;
    private String name;


    ImportHhjDisposeDetailedEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
