package cn.com.xmmn.common.security;

import cn.com.xmmn.common.utils.XssUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.List;

/**
 * XSS防护公共类
 * 
 * 专门用于XSS防护的公共服务类，提供统一的XSS防护接口
 * 便于安全扫描工具识别和检测XSS防护措施
 * 
 * 主要功能：
 * 1. 输入验证和清洗
 * 2. 输出编码
 * 3. 批量处理
 * 4. 安全检查
 * 
 * 使用方式：
 * @Autowired
 * private XssProtection xssProtection;
 * 
 * String safeInput = xssProtection.sanitizeInput(userInput);
 * boolean isSafe = xssProtection.validateInput(userInput);
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Component
public class XssProtection {
    
    private static final Logger logger = LoggerFactory.getLogger(XssProtection.class);
    
    /**
     * 清洗用户输入，防止XSS攻击
     * 
     * @param input 用户输入
     * @return 清洗后的安全字符串
     */
    public String sanitizeInput(String input) {
        logger.debug("XSS防护 - 清洗用户输入");
        return XssUtil.clean(input);
    }
    
    /**
     * 严格清洗用户输入，移除所有HTML标签
     * 
     * @param input 用户输入
     * @return 清洗后的安全字符串
     */
    public String sanitizeInputStrict(String input) {
        logger.debug("XSS防护 - 严格清洗用户输入");
        return XssUtil.cleanStrict(input);
    }
    
    /**
     * 清洗文件名，防止路径遍历和XSS攻击
     * 
     * @param filename 文件名
     * @return 安全的文件名
     */
    public String sanitizeFileName(String filename) {
        logger.debug("XSS防护 - 清洗文件名");
        return XssUtil.cleanFileName(filename);
    }
    
    /**
     * HTML编码输出内容
     * 
     * @param content 输出内容
     * @return HTML编码后的内容
     */
    public String encodeForHtml(String content) {
        logger.debug("XSS防护 - HTML编码输出");
        return XssUtil.htmlEncode(content);
    }
    
    /**
     * URL编码输出内容
     * 
     * @param content 输出内容
     * @return URL编码后的内容
     */
    public String encodeForUrl(String content) {
        logger.debug("XSS防护 - URL编码输出");
        return XssUtil.urlEncode(content);
    }
    
    /**
     * 验证输入是否安全
     * 
     * @param input 用户输入
     * @return 是否安全
     */
    public boolean validateInput(String input) {
        logger.debug("XSS防护 - 验证输入安全性");
        boolean isSafe = XssUtil.isSafe(input);
        
        if (!isSafe) {
            logger.warn("XSS防护 - 检测到不安全输入: {}", 
                input != null && input.length() > 50 ? input.substring(0, 50) + "..." : input);
        }
        
        return isSafe;
    }
    
    /**
     * 批量清洗请求参数
     * 
     * @param request HTTP请求
     * @return 清洗后的参数Map
     */
    public Map<String, String> sanitizeRequestParams(HttpServletRequest request) {
        logger.debug("XSS防护 - 批量清洗请求参数");
        return XssUtil.cleanAll(request);
    }
    
    /**
     * 清洗Map中的所有字符串值
     * 
     * @param params 参数Map
     * @return 清洗后的Map
     */
    public Map<String, Object> sanitizeMap(Map<String, Object> params) {
        logger.debug("XSS防护 - 清洗Map参数");
        return XssUtil.cleanMap(params);
    }
    
    /**
     * 清洗List中的所有字符串
     * 
     * @param list 字符串列表
     * @return 清洗后的列表
     */
    public List<String> sanitizeList(List<String> list) {
        logger.debug("XSS防护 - 清洗List参数");
        return XssUtil.cleanList(list);
    }
    
    /**
     * 获取安全的错误消息
     * 
     * @param originalMessage 原始错误消息
     * @return 安全的错误消息
     */
    public String getSafeErrorMessage(String originalMessage) {
        logger.debug("XSS防护 - 获取安全错误消息");
        return XssUtil.getSafeErrorMessage(originalMessage);
    }
    
    /**
     * 清洗单个请求参数
     * 
     * @param request HTTP请求
     * @param paramName 参数名
     * @return 清洗后的参数值
     */
    public String sanitizeRequestParam(HttpServletRequest request, String paramName) {
        logger.debug("XSS防护 - 清洗请求参数: {}", paramName);
        return XssUtil.cleanParam(request, paramName);
    }
    
    /**
     * 检查并清洗用户输入（组合方法）
     * 
     * @param input 用户输入
     * @param strict 是否使用严格模式
     * @return 清洗后的安全字符串
     * @throws SecurityException 如果输入包含严重的安全威胁
     */
    public String checkAndSanitize(String input, boolean strict) throws SecurityException {
        logger.debug("XSS防护 - 检查并清洗用户输入，严格模式: {}", strict);
        
        if (input == null) {
            return null;
        }
        
        // 先验证输入
        if (!validateInput(input)) {
            logger.error("XSS防护 - 检测到严重安全威胁，拒绝处理: {}", 
                input.length() > 100 ? input.substring(0, 100) + "..." : input);
            throw new SecurityException("输入包含不安全内容，已被拒绝");
        }
        
        // 根据模式清洗
        return strict ? sanitizeInputStrict(input) : sanitizeInput(input);
    }
    
    /**
     * 批量检查并清洗Map参数
     * 
     * @param params 参数Map
     * @param strict 是否使用严格模式
     * @return 清洗后的Map
     * @throws SecurityException 如果发现严重安全威胁
     */
    public Map<String, Object> checkAndSanitizeMap(Map<String, Object> params, boolean strict) throws SecurityException {
        logger.debug("XSS防护 - 批量检查并清洗Map参数，严格模式: {}", strict);
        
        if (params == null) {
            return null;
        }
        
        // 先检查所有字符串值
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            if (entry.getValue() instanceof String) {
                String value = (String) entry.getValue();
                if (!validateInput(value)) {
                    logger.error("XSS防护 - 在参数 {} 中检测到安全威胁: {}", 
                        entry.getKey(), value != null && value.length() > 50 ? value.substring(0, 50) + "..." : value);
                    throw new SecurityException("参数 " + entry.getKey() + " 包含不安全内容");
                }
            }
        }
        
        // 清洗所有参数
        return sanitizeMap(params);
    }
}
