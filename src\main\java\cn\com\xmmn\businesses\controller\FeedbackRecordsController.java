package cn.com.xmmn.businesses.controller;

import cn.com.xmmn.businesses.config.PushServiceConfig;
import cn.com.xmmn.businesses.config.ServiceConfig;
import cn.com.xmmn.businesses.domain.FeedbackRecordsDO;
import cn.com.xmmn.businesses.domain.FeedbackRecordsPushLogDO;
import cn.com.xmmn.businesses.service.FeedbackRecordsService;
import cn.com.xmmn.common.utils.PageUtils;
import cn.com.xmmn.common.utils.Query;
import cn.com.xmmn.common.utils.R;
import com.alibaba.excel.EasyExcel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

/**
 * 信息反馈
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-08 15:45:24
 */

@Controller
@RequestMapping("/feedbackRecords")
@RequiredArgsConstructor
@Slf4j
public class FeedbackRecordsController {

    private final FeedbackRecordsService feedbackRecordsService;
    @GetMapping()
    @RequiresPermissions("feedbackRecords:feedbackRecords")
    String FeedbackRecords() {
        return "businesses/feedbackRecords/feedbackRecords";
    }

    @ResponseBody
    @GetMapping("/list")
    @RequiresPermissions("feedbackRecords:feedbackRecords")
    public PageUtils list(@RequestParam Map<String, Object> params) {

        //查询列表数据
        Query query = new Query(params);
        List<FeedbackRecordsDO> feedbackRecordsList = feedbackRecordsService.list(query);
        int total = feedbackRecordsService.count(query);
        PageUtils pageUtils = new PageUtils(feedbackRecordsList, total);
        return pageUtils;
    }


    /**
     * 批量推送
     *
     * @param ids
     * @param pushType 推送类型:1接收2启运3运抵4交邮
     * @return
     */
    @PostMapping("/batchPush")
    @ResponseBody
    @RequiresPermissions("feedbackRecords:feedbackRecords")
    public R batchPush(@RequestParam("ids[]") Integer[] ids, @RequestParam("pushType") Integer pushType) {
        feedbackRecordsService.batchPush(ids, pushType);
        return R.ok();
    }

    /**
     * 跳转推送日志列表
     *
     * @return
     */
    @GetMapping("/push/log")
    @RequiresPermissions("feedbackRecords:feedbackRecords")
    public String detailPage(HttpServletRequest request, Model model) {
        //邮袋条码
        String bagBarCode = request.getParameter("bagBarCode");
        model.addAttribute("bagBarCode", bagBarCode);
        return "businesses/feedbackRecords/feedbackRecordsPushLog";
    }

    @ResponseBody
    @GetMapping("/pushLog/list")
    @RequiresPermissions("feedbackRecords:feedbackRecords")
    public PageUtils pushLogList(@RequestParam Map<String, Object> params) {
        //查询列表数据
        Query query = new Query(params);
        List<FeedbackRecordsPushLogDO> feedbackRecordsPushLogList = feedbackRecordsService.pushLogList(query);
        int total = feedbackRecordsService.pushLogCount(query);
        PageUtils pageUtils = new PageUtils(feedbackRecordsPushLogList, total);
        return pageUtils;
    }

    /**
     * 导出数据
     *
     * @param map
     * @param response 入参
     * @return void
     **/
    @RequestMapping(value = "/exportExcel", method = RequestMethod.GET)
    public void exportExcel(@RequestParam Map<String, Object> map, HttpServletResponse response) throws IOException {

        List<FeedbackRecordsDO> hkCarrierDealerList = feedbackRecordsService.list(map);

        //导出
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");
        // 文件名
        String fileName = map.get("dateStart") + "反馈信息表" + ".xlsx";
        try {
            fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
            //fileName = URLEncoder.encode(fileName, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
        //导出
        EasyExcel.write(response.getOutputStream(), FeedbackRecordsDO.class).sheet("反馈信息表").doWrite(hkCarrierDealerList);
    }
}
