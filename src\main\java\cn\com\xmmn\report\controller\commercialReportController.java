package cn.com.xmmn.report.controller;

import cn.com.xmmn.businesses.service.AirMailChannelBillsFristBillService;
import cn.com.xmmn.businesses.service.BatchService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * TMS订单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-30 20:15:07
 */
@Slf4j
@Controller
@RequestMapping("/report/commercialReport")
public class commercialReportController {

    @Autowired
    private BatchService batchService;


    @GetMapping()
    @RequiresPermissions("report:commercialReport")
    String init(Model model) {
        //设置初始时间  初始时间为三个月前的第一天，比如今天3月22日，则开始时间设为2023-01，结束时间为2023-03
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DATE, 1);// 设为当前月的1号
        calendar.add(Calendar.MONTH, -2);// 0表示当前月，-2就是当前月-2
        Date d = calendar.getTime();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String startDate = sdf.format(d);
        String endDate = sdf.format(new Date());
        //柱形图时间
        model.addAttribute("startDate", startDate);
        model.addAttribute("endDate", endDate);
        return "report/commercial";
    }

    //    按口岸统计图
    @ResponseBody
    @PostMapping("/list")
    @RequiresPermissions("report:commercialReport")
    public Map<String, Object> portList(String startDate, String endDate) {
        Map<String, Object> data = new HashMap<String, Object>();

        /**********A1.柱形图数据************/
        String endDateStr = endDate + "-32";//加30天
        List<String> title = getTitle(startDate, endDate);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("endDateStr", endDateStr);
        map.put("endDate", endDate);
        map.put("startDate", startDate);
        map.put("title", title);
        List<Map<String, Object>> dataList = batchService.commercialList(map);
        String result = JSON.toJSONString(dataList).toString();
        log.info("返回结果：" + result);
//        title.add(0, "product");//Echart显示表头的时候需要用到
//        log.info("title" + title.toString());
//        model.addAttribute("yuefen", title);
        data.put("result", result);
        data.put("barnum", title.size());


        data.put("status", "success");
        return data;

    }


    //获取sql需要分组的参数
    public List<String> getTitle(String begin, String end) {
        //若 begin = '2022-11',end = '2023-03',则返回[2022-11, 2022-12, 2023-01, 2023-02, 2023-03]
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        List<String> list = new ArrayList<String>();
        /*list.add("product"); //Echart显示表头的时候需要用到*/
        Date d1;
        Date d2;
        try {
            d1 = new SimpleDateFormat("yyyy-MM").parse(begin);
            d2 = new SimpleDateFormat("yyy-MM").parse(end);//定义结束日期可以去当前月也可以手动写日期。
            Calendar dd = Calendar.getInstance();//定义日期实例
            dd.setTime(d1);//设置日期起始时间
            while (dd.getTime().before(d2)) {//判断是否到结束日期
                SimpleDateFormat sdf = new SimpleDateFormat("yyy-MM");
                String str = sdf.format(dd.getTime());
                dd.add(Calendar.MONTH, 1);//进行当前日期月份加1
                list.add(str);
            }
            list.add(end);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return list;

    }

    //使用Calendar的set和add方法，从下个月的第一天计算得到当前月的最后一天,并得到最后一天是28/29/30/31
    public String getLastDayOfMonth(String dateStr) {//参数2023-03，返回结果31

        String year = dateStr.substring(0,4);
        String month = dateStr.substring(5,7);
        Calendar cal = Calendar.getInstance();
        //年
        cal.set(Calendar.YEAR, Integer.parseInt(year));
        //月，因为Calendar里的月是从0开始，所以要-1
        cal.set(Calendar.MONTH, Integer.parseInt(month) - 1);
        //日，设为一号
        cal.set(Calendar.DATE, 1);
        //月份加一，得到下个月的一号
        cal.add(Calendar.MONTH,1);
        //下一个月减一为本月最后一天
        cal.add(Calendar.DATE, -1);
        String monthEnd = String.valueOf(cal.get(Calendar.DAY_OF_MONTH));//获得月末是几号
//        System.out.println(year+month+",获得本月月末:" + monthEnd);
        return monthEnd;
    }
}
