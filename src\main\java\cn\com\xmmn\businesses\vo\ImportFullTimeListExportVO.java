package cn.com.xmmn.businesses.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.util.Date;

@Data
@HeadRowHeight(value = 30) //头部行高
@ContentRowHeight(value = 25) //内容行高
@ColumnWidth(value = 20) //列宽
@HeadFontStyle(fontName = "宋体",fontHeightInPoints = 11)
public class ImportFullTimeListExportVO {

    @ExcelProperty("客户名称")
    private String custName;
    @ExcelProperty("邮件号码")
    private String itemId;

    @ExcelProperty("香港已形成总包时间")
    private Date contractTime;


    // 香港已形成总包条码
    @ExcelProperty("香港已形成总包条码")
    private String barCode;

    // 互换局进口接收扫描时间
    @ExcelProperty("互换局进口接收扫描时间")
    private Date chOrgRecvTime;

    // 互换局开拆时间
    @ExcelProperty("互换局开拆时间")
    private Date chOrgOpenTime;

    // 交海关待验
    @ExcelProperty("交海关待验")
    private Date waitTestTime;

    // 海关待验开拆
    @ExcelProperty("海关待验开拆")
    private Date waitOpenTime;

    // 封报关行时间
    @ExcelProperty("封报关行时间")
    private Date disBrokerTime;

    // 报关行出库时间
    @ExcelProperty("报关行出库时间")
    private Date brokerOutTime;

    // 互换局封发时间
    @ExcelProperty("互换局封发时间")
    private Date chOrgDisTime;

    // 互换局发运时间
    @ExcelProperty("互换局发运时间")
    private Date chOrgSendTime;

    // 国内处理中心接收时间
    @ExcelProperty("国内处理中心接收时间")
    private Date processRecvTime;

    // 投递时间
    @ExcelProperty("投递时间")
    private Date deliverTime;




    /**
     * 发运-接收（天数）
     */
    @ExcelProperty("发运-接收（天数）")
    private Double dayShipmentToReceipt;

    /**
     * 开拆-封发（天数）
     */
    @ExcelProperty("开拆-封发（天数）")
    private Double dayOpenToSeal;


    /**
     * 封发-发运（天数）
     */
    @ExcelProperty("封发-发运（天数）")
    private Double daySealToShipment;

    /**
     * 发运-投递（天数）
     */
    @ExcelProperty("发运-投递（天数）")
    private Double dayShipmentToDelivery;

    /**
     * 全程时限（出仓-投递）逾限天数
     */
    @ExcelProperty("全程时限（出仓-投递）逾限天数")
    private Double totalDays;

    //超时预警(5天）
    @ExcelProperty("超时预警(5天）")
    private String status;
}
