package cn.com.xmmn.common.push.postal;

import cn.com.xmmn.common.push.postal.model.PostalResponse;
import cn.com.xmmn.common.push.postal.model.PostalTraceMessageBody;
import cn.com.xmmn.common.push.postal.model.PostalTraces;

import java.util.List;

/**
 * 推送新一代邮政
 */
public interface PostalPushService {

    /**
     * 总包扫描轨迹信息回传接口
     * @param postalTraces 小于30数量
     */
    public PostalResponse pushMailsTrack(PostalTraces postalTraces);
}
